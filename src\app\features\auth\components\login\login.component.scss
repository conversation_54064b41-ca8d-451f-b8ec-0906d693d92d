.login {
  padding: 55px 120px;
  // background-image: url(../../../../../assets/icons/bg-login.png);
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  min-width: fit-content;
  min-height: 100vh;
  max-height: 100%;
  .lang-btn {
    border-radius: 50px;
    color: #fff;
    font-size: 20px;
    font-weight: 400;
    line-height: 16px;
    height: 40px;
    padding: 0;
   // padding-bottom: 20px;

    &:focus {
      box-shadow: none;
      border-color: #fff;
    }

    .language-flag {
      width: 16px;
      height: 16px;
      object-fit: cover;
      border-radius: 2px;
    }

    .language-text {
      font-size: 16px;
      font-weight: 400;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
    svg {
    width: 10px;
    height: 10px;
    }
    .language-option {
      color: #00205a;
      font-size: 16px;
      font-weight: 400;
      font-style: normal;
      font-weight: 600;
      line-height: var(--Line-height-400, 22px);
    }
    img {
    //  padding-top: 7px;
    }
  }

  // Language dropdown styles for login
  .dropdown-toggle::after {
  display: none !important;
}

  .dropdown-menu {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 8px 0;
    min-width: 140px;

    .dropdown-item {
      padding: 8px 16px;
      font-size: 14px;
      color: #333;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
        color: #333;
      }

      &.active {
        background-color: #00205a;
        color: white;

        .language-flag svg path {
          opacity: 0.9;
        }
      }

      .language-flag {
        width: 16px;
        height: 16px;
        object-fit: cover;
        border-radius: 2px;
      }
    }
  }

  &:lang(ar) {
    background-image: url(../../../../../assets/icons/bg-login.png);
  }

  &:lang(en) {
    background-image: url(../../../../../assets/images/login-en-bg.jpg);
  }

  // Hide language button on mobile devices
  @media (max-width: 767px) {
    .lang-container {
      display: none;
    }

    .logo-container {
      justify-content: center !important;
    }
  }
  .login-container {
    padding: 40px 24px;

    .eye-icon {
      display: flex;
      justify-content: end;
      align-items: end;
      width: 100%;
      top: 50%;
      transform: translateY(-50%);
      width: fit-content;
      &:lang(ar) {
        left: 10px;
      }
      &:lang(en) {
        right: 10px;
      }

      img {
        // margin: -7px 10px;
        width: 24px;
        height: 24px;
      }
    }
     .login-btn{
      }

       .form-control {
            border-radius: 8px;
            border: 1px solid #BDBDBD;
            color: #1D1D1D !important;
            font-size: 16px;
            font-weight: 400;
            line-height: 22.4px;
            padding: 9px 12px 13px;
            height: 40px;

          }

          .form-control:focus {
  outline: none;       /* removes blue outline */
  box-shadow: none;    /* removes Bootstrap/Chrome shadow */
  border-color: inherit; /* keep your border color */
}

          .form-control:-webkit-autofill,
.form-control:-webkit-autofill:hover,
.form-control:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important; /* set bg */
  -webkit-text-fill-color: inherit !important; /* keep your text color */
  transition: background-color 5000s ease-in-out 0s; /* hack to stop yellow */
}

//           input {
//   all: unset; /* removes ALL browser styles */
// }
  }

  // Mobile responsive styles
  @media (max-width: 768px) {
    padding: 60px;
    background-position: center;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 1) 24.61%,
        rgba(0, 32, 90, 0.8) 100%
      ),
      url(../../../../../assets/images/bg-img.jpg) 52% 15% !important;

    .logo-container {
      align-items: center !important;
      text-align: center;
      gap: 16px;
      margin-bottom: 32px !important;

      .logo-img {
        height: 80px !important; // Smaller logo on mobile
        width: auto;
      }
    }

    .lang-btn {
      font-size: 16px;
      height: 36px;
      padding: 8px 16px;
      padding-bottom: 16px;
      min-width: 120px;
    }

    .login-container {
      padding: 60px 24px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      width: 100%;
      max-width: 600px;
      margin: 0 auto;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      margin-top: 36px;

      .margin-bottom-56 {
        margin-bottom: 24px !important;
        text-align: center;
      }

      // Make form elements full width on mobile
      .row {
        margin: 0;

        .col-md-12 {
          padding: 0;

          .login-password,
          .log-submit-btn {
            width: 100% !important;
          }

          .form-control {
            width: 100% !important;
            // padding: 12px 16px;
            font-size: 16px;
            border-radius: 8px;
            border: 1px solid #BDBDBD;
            color: #1D1D1D;
            font-size: 16px;
            font-weight: 400;
            line-height: 22.4px;

            &:focus {
              // border-color: #00205a;
              // box-shadow: 0 0 0 0.2rem rgba(0, 32, 90, 0.25);
            }
          }

          .form-label {
            margin-bottom: 8px;
          }
        }
      }

      .eye-icon {
        top: 50%;
        transform: translateY(-50%);

        img {
          margin: 0;
          width: 20px;
          height: 20px;
        }
      }

      // Center the copyright text
      .bold-400.font-size-xxs.dark-gray {
        text-align: center;
        margin-top: 16px;

        img {
          margin-right: 8px;
        }
      }
    }

    // Typography adjustments for mobile
    .font-size-xl {
      font-size: 24px !important;
      line-height: 1.3;
    }

    .font-size-sm {
      font-size: 20px !important;
      line-height: 1.4;
    }

    .font-size-m {
      font-size: 18px !important;
      font-weight: 700;
    }
  }

  // Extra small mobile devices
  @media (max-width: 480px) {
    padding: 60px 24px;

    .d-flex.justify-content-between img {
      height: 60px !important;
    }

    .login-container {
      padding: 20px;
      margin-top: 36px;

      .font-size-xl {
        font-size: 20px !important;
      }

      .margin-bottom-56 {
        margin-bottom: 16px !important;
      }

      .eye-icon img {
        width: 18px !important;
        height: 18px !important;
      }

      // Footer styling to match the screenshot
      // .bold-400.font-size-xxs.dark-gray {
      //   font-size: 10px !important;
      //   margin-top: 8px;

      //   img {
      //     width: 14px;
      //     height: 14px;
      //   }
      // }
    }
  }
}
