import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, Validators, FormArray, FormControl, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Shared Components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Core Interfaces and Enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// API Generated Types - Following resolution component patterns
import { CreateAssessmentQuestionDto, CreateAssessmentOptionDto, QuestionType } from '@core/api/api.generated';

export interface QuestionDialogData {
  question?: CreateAssessmentQuestionDto; // For editing existing question
  existingQuestions?: CreateAssessmentQuestionDto[]; // For validation
  isEdit: boolean;
  questionIndex?: number;
}

@Component({
  selector: 'app-question-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    FormBuilderComponent,
    CustomButtonComponent,
  ],
  templateUrl: './question-dialog.component.html',
  styleUrls: ['./question-dialog.component.scss']
})
export class QuestionDialogComponent implements OnInit {
  // Form properties
  formGroup!: FormGroup;
  formControls: IControlOption[] = [];
  isValidationFire = false;
  isFormSubmitted = false;

  // UI enums
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  questionType = QuestionType;

  // Options management
  optionsFormArray!: FormArray;
  minOptions = 2;
  maxOptions = 5;


  constructor(
    private formBuilder: FormBuilder,
    private dialogRef: MatDialogRef<QuestionDialogComponent>,
    private translateService: TranslateService,
    @Inject(MAT_DIALOG_DATA) public data: QuestionDialogData
  ) {}

  ngOnInit(): void {
    this.initializeFormControls();
    this.initForm();

    if (this.data.isEdit && this.data.question) {
      this.populateForm(this.data.question);
    }
  }

  private initializeFormControls(): void {
    this.formControls = [
      {
        type: InputType.TextAndNumber,
        formControlName: 'questionText',
        id: 'questionText',
        name: 'questionText',
        label: 'ASSESSMENTS.QUESTION_TEXT',
        placeholder: 'ASSESSMENTS.QUESTION_TEXT_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-12',
        maxLength: 500
      },
      {
        type: InputType.Radio,
        formControlName: 'questionType',
        id: 'questionType',
        name: 'questionType',
        label: 'ASSESSMENTS.QUESTION_TYPE',
        isRequired: true,
        class: 'col-md-12',
        options: [
          { name: 'ASSESSMENTS.SINGLE_CHOICE', id: QuestionType._1 },
          { name: 'ASSESSMENTS.MULTI_CHOICE', id: QuestionType._2 },
          { name: 'ASSESSMENTS.TEXT_ANSWER', id: QuestionType._3 }
        ]
      }
    ];
  }

  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      questionText: ['', [Validators.required, Validators.maxLength(500)]],
      questionType: [QuestionType._1, [Validators.required]],
      options: this.formBuilder.array([]),
    });

    this.optionsFormArray = this.formGroup.get('options') as FormArray;

    // Watch for question type changes
    this.formGroup.get('questionType')?.valueChanges.subscribe(value => {
      this.onQuestionTypeChange(value);
    });

    // Initialize with default single choice type
    this.onQuestionTypeChange(QuestionType._1);
  }

  private populateForm(question: CreateAssessmentQuestionDto): void {
    debugger
    this.formGroup.patchValue({
      questionText: question.questionText,
      questionType: question.type
    });

    // Clear existing options
    this.clearOptions();

    // Handle different question types
    if ((question.type === QuestionType._1 || question.type === QuestionType._2) && question.options) {
      // Single choice or multi choice
      question.options.forEach(option => {
        this.addOption(option.optionText || '',option.isOther);
      });
    } else if (question.type === QuestionType._3) {
      // Text answer - set expected answer
    }
  }

  private onQuestionTypeChange(questionType: QuestionType): void {
    debugger;
    if (questionType === QuestionType._1 || questionType === QuestionType._2) {
      // Single choice or multi choice - ensure minimum options
      if (this.optionsFormArray.length < this.minOptions) {
        while (this.optionsFormArray.length < this.minOptions) {
          this.addOption('');
        }
      }
    } else if (questionType === QuestionType._3) {
      // Text answer - clear options and set expected answer validation
      this.clearOptions();
    }
  }

  // Options management methods
  addOption(value: string = '',isOther:boolean = false): void {
    debugger;
    if (this.optionsFormArray.length < this.maxOptions) {
      const optionGroup = this.formBuilder.group({
        text: [value, [Validators.required]],
        isOther:isOther
      });
      this.optionsFormArray.push(optionGroup);
    }
  }

  removeOption(index: number): void {
    if (this.optionsFormArray.length > this.minOptions) {
      this.optionsFormArray.removeAt(index);
    }
  }

  clearOptions(): void {
    while (this.optionsFormArray.length > 0) {
      this.optionsFormArray.removeAt(0);
    }
  }

  // Ensure "Other" option exists for single choice questions
  private ensureOtherOptionExists(): void {
      this.optionsFormArray.value.forEach((option: any, index: number) => {
        if (option.isOther) {
          this.optionsFormArray.removeAt(index);
        }
      });
      this.addOption('ASSESSMENTS.OTHER',true);
  }
  private ensureTextOptionExists(): void {
    if (this.optionsFormArray.value.length <= 0) {
      this.addOption('ASSESSMENTS.APPROVE');
      this.addOption('ASSESSMENTS.REJECT');
    }
  }

  // Event handlers
  onSubmit(): void {
    this.isValidationFire = true;

    // Additional validation based on question type
    const questionType = this.formGroup.get('questionType')?.value;

    if (questionType === QuestionType._1 || questionType === QuestionType._2) {
      // Choice questions validation
      const validOptions = this.optionsFormArray.value.filter((option: any) => option.text?.trim() !== '');
      if (validOptions.length < this.minOptions) {
        console.error(`At least ${this.minOptions} options are required for choice questions`);
        return;
      }
       // For single choice questions, automatically add "Other" option if not already present
      if (questionType === QuestionType._1) {
        this.ensureOtherOptionExists();
      }

    } else if (questionType === QuestionType._3) {
     this.ensureTextOptionExists();
    }

    if (this.formGroup.valid && !this.isFormSubmitted) {
      this.isFormSubmitted = true;

      const questionType = this.formGroup.get('questionType')?.value || QuestionType._1;
      let options: CreateAssessmentOptionDto[] | undefined = undefined;

        // Choice questions - create options from form array
        options = this.optionsFormArray.value
          .filter((option: any) => option.text?.trim() !== '')
          .map((option: any, index: number) => new CreateAssessmentOptionDto({
            id: 0,
            optionText: option.text,
            order: index + 1,
            isSelected: false,
            isOther : option.isOther
          }));

      const questionData = new CreateAssessmentQuestionDto({
        id: 0,
        questionText: this.formGroup.get('questionText')?.value?.trim() || '',
        type: questionType,
        order: this.data.questionIndex !== undefined ? this.data.questionIndex + 1 : 1,
        isRequired: true,
        options: options,
        assessmentId:0,
        customText: undefined
      });

      this.dialogRef.close(questionData);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  // Utility methods
  isSingleChoiceType(): boolean {
    return this.formGroup?.get('questionType')?.value === QuestionType._1;
  }

  isMultiChoiceType(): boolean {
    return this.formGroup?.get('questionType')?.value === QuestionType._2;
  }

  isTextAnswerType(): boolean {
    return this.formGroup?.get('questionType')?.value === QuestionType._3;
  }

  isChoiceType(): boolean {
    return this.isSingleChoiceType() || this.isMultiChoiceType();
  }

  canAddOption(): boolean {
    return this.isChoiceType() && this.optionsFormArray.length < this.maxOptions;
  }

  canRemoveOption(_index: number): boolean {
    return this.isChoiceType() && this.optionsFormArray.length > this.minOptions;
  }

  getDialogTitle(): string {
    return this.data.isEdit ? 'ASSESSMENTS.EDIT_QUESTION' : 'ASSESSMENTS.ADD_QUESTION';
  }

  getSubmitButtonText(): string {
    return this.data.isEdit ? 'COMMON.UPDATE' : 'COMMON.ADD';
  }
  getOptions(options:any) : AbstractControl<any, any>[]
  {
    return options.filter((o: FormGroup) => !o.value.isOther);
  }
}
