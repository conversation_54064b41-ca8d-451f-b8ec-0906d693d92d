import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import {
  ColumnTypeEnum,
  DataHandlingType,
} from '../../../core/enums/column-type';
import {
  ActionDisplayMode,
  ITableColumn,
  SwitchToggleEvent,
  TableActionEvent,
  TextLinkClickEvent,
} from '../../../core/gl-interfaces/I-table/i-table';
import { CommonModule } from '@angular/common';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CdkTableModule } from '@angular/cdk/table';
import { SelectionModel } from '@angular/cdk/collections';
import {
  MatPaginator,
  MatPaginatorModule,
  PageEvent,
} from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-table',
  standalone: true,
  imports: [
    MatTableModule,
    CommonModule,
    MatSlideToggleModule,
    MatCheckboxModule,
    MatPaginatorModule,
    MatSortModule,
    MatMenuModule,
    MatButtonModule,
    MatIconModule,
    CdkTableModule,
    TranslateModule,
    FormsModule
  ],
  providers: [
  {
    provide: MatPaginatorIntl,
    useFactory: (translate: TranslateService) => {
      const intl = new MatPaginatorIntl();
      const lang = translate.currentLang;

      if (lang === 'ar') {
        intl.itemsPerPageLabel = ' عدد السجلات: ';

        intl.getRangeLabel = (page, pageSize, length) => {
          if (length === 0 || pageSize === 0) {
            return `0 من ${length}`;
          }
          const startIndex = page * pageSize;
          const endIndex = startIndex < length
            ? Math.min(startIndex + pageSize, length)
            : startIndex + pageSize;
          return `${startIndex + 1} - ${endIndex} من ${length}`;
        };

      } else {
        intl.itemsPerPageLabel = 'Items per page:';


        intl.getRangeLabel = (page, pageSize, length) => {
          if (length === 0 || pageSize === 0) {
            return `0 of ${length}`;
          }
          const startIndex = page * pageSize;
          const endIndex = startIndex < length
            ? Math.min(startIndex + pageSize, length)
            : startIndex + pageSize;
          return `${startIndex + 1} - ${endIndex} of ${length}`;
        };
      }

      intl.changes.next();
      return intl;
    },
    deps: [TranslateService]
  }
],
  templateUrl: './table.component.html',
  styleUrl: './table.component.scss',
})
export class TableComponent implements OnInit, AfterViewInit, OnChanges {
  columnTypeEnum = ColumnTypeEnum;
  ActionDisplayMode = ActionDisplayMode;
  @Input() columns: ITableColumn[] | undefined;
  @Input() displayedColumns: string[] | undefined;
  @Input() sortingType: DataHandlingType | undefined;
  @Input() paginationType: DataHandlingType | undefined;
  @Input() totalItems = 0;
  @Input() pageSize = 10;
  @Input() paginatorText = ''
  @Input() pageSizeOptions = [5, 10, 20, 50];
  @Input() alignLastColumnEnd: boolean = false;


  // Enhanced pagination properties
  currentPage = 0;
  totalPages = 0;

  @Input() selection = new SelectionModel<any>(true, []);
  @Input() dataSource: MatTableDataSource<any> | undefined;

  @Output() onClickAction = new EventEmitter<TableActionEvent>();
  @Output() switchToggleEvent = new EventEmitter<SwitchToggleEvent>();
  @Output() textLinkClick = new EventEmitter<TextLinkClickEvent>();
  @Output() toggleAllRows = new EventEmitter<void>();
  @Output() toggleRow = new EventEmitter<any>();

  @Output() sortChanged = new EventEmitter<{
    active: string;
    direction: string;
  }>();
  @Output() pageChange = new EventEmitter<PageEvent>();

  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @Input() customColumnTemplate!: TemplateRef<any>;
  DataHandlingType = DataHandlingType;

  constructor(private translateService:TranslateService){}

  ngOnInit(): void {
    this.updatePaginationInfo();
  }

  ngAfterViewInit(): void {
    this.initializeSorting();
    this.checkPaginationType();
    this.checkSortingType();
  }

  ngOnChanges(): void {
    this.updatePaginationInfo();
  }

  private updatePaginationInfo(): void {
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
  }
  checkSortingType(): void {
    if (this.dataSource)
      if (this.sortingType === DataHandlingType.Frontend) {
        this.dataSource.sort = this.sort; // Apply MatSort for frontend sorting
      } else {
        this.dataSource.sort = null; // Detach MatSort for backend sorting
      }
  }

  initializeSorting(): void {
    if (this.sortingType === DataHandlingType.Backend) {
      this.sort.sortChange.subscribe(({ active, direction }) => {
        this.sortChanged.emit({ active, direction }); // Emit backend sorting parameters
      });
    }
  }
  checkPaginationType(): void {
    if (this.dataSource)
      if (this.paginationType === DataHandlingType.Frontend) {
        this.dataSource.paginator = this.paginator; // Attach MatPaginator for frontend pagination
      } else {
        this.dataSource.paginator = null; // Detach paginator for backend pagination
      }
  }

  onPageChange(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.updatePaginationInfo();
    if (this.paginationType === DataHandlingType.Backend) {
      this.pageChange.emit(event);
    }
  }

  // Enhanced pagination methods
  onPageSizeChange(event: any): void {
    const newPageSize = parseInt(event.target.value, 10);
    this.pageSize = newPageSize;
    this.currentPage = 0; // Reset to first page
    this.updatePaginationInfo();

    const pageEvent: PageEvent = {
      pageIndex: this.currentPage,
      pageSize: this.pageSize,
      length: this.totalItems
    };

    this.onPageChange(pageEvent);
  }

  goToPage(pageIndex: number): void {
    if (pageIndex >= 0 && pageIndex < this.totalPages) {
      this.currentPage = pageIndex;

      const pageEvent: PageEvent = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        length: this.totalItems
      };

      this.onPageChange(pageEvent);
    }
  }

  getVisiblePages(): (number | string)[] {
    const pages: (number | string)[] = [];
    const totalPages = this.totalPages;
    const currentPage = this.currentPage + 1; // Convert to 1-based for display

    if (totalPages <= 7) {
      // Show all pages if 7 or fewer
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (currentPage <= 4) {
        // Show pages 2, 3, 4, 5, ..., last
        for (let i = 2; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // Show 1, ..., last-4, last-3, last-2, last-1, last
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Show 1, ..., current-1, current, current+1, ..., last
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  }

  isAllSelected(): boolean {
    if (!this.dataSource) return false;
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return this.isAllSelected() ? 'deselect all' : 'select all';
    }
    return this.selection.isSelected(row)
      ? `deselect row ${row.position + 1}`
      : `select row ${row.position + 1}`;
  }

  onToggleAllRows(): void {
    this.toggleAllRows.emit();
  }

  onToggleRow(row: any): void {
    this.toggleRow.emit(row);
  }

  handleAction(action: string, row: any): void {
    this.onClickAction.emit({ action, row });
  }

  onToggle(row: any, newValue: boolean): void {
    this.switchToggleEvent.emit({ row, newValue });
  }

  onTextLinkClick(row: any, columnDef: string): void {
    this.textLinkClick.emit({ row, columnDef });
  }
}
