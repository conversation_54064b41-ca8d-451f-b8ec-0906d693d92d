@import "../../../../../assets/scss/variables.scss";

.create-resolution-page {
  // Align with fund components styling
  .form-container {
    padding: 2rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 0.5px solid #dce0e3;

    // Alert messages styling
    .alert {
      margin-bottom: 1.5rem;
      border-radius: 8px;
      border: none;
      padding: 1rem 1.25rem;
      font-size: 14px;
      line-height: 1.5;

      &.alert-danger {
        background-color: #fef2f2;
        color: #dc2626;
        border-left: 4px solid #dc2626;

        i {
          color: #dc2626;
        }
      }

      &.alert-success {
        background-color: #f0fdf4;
        color: #16a34a;
        border-left: 4px solid #16a34a;

        i {
          color: #16a34a;
        }
      }

      .btn-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        opacity: 0.6;
        cursor: pointer;

        &:hover {
          opacity: 1;
        }
      }
    }
    .header {
      color:  #00205a;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }
    .hr-first{
      margin: 24px 0 5px;
      border: 1px solid #E5E7EB;
    }
    .hr-last{
      border: 1px solid #E5E7EB;
      margin: 32px 0 16px;
    }
    .form-group {
      margin-bottom: 1.5rem;

      &.col-12 {
        width: 100%;
      }

      &.col-md-6 {
        width: 48%;
        margin-inline-end: 2%;
        &:nth-child(2n) {
          margin-inline-end: 0;
        }
      }
    }
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }

  // RTL support
  [dir="rtl"] & {
    .actions {
      justify-content: flex-start;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .form-container {
      padding: 1rem;
    }

    .actions {
      flex-direction: column;
      gap: 0.5rem;

      ::ng-deep app-custom-button {
        width: 100%;
      }
    }
  }

  @media (max-width: 480px) {
    .form-container {
      padding: 0.75rem;
    }
  }
}

.form-section {
margin-bottom: 40px;

&:last-child {
    margin-bottom: 0;
}

.section-header {
    font-size: 16px;
    font-weight: 600;
    color: #00205a;
    margin-bottom: 20px;
    margin-top: 20px;  
    padding-bottom: 10px;
    // border-bottom: 2px solid #e0e0e0;

    h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #00205a;

    }
    .items-num{
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 14px;
    background: rgba(38, 86, 135, 0.12);
    color: #000;
    font-size: 16px;
    font-weight: 400;
    line-height: 18px;
    }
}

// Status display styling removed - now handled by form-builder component

// Form builder header styling
.header {
    font-size: 16px;
    font-weight: 600;
    color: #00205a;
    margin-bottom: 10px;
}

.hr-first{
    margin: 24px 0 15px;
    border: 1px solid #b6b6b6;
}
.hr-last{
    border: 1px solid #b6b6b6;
    margin: 32px 0 16px;
}

.section-divider {
    border: none;
    height: 1px;
    background: linear-gradient(to right, #e0e0e0, transparent);
    margin: 20px 0;
}



.empty-items {
    text-align: center;
    padding: 60px 20px;
    background: #fafbfc;
    border: 2px dashed #e0e0e0;
    border-radius: 12px;
    margin-bottom: 20px;

    .empty-icon {
    margin-bottom: 20px;

    i {
        font-size: 64px;
        color: #d0d7de;
    }
    }

    .empty-message {
    font-size: 16px;
    color: #656d76;
    margin-bottom: 24px;
    font-weight: 500;
    }

    .add-first-item-btn {
    background: #007bff;
    border-color: #007bff;
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;

    &:hover {
        background: #0056b3;
        border-color: #004085;
        transform: translateY(-1px);
    }
    }
}

// Attachments Styles
.section-header {
    .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .attachments-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;

        .attachments-count {
        color: #666;

        strong {
            color: #007bff;
        }
        }

        .remaining-count {
        color: #28a745;
        font-size: 12px;
        }
    }
    }
}

.attachments-container {
    margin-bottom: 20px;

    .attachment-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
    }

    .attachment-icon {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        background: #fff5f5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #dc3545;
        font-size: 20px;
    }

    .attachment-info {
        flex: 1;

        .attachment-name {
        font-size: 15px;
        font-weight: 600;
        color: #00205a;
        margin-bottom: 6px;
        word-break: break-word;
        line-height: 1.4;
        }

        .attachment-meta {
        display: flex;
        align-items: center;
        gap: 16px;
        font-size: 13px;
        color: #666;

        .file-size {
            font-weight: 500;
            color: #007bff;
        }

        .upload-date {
            color: #999;
        }
        }
    }

    .attachment-actions {
        display: flex;
        gap: 8px;

        .btn {
        padding: 6px 10px;
        font-size: 12px;
        border-radius: 6px;
        transition: all 0.2s ease;

        &.download-btn {
            border-color: #007bff;
            color: #007bff;

            &:hover {
            background: #007bff;
            color: white;
            }
        }

        &.delete-btn {
            border-color: #dc3545;
            color: #dc3545;

            &:hover {
            background: #dc3545;
            color: white;
            }
        }
        }
    }
    }
}

.empty-attachments {
    text-align: center;
    padding: 50px 20px;
    background: #fafbfc;
    border: 2px dashed #e0e0e0;
    border-radius: 12px;
    margin-bottom: 20px;

    .empty-icon {
    margin-bottom: 16px;

    i {
        font-size: 56px;
        color: #d0d7de;
    }
    }

    .empty-message {
    font-size: 15px;
    color: #656d76;
    margin: 0;
    font-weight: 500;
    }
}

.upload-section {
    text-align: center;
    padding: 20px;
    border: 2px dashed #007bff;
    border-radius: 8px;
    background: #f8f9ff;

    .upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-color: #007bff;
    color: #007bff;
    background: white;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
        background: #007bff;
        color: white;
    }
    }

    .upload-info {
    margin-top: 10px;

        small {
        font-size: 11px;
        color: #666;
        }
    }
    }

    .max-reached-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff3cd;
    color: #856404;
    border-radius: 6px;
    font-size: 14px;

    i {
        font-size: 16px;
    }
    }
}

// .members-section {
//   // padding: 1rem;
//   // max-width: 1200px;
//   // margin: 0 auto;

//   background-color: $card-background;
//   border: 1px solid $border-color;
//   border-radius: 8px;
//   padding: 16px;
//   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

//   .info-item {
//     .info-label {
//       color: $text-grey;
//       font-size: 14px;
//       font-weight: 700;
//       line-height: 20px;
//     }
//     .description-text {
//       max-width: 100%; // or set a fixed width like 250px
//       white-space: nowrap;
//       overflow: hidden;
//       text-overflow: ellipsis;
//       display: block;
//     }
//     .info-value {
//       color: $navy-blue;
//       font-size: 16px;
//       font-weight: 500;
//       line-height: 16px;
//     }
//   }
// }
.meeting-attendees{
  .title {
    color: $navy-blue;
    font-size: 18px;
    font-weight: 700;
    // margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .members-section {
      padding: 18px 0px;
      height: fit-content;
  
  
    .members-list {
      display: flex;
      margin: 12px 0px;
    }
  
    .member-card {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 8px;
      border: 1px solid rgb(226, 232, 240);
      background-color: #fff;
      transition: all 0.2s ease;
      
      &.selected-card {
        background-color: rgb(208 220 238 / 40%);
        border: 1px solid rgb(27, 54, 93);
      }

      .member-info {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
  
        .member-avatar {
          width: 45px;
          height: 45px;
          border-radius: 50%;
          overflow: hidden;
          background: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
  
          .avatar-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
  
        .member-details {
          .member-name {
            font-size: 16px;
            font-weight: 500;
            color: $navy-blue;
            margin-bottom: 4px;
            line-height: 22px;
  
            &:hover {
              text-decoration: underline;
              cursor: pointer;
            }
          }
  
          .member-role {
            font-size: 14px;
            color: #666;
            margin: 0;
            line-height: 1.2;
          }
        }

        .has-conflict{
          border-radius: 8px;
          border: 1px solid #FFC800;
          background: #FFFAEB;
          color: #B68107;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 18px;
          padding: 2px 10px;
        }
      }
    }

  }  
}

.items-container {
  margin-bottom: 20px;

  .resolution-item-card {
    padding: 16px 10px;
    border-radius: 8px;
    background-color: white;
    margin-bottom: 16px;
    // background: white;
    // border: 1px solid #e0e0e0;
    // border-radius: 12px;
    // margin-bottom: 16px;
    // overflow: hidden;
    // transition: all 0.2s ease;
    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    // &:hover {
    //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    //   transform: translateY(-1px);
    // }

    .item-header {
      // background: #f8f9fa;
      // padding: 16px 20px;
      // border-bottom: 1px solid #e0e0e0;

      .item-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .item-number {
          font-size: 20px;
          font-weight: 500;
          line-height: 32px;
          color: #00205a;
          // background: #e3f2fd;
          // padding: 4px 12px;
          // border-radius: 16px;
        }

        .item-actions {
          display: flex;
          gap: 8px;

          .btn {
            padding: 8px;
            // font-size: 12px;
            // border-radius: 6px;
            &:hover{
              background: unset;
              border: unset;
              color: unset;
            }
            &.view-conflict-btn {
              border-radius: 8px;
              border: 1px solid #FFC800;
              background: #FFFAEB;
              color: #B68107;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 18px;
            }
          }
        }
      }

      .item-conflict {
        display: flex;
        align-items: center;
        justify-content: center;

        .conflict-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #856404;
          font-size: 13px;
          font-weight: 500;
          margin-bottom: 8px;

          i {
            color: #ffc107;
          }
        }
      }
    }

    .item-body {
      margin-top: 15px;

      .item-title {
        font-size: 16px;
        font-weight: 600;
        color: #00205a;
        margin-bottom: 8px;
      }

      .item-description {
            color: #333;
            line-height: 21px;
            font-size: 14px;
            font-weight: 400;
      }
    }
  }
}
