import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Saudi IBAN validator
 * Format: SA followed by 32 digits (total 34 characters)
 * Example: **********************************
 */
export function saudiIbanValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null; // Don't validate empty values, use required validator for that
    }

    // Remove spaces and convert to uppercase
    const cleanValue = value.replace(/\s/g, '').toUpperCase();

    // Check if it starts with SA and has exactly 34 characters
    const saudiIbanPattern = /^SA\d{32}$/;

    if (!saudiIbanPattern.test(cleanValue)) {
      return {
        saudiIban: {
          message: 'IBAN must be in Saudi format (SA followed by 32 digits)',
          actualValue: value,
          expectedFormat: '**********************************'
        }
      };
    }

    return null;
  };
}

/**
 * Saudi mobile number validator
 * Format: Saudi mobile numbers starting with 05 or 5, followed by specific second digits
 * Regex: /^(05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/
 * Examples: 05012345678, 5012345678, 05512345678
 */
export function saudiMobileValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    // Remove spaces, dashes, and plus signs
    const cleanValue = value.replace(/[\s\-\+]/g, '');

    // Saudi mobile regex pattern: (05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})
    const saudiMobileRegex = /^(05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/;

    // Check different formats
    let isValid = false;
    let normalizedNumber = '';

    // Format 1: Direct match with Saudi regex
    if (saudiMobileRegex.test(cleanValue)) {
      isValid = true;
      normalizedNumber = cleanValue.startsWith('05') ? cleanValue : '0' + cleanValue;
    }
    // Format 2: With country code 966
    else if (cleanValue.startsWith('966') && cleanValue.length === 12) {
      const numberWithoutCountryCode = cleanValue.substring(3);
      if (saudiMobileRegex.test('0' + numberWithoutCountryCode)) {
        isValid = true;
        normalizedNumber = '0' + numberWithoutCountryCode;
      }
    }

    if (!isValid) {
      return {
        saudiMobile: {
          message: 'Mobile number must be a valid Saudi number (05X or 5X where X is 5,0,3,6,4,9,1,8,7 followed by 7 digits)',
          actualValue: value,
          expectedFormat: '05XXXXXXX or 5XXXXXXX'
        }
      };
    }

    return null;
  };
}

/**
 * Saudi passport number validator
 * Format: 1 letter followed by 19 digits
 * Examples: A0000000012345678910, B87654321
 */
export function saudiPassportValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    // Remove spaces and convert to uppercase
    const cleanValue = value.replace(/\s/g, '').toUpperCase();

    // Saudi passport pattern: 1 letter followed by 8 digits
    const saudiPassportPattern = /^[A-Z]\d{19}$/;

    if (!saudiPassportPattern.test(cleanValue)) {
      return {
        saudiPassport: {
          message: 'Passport number must be 1 letter followed by 19 digits',
          actualValue: value,
          expectedFormat: 'A0000000012345678910'
        }
      };
    }

    return null;
  };
}

/**
 * Saudi national ID validator
 * Format: 10 digits
 * Examples: 1234567890
 */
export function saudiNationalIdValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    // Remove spaces
    const cleanValue = value.replace(/\s/g, '');

    // Saudi national ID pattern: exactly 10 digits
    const saudiNationalIdPattern = /^\d{10}$/;

    if (!saudiNationalIdPattern.test(cleanValue)) {
      return {
        saudiNationalId: {
          message: 'National ID must be exactly 10 digits',
          actualValue: value,
          expectedFormat: '1234567890'
        }
      };
    }

    return null;
  };
}

/**
 * Helper function to format IBAN with spaces for display
 */
export function formatIban(iban: string): string {
  if (!iban) return '';

  const cleanIban = iban.replace(/\s/g, '').toUpperCase();

  // Add space every 4 characters
  return cleanIban.replace(/(.{4})/g, '$1 ').trim();
}

/**
 * Helper function to format mobile number for display
 */
export function formatMobileNumber(mobile: string): string {
  if (!mobile) return '';

  const cleanMobile = mobile.replace(/[\s\-\+]/g, '');

  // Format as 05X XXX XXXX (for 10-digit numbers starting with 05)
  if (cleanMobile.length === 10 && cleanMobile.startsWith('05')) {
    return `${cleanMobile.substring(0, 3)} ${cleanMobile.substring(3, 6)} ${cleanMobile.substring(6)}`;
  }
  // Format as 5X XXX XXXX (for 9-digit numbers starting with 5)
  else if (cleanMobile.length === 9 && cleanMobile.startsWith('5')) {
    return `${cleanMobile.substring(0, 2)} ${cleanMobile.substring(2, 5)} ${cleanMobile.substring(5)}`;
  }

  return mobile;
}

/**
 * Helper function to format passport number for display
 */
export function formatPassportNumber(passport: string): string {
  if (!passport) return '';

  const cleanPassport = passport.replace(/\s/g, '').toUpperCase();

  // Format as A 1234 5678
  if (cleanPassport.length === 9 && /^[A-Z]\d{8}$/.test(cleanPassport)) {
    return `${cleanPassport.substring(0, 1)} ${cleanPassport.substring(1, 5)} ${cleanPassport.substring(5)}`;
  }

  return passport;
}
