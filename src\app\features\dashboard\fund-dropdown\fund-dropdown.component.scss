// Fund Dropdown Component Styles
// Matching dashboard design system with RTL support


::ng-deep .ng-select.ng-select-single .ng-select-container,
.ng-select.ng-select-opened.ng-select-top > .ng-select-container{
  border: 1px solid transparent;
  background-color: transparent;
}

.fund-dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
  max-width: 300px;

  // Dropdown Trigger
  .dropdown-trigger {
    background: transparent;
    border: 1px solid transparent;
    padding: 8px 12px;
    cursor: pointer;
    outline: none;
    width: 100%;
    transition: all 0.2s ease;
    border-radius: 6px;
    min-height: 40px;
    display: flex;
    align-items: center;

    &:hover:not(.fund-dropdown--disabled &) {
      background: #f8f9fa;
      border-color: #dee2e6;
    }

    &:focus {
      outline: 2px solid #0d6efd;
      outline-offset: 2px;
      border-radius: 4px;
    }

    .selected-fund {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      min-height: 32px;

      .fund-name {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        text-align: right;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.placeholder {
          color: #6c757d;
          font-style: italic;
        }
      }

      .loading-spinner {
        display: flex;
        align-items: center;

        .spinner-border-sm {
          width: 1rem;
          height: 1rem;
          border-width: 0.1em;
        }
      }

      .dropdown-arrow {
        display: flex;
        align-items: center;
        color: #6c757d;
        transition: transform 0.2s ease;

        i {
          font-size: 0.8rem;
        }

        &.rotated {
          transform: rotate(180deg);
        }
      }
    }
  }

  // Dropdown Menu
  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1050;
    margin-top: 4px;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow: hidden;
    animation: dropdownFadeIn 0.2s ease;

    .dropdown-content {
      max-height: 300px;
      overflow-y: auto;
      padding: 4px 0;

      // Custom scrollbar
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f8f9fa;
      }

      &::-webkit-scrollbar-thumb {
        background: #dee2e6;
        border-radius: 3px;

        &:hover {
          background: #adb5bd;
        }
      }
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 1px solid #f8f9fa;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f8f9fa;
      }

      &.selected {
        background: #e3f2fd;
        border-left: 3px solid #0d6efd;
        padding-left: 13px;
      }

      .fund-info {
        flex: 1;
        text-align: right;

        .fund-name {
          font-size: 0.9rem;
          font-weight: 500;
          color: #2c3e50;
          margin-bottom: 2px;
          line-height: 1.3;
        }

        .fund-id {
          font-size: 0.75rem;
          color: #6c757d;
        }
      }

      .selected-indicator {
        margin-left: 8px;

        i {
          font-size: 0.9rem;
        }
      }
    }

    .dropdown-empty {
      padding: 20px;
      text-align: center;

      .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;

        i {
          font-size: 1.5rem;
          opacity: 0.5;
        }

        .empty-text {
          font-size: 0.85rem;
          color: #6c757d;
        }
      }
    }
  }

  // Dropdown Backdrop (for mobile)
  .dropdown-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1040;
    background: transparent;
  }

  // State Modifiers
  &.fund-dropdown--loading {
    .dropdown-trigger {
      cursor: wait;
    }
  }

  &.fund-dropdown--disabled {
    .dropdown-trigger {
      cursor: not-allowed;
      opacity: 0.6;

      .selected-fund .fund-name {
        color: #6c757d;
      }
    }
  }

  &.fund-dropdown--empty {
    .dropdown-trigger {
      cursor: default;
    }
  }
}

// Animations
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .fund-dropdown {
    max-width: 100%;

    .dropdown-menu {
      left: -8px;
      right: -8px;
      margin-top: 8px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);

      .dropdown-item {
        padding: 16px 20px;

        .fund-info .fund-name {
          font-size: 1rem;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .fund-dropdown {
    .dropdown-trigger {
      .selected-fund {
        .fund-name {
          text-align: left;
        }
      }
    }

    .dropdown-menu {
      .dropdown-item {
        &.selected {
          border-left: none;
          border-right: 3px solid #0d6efd;
          padding-left: 16px;
          padding-right: 13px;
        }

        .fund-info {
          text-align: left;
        }

        .selected-indicator {
          margin-left: 0;
          margin-right: 8px;
        }
      }
    }
  }
}

// High Contrast Mode
@media (prefers-contrast: high) {
  .fund-dropdown {
    .dropdown-menu {
      border: 2px solid #000000;

      .dropdown-item {
        border-bottom: 1px solid #000000;

        &:hover {
          background: #000000;
          color: #ffffff;
        }

        &.selected {
          background: #0000ff;
          color: #ffffff;
        }
      }
    }
  }
}

// Print Styles
@media print {
  .fund-dropdown {
    .dropdown-menu {
      display: none !important;
    }

    .dropdown-trigger {
      .dropdown-arrow {
        display: none;
      }
    }
  }
}
