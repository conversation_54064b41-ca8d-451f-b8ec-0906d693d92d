<!-- Change Password Section -->
<div class="change-password-section">
  <div class="section-header mb-4">
    <h4 class="section-title">{{ 'USER_PROFILE.CHANGE_PASSWORD' | translate }}</h4>
  </div>

    <!-- Form Fields -->

    <div class="form-fields-section">
     <div class="d-flex align-items-center justify-content-center w-100 m-auto">
      <app-form-builder
        [formControls]="formControls"
        [formGroup]="changePasswordForm"
        [isFormSubmitted]="isFormSubmitted">


      </app-form-builder>
</div>

    <!-- Action Buttons -->

    <div class="my-4 d-flex justify-content-center align-items-center w-100">
       <app-custom-button [btnName]="'COMMON.CANCEL' | translate" (click)="cancel()" class="cancel-btn"
        [buttonType]="buttonEnum.Secondary" [iconName]="IconEnum.cancel">
      </app-custom-button>
      <app-custom-button  type="submit" class="change-password-btn"  [iconName]="" [btnName]="'LOGIN_PAGE.CHANGE_PASSWORD' | translate" (click)="onSubmit()" [disabled]="isLoading"></app-custom-button>

    </div>

  </div>
</div>
