import {
  Component,
  forwardRef,
  Input,
  Output,
  EventEmitter,
  NO_ERRORS_SCHEMA,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { LabelPositionEnum } from '@shared/enum/label-position-enum';
import { SizeEnum } from '@core/enums/size';
import { AppearanceEnum } from '@shared/enum/appearance-enum';
import { InputStyleModel } from '@shared/gl-models/inputs/input-style.model';
import { TranslateModule } from '@ngx-translate/core';
import { InputType } from '@shared/enum/input-type.enum';

@Component({
  selector: 'arabdt-reusable-rf-text-input',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, TranslateModule],
  schemas: [NO_ERRORS_SCHEMA],
  templateUrl: './reusable-rf-text-input.component.html',
  styleUrls: ['./reusable-rf-text-input.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ReusableRfTextInputComponent),
      multi: true,
    },
  ],
})
export class ReusableRfTextInputComponent implements ControlValueAccessor {
  writeValue(value: string): void {
    this.value = value;
    this.onChange(this.value);
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled: boolean): void {
    this.readonly = isDisabled;
  }
  onChange: any = () => {};
  onTouched: any = () => {};

  @Input() appearance: AppearanceEnum | undefined;
  @Input() labelPosition: LabelPositionEnum | undefined;
  @Input() controlSize: SizeEnum | undefined;

  @Input() readonly: boolean | undefined;
  @Input() isDisabled!: boolean;
  @Input() placeholder: string | undefined;
  @Input() id: string | undefined;
  @Input() label: string | undefined;
  @Input() autoFocus: boolean | undefined;
  @Input() value: string | undefined;
  @Input() isInvalid: boolean | undefined;
  @Input() isRequired: boolean | undefined;
  @Input() minLength: number | undefined;
  @Input() maxLength: number | undefined;
  @Input() tooltipText!: string | undefined ;



  @Input() type: string = 'text';
  @Input() step?: string | number;
  @Input() min?: number = 0;
  @Input() max?: number;
  @Output() focusEvent: EventEmitter<Event> = new EventEmitter<Event>();
  @Output() blurEvent: EventEmitter<Event> = new EventEmitter<Event>();
  @Output() inputChangeEvent: EventEmitter<Event> = new EventEmitter<Event>();
  @Output() keyDownEvent: EventEmitter<KeyboardEvent> =
    new EventEmitter<KeyboardEvent>();

  labelPositionEnum = LabelPositionEnum;
  appearanceEnum = AppearanceEnum;
  sizeEnum = SizeEnum;

  onInputChange(event: Event): void {
    this.inputChangeEvent.emit(event);
    this.onChange(this.value);
  }

  onBlur(event: Event): void {
    this.blurEvent.emit(event);
    this.onTouched();
  }

  onFocus(event: Event): void {
    this.focusEvent.emit(event);
  }

  onKeyDown(event: KeyboardEvent): void {
    this.keyDownEvent.emit(event);
  }

  getFormControlClasses() {
    return {
      ['is-invalid']: this.isInvalid,
      ['read-only']: this.readonly,
      [InputStyleModel.OUTLINE]: this.appearance === AppearanceEnum.Outline,
      [InputStyleModel.FILLED_DARKER]:
        this.appearance === AppearanceEnum.FilledDarker,
      [InputStyleModel.FILL_LIGHTER]:
        this.appearance === AppearanceEnum.FillLighter,
      [InputStyleModel.UNDERLINE]: this.appearance === AppearanceEnum.Underline,
      [InputStyleModel.SMALL]: this.controlSize === SizeEnum.Small,
      [InputStyleModel.LARGE]: this.controlSize === SizeEnum.Large,
    };
  }
  restrictNumericInput(event: KeyboardEvent, control: any): void {
    if (control.type === InputType.Number) {
      const allowedKeys = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab'];
      const isNumber = /^[0-9]$/.test(event.key);

      if (!isNumber && !allowedKeys.includes(event.key)) {
        event.preventDefault();
      }
    }

    if (control.type === InputType.NumberDecimal) {
      const allowedKeys = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab', '.'];
      const isValid = /^[0-9.]$/.test(event.key);

      if (
        !isValid ||
        (event.key === '.' &&
          (event.target as HTMLInputElement).value.includes('.'))
      ) {
        event.preventDefault();
      }
    }
  }
}
