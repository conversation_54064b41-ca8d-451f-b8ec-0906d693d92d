# Assessment Details Component Enhancements

## 🎯 Overview

This document outlines the enhancements made to the `assessment-details` component to add approval/rejection functionality and collapsible questions section, following established JadwaUI patterns.

## ✨ New Features Implemented

### 1. Actions Section with Approve/Reject Buttons

#### **Location**: `src/app/features/assessments/components/assessment-details/assessment-details.component.html`

**Added approve and reject buttons with the following features:**
- **Conditional Visibility**: Only shown when user has appropriate permissions
- **Role-based Access**: Legal Council and Board Secretary can approve/reject
- **Permission Checking**: Validates specific permissions before showing buttons
- **Loading States**: Buttons are disabled during processing
- **Proper Styling**: Follows JadwaUI button patterns

```html
<!-- Approve Button -->
<app-custom-button 
  *ngIf="shouldShowApproveButton()" 
  [btnName]="'ASSESSMENTS.APPROVE' | translate" 
  (click)="approveAssessment()" 
  class="mx-2" 
  [buttonType]="buttonEnum.Primary" 
  [iconName]="IconEnum.check"
  [disabled]="isApproving">
</app-custom-button>

<!-- Reject Button -->
<app-custom-button 
  *ngIf="shouldShowRejectButton()" 
  [btnName]="'ASSESSMENTS.REJECT' | translate" 
  (click)="rejectAssessment()" 
  class="mx-2" 
  [buttonType]="buttonEnum.Danger" 
  [iconName]="IconEnum.cancel"
  [disabled]="isRejecting">
</app-custom-button>
```

### 2. Collapsible Questions Section

#### **Location**: `src/app/features/assessments/components/assessment-details/assessment-details.component.html`

**Enhanced questions display with:**
- **Expandable/Collapsible Interface**: Similar to resolution-details pattern
- **Toggle Button**: Accordion-style expand/collapse control
- **Smooth Animations**: CSS transitions for expand/collapse
- **Consistent Styling**: Matches existing JadwaUI patterns

```html
<div class="assessment-questions-header">
  <p class="section-title navy-color">
    {{ 'ASSESSMENTS.ASSESSMENT_QUESTIONS' | translate }}
    <span class="questions-count">({{ assessment.questions.length }} {{ 'ASSESSMENTS.QUESTIONS' | translate }})</span>
  </p>
  <div class="header-actions">
    <button class="expand-button" (click)="toggleQuestionsExpand()">
      <img [src]="isQuestionsExpanded ? 'assets/images/accrdion_up.png' : 'assets/images/accrdion_down.png'" 
           alt="expand" style="width: 14px; height: 8px;" />
    </button>
  </div>
</div>
<hr *ngIf="isQuestionsExpanded" style="margin-bottom: 16px;">

<div class="questions-list" [class.expanded]="isQuestionsExpanded" *ngIf="isQuestionsExpanded">
  <!-- Questions content -->
</div>
```

## 🔧 Component Logic Updates

### **Location**: `src/app/features/assessments/components/assessment-details/assessment-details.component.ts`

### New Properties Added

```typescript
// Permission and role-based properties
canApprove = false;
canReject = false;
userRole = '';

// UI state properties
isQuestionsExpanded = true;
isApproving = false;
isRejecting = false;
currentFundId = 0;
```

### Role-based Access Control

```typescript
private initializeRoleBasedAccess(): void {
  // Determine user role and permissions based on assessment approval workflow
  if (this.tokenService.hasRole('legalcouncil') || this.tokenService.hasRole('boardsecretary')) {
    this.userRole = 'legalcouncil';
    this.canApprove = this.tokenService.hasPermission('Assessment.Approve');
    this.canReject = this.tokenService.hasPermission('Assessment.Reject');
  } else if (this.tokenService.hasRole('fundmanager')) {
    this.userRole = 'fundmanager';
    this.canApprove = false; // Fund managers cannot approve their own assessments
    this.canReject = false;
  } else {
    this.userRole = 'default';
    this.canApprove = false;
    this.canReject = false;
  }
}
```

### Permission Checking Methods

```typescript
shouldShowApproveButton(): boolean {
  return this.canApprove && 
         this.assessment !== null &&
         this.tokenService.hasPermission('Assessment.Approve');
}

shouldShowRejectButton(): boolean {
  return this.canReject && 
         this.assessment !== null &&
         this.tokenService.hasPermission('Assessment.Reject');
}
```

### UI Control Methods

```typescript
toggleQuestionsExpand(): void {
  this.isQuestionsExpanded = !this.isQuestionsExpanded;
}
```

### Action Handlers

```typescript
approveAssessment(): void {
  if (!this.assessment || this.isApproving) return;
  
  this.isApproving = true;
  
  // TODO: Replace with actual NSwag-generated approve method when available
  // Placeholder implementation for now
  setTimeout(() => {
    this.isApproving = false;
    this.errorModalService.showSuccess(
      this.translateService.instant('ASSESSMENTS.APPROVE_SUCCESS')
    );
    this.cancel();
  }, 1000);
}

rejectAssessment(): void {
  if (!this.assessment || this.isRejecting) return;
  
  this.isRejecting = true;
  
  // TODO: Replace with actual NSwag-generated reject method when available
  // Placeholder implementation for now
  setTimeout(() => {
    this.isRejecting = false;
    this.errorModalService.showSuccess(
      this.translateService.instant('ASSESSMENTS.REJECT_SUCCESS')
    );
    this.cancel();
  }, 1000);
}
```

## 🎨 Styling Enhancements

### **Location**: `src/app/features/assessments/components/assessment-details/assessment-details.component.scss`

**Added collapsible animation styles:**

```scss
// Assessment Questions Styles
.questions-list {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;

  &.expanded {
    max-height: 2000px;
  }
}
```

## 🌐 Internationalization

### **Added Translation Keys**

#### English (`src/assets/i18n/en.json`)
```json
"APPROVE": "Approve",
"REJECT": "Reject",
"APPROVE_SUCCESS": "Assessment approved successfully",
"REJECT_SUCCESS": "Assessment rejected successfully",
"APPROVE_FAILED": "Failed to approve assessment",
"REJECT_FAILED": "Failed to reject assessment"
```

#### Arabic (`src/assets/i18n/ar.json`)
```json
"APPROVE": "موافقة",
"REJECT": "رفض",
"APPROVE_SUCCESS": "تم اعتماد التقييم بنجاح",
"REJECT_SUCCESS": "تم رفض التقييم بنجاح",
"APPROVE_FAILED": "فشل في اعتماد التقييم",
"REJECT_FAILED": "فشل في رفض التقييم"
```

## 🔗 Dependencies Added

```typescript
import { TokenService } from '@features/auth/services/token.service';
```

## 📋 Next Steps for Full Implementation

### 1. Backend API Integration
- **Approve Assessment Endpoint**: `POST /api/Assessment/Approve/{id}`
- **Reject Assessment Endpoint**: `POST /api/Assessment/Reject/{id}`
- **Update AssessmentByIdDto**: Add status property for better permission checking

### 2. NSwag Regeneration
```bash
npm run nswag
```

### 3. Replace Placeholder Methods
Update the approve/reject methods to use actual NSwag-generated service calls:

```typescript
approveAssessment(): void {
  if (!this.assessment || this.isApproving) return;
  
  this.isApproving = true;
  
  this.assessmentServiceProxy.approve(this.currentAssessmentId)
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response) => {
        this.isApproving = false;
        if (response.successed) {
          this.errorModalService.showSuccess('ASSESSMENTS.APPROVE_SUCCESS');
          this.cancel();
        } else {
          this.errorModalService.showError(response.message || 'ASSESSMENTS.APPROVE_FAILED');
        }
      },
      error: (error) => {
        this.isApproving = false;
        this.errorModalService.showError('ASSESSMENTS.APPROVE_FAILED');
      }
    });
}
```

## ✅ Testing Checklist

- [ ] Approve button shows only for Legal Council/Board Secretary
- [ ] Reject button shows only for Legal Council/Board Secretary  
- [ ] Buttons are disabled during processing
- [ ] Questions section expands/collapses correctly
- [ ] Smooth animations work properly
- [ ] Translation keys display correctly in both languages
- [ ] Navigation maintains fund context
- [ ] Permission checking works correctly
- [ ] Loading states function properly

## 🎯 Compliance with JadwaUI Standards

✅ **Button Styling**: Uses established `app-custom-button` component patterns
✅ **Permission Checking**: Follows TokenService role and permission patterns
✅ **Collapsible UI**: Matches resolution-details component patterns
✅ **Translation Support**: Proper i18n implementation
✅ **Navigation**: Maintains fund context in routing
✅ **Error Handling**: Uses ErrorModalService for consistent messaging
✅ **Loading States**: Proper UI feedback during operations
