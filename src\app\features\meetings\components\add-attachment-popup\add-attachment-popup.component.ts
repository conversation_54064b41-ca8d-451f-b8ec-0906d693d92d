import { Component, Inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { AttachmentModule } from '@shared/enum/AttachmentModule';
import { 
  MeetingServiceProxy, 
  AddMeetingAttachmentRequest,
  MeetingAttachmentDtoBaseResponse
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';

export interface AddAttachmentDialogData {
  meetingId: number;
}

@Component({
  selector: 'app-add-attachment-popup',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    TranslateModule,
    FileUploadComponent,
    CustomButtonComponent
  ],
  templateUrl: './add-attachment-popup.component.html',
  styleUrls: ['./add-attachment-popup.component.scss']
})
export class AddAttachmentPopupComponent implements OnDestroy {
  private destroy$ = new Subject<void>();
  
  // UI State
  isLoading = false;
  uploadedFiles: any[] = [];

  // Flag to prevent duplicate API calls
  isAttachingFiles = false;
  
  // Enums for template
  buttonTypeEnum = ButtonTypeEnum;
  iconEnum = IconEnum;
  attachmentModule = AttachmentModule;

  constructor(
    public dialogRef: MatDialogRef<AddAttachmentPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddAttachmentDialogData,
    private meetingServiceProxy: MeetingServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService
  ) {}

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Handle single file upload from the file upload component
   */
  onFileUploaded(file: any): void {
    console.log('File uploaded:', file);

    if (file?.id) {
      // Check if file already exists in the list
      if (!this.uploadedFiles.find(f => f.id === file.id)) {
        this.uploadedFiles.push(file);
        console.log('File added to list:', file);
      } else {
        console.log('File already exists in list');
      }
    } else {
      console.error('Invalid file object received:', file);
    }

    console.log('Current uploaded files:', this.uploadedFiles);
  }

  /**
   * Remove a file from the uploaded files list
   */
  removeFile(fileToRemove: any): void {
    this.uploadedFiles = this.uploadedFiles.filter(file => file.id !== fileToRemove.id);
  }

  /**
   * Save attachments to the meeting
   */
  saveAttachments(): void {
    // Prevent duplicate API calls
    if (this.isAttachingFiles) {
      return;
    }

    if (this.uploadedFiles.length === 0) {
      this.errorModalService.showError(
        this.translateService.instant('COMMON.VALIDATION_ERROR')
      );
      return;
    }

    // Set loading states
    this.isAttachingFiles = true;
    this.isLoading = true;
    let completedRequests = 0;
    let successfulAttachments = 0;
    const totalFiles = this.uploadedFiles.length;

    // Process each file individually
    this.uploadedFiles.forEach(file => {
      const request = new AddMeetingAttachmentRequest();
      request.meetingId = this.data.meetingId;
      request.attachmentId = file.id;

      this.meetingServiceProxy.attachmentsMeetingPost(request)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: MeetingAttachmentDtoBaseResponse) => {
            completedRequests++;
            if (response.successed) {
              successfulAttachments++;
            }

            // Check if all requests are completed
            if (completedRequests === totalFiles) {
              this.handleAttachmentResults(successfulAttachments, totalFiles,null);
            }
          },
          error: (error) => {
            completedRequests++;
            console.error('Error attaching file:', error);

            // Check if all requests are completed
            if (completedRequests === totalFiles) {
              this.handleAttachmentResults(successfulAttachments, totalFiles,error);
            }
          }
        });
    });
  }

  /**
   * Handle the results of attachment operations
   */
  private handleAttachmentResults(successful: number, total: number,error:any): void {
    // Reset loading states
    this.isAttachingFiles = false;
    this.isLoading = false;
    
    if (successful === total) {
      // All attachments successful
      this.errorModalService.showSuccess(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.ATTACHMENT_ADDED_SUCCESS')
      );
      this.dialogRef.close({ success: true, attachedCount: successful });
    } else if (successful > 0) {
      // Partial success
      this.errorModalService.showError(
        error==null?
        this.translateService.instant('COMMON.ERROR'):
        error.parsedMessage
      );
      this.dialogRef.close({ success: true, attachedCount: successful });
    } else {
      // All failed
      this.errorModalService.showError(
        error==null?
        this.translateService.instant('COMMON.ERROR'):
        error.parsedMessage
      );
    }
  }

  /**
   * Cancel and close dialog
   */
  cancel(): void {
    // Prevent closing during API operations
    if (this.isAttachingFiles) {
      return;
    }
    this.dialogRef.close({ success: false });
  }

  /**
   * Check if save button should be disabled
   */
  isSaveDisabled(): boolean {
    return this.isLoading || this.isAttachingFiles || this.uploadedFiles.length === 0;
  }

  /**
   * Format file size in bytes to human readable format
   */
  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
