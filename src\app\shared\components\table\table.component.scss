@import "../../../../assets/scss/variables";

::ng-deep.mat-mdc-table thead{
background-color: #EBF3FC !important;
}

::ng-deep.mat-sort-header-content{
  font-weight: 700;
  color: #4F4F4F;
  font-size: 14px;
}

::ng-deep [dir="ltr"] .mat-sort-header-container {
  flex-direction: row-reverse !important;
   width: fit-content;
}

::ng-deep [dir="rtl"] .mat-sort-header-container {
  flex-direction: row-reverse !important;
  width: fit-content;
}

::ng-deep.mdc-data-table__cell {
  color:  #333;
font-size: 14px;
font-weight: 500;
padding: 0;
}

// ::ng-deep.mdc-data-table__cell:last-child ,.mdc-data-table__cell:nth-child(5){
// padding: 0 !important;
// }



  ::ng-deep .mat-mdc-table thead tr th.mat-mdc-header-cell .mat-sort-header-arrow {
    opacity: 0.54 !important;
    transform: translate(25%, 3px) !important;
  }



  ::ng-deep .mat-sort-header-arrow {
    opacity: 0.54 !important;
  transform: none !important;
}

::ng-deep .mat-sort-header-stem,
::ng-deep .mat-sort-header-indicator {
    opacity: 0.54 !important;}


// .flex-align-buttons{
//   justify-content: end;
// }


.flex-buttons{
  display: flex;
  gap: 8px;
  align-items: center;

  .action-button {
    background-color: transparent;
    border: 1px solid transparent;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f5f5f5;
      border-color: #e0e0e0;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(15, 108, 189, 0.2);
    }

    span {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Action menu styles
.action-menu-item {
  width: 100%;

  .action-menu-button {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    .action-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .action-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }
}
::ng-deep.mat-sort-header-arrow{
color: #0F6CBD !important;
margin:  0 6px 0 !important;

}

table td  tr:nth-child(odd):nth-child(n+1) {
  background-color: #FFFFFF;
}

table  tr:nth-child(even):nth-child(n+1) {
  background-color: #FAFAFA;
}

table {
  box-shadow: none;
}

::ng-deep {

//   .align-end-last {
//   .mat-mdc-header-cell:last-child,
//   .mat-mdc-cell:last-child {
//     text-align: end !important;
//   }
// }
//  .mat-mdc-header-cell:last-child {
//   text-align: end !important;
// }
// .mdc-data-table__header-cell {
//     padding: 0 6px !important;
// }
  .mat-mdc-slide-toggle {
    .mdc-switch {
      .mdc-switch__track {
        &::before {
          background-color: $grey !important;
        }

        &::after {
          background-color: #5675ad  !important;
        }
      }

      .mdc-switch__handle-track {
        .mdc-switch--selected {
          .mdc-switch__handle {
            &::before {
              background-color: #FFFFFF !important;
            }

            &::after{
              background-color: #00205a !important;
            }
          }
        }
      }

    }
  }

  .mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{
    background-color: #0F6CBD !important;
  }

  .mdc-switch--selected:enabled .mdc-switch__handle::after {
    background: $navy-blue !important;
  }

  .mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after {
    background: $dark-blue !important;
  }

  .mdc-switch--selected:enabled:active .mdc-switch__ripple::after {
    background-color: #0F6CBD !important;
  }
}


.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;

  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin:0 6px;
  }

  &.status-green {
    background-color: #f1faf1;
    color: #27ae60;

    .dot {
      background-color: #27ae60;
    }
  }

  &.status-blue {
    background-color: #e5eefb;
    color: #2f80ed;

    .dot {
      background-color: #2f80ed;
    }
  }

  &.status-orange {
    background-color: #fdf1eb;
    color: #ff5f3d;

    .dot {
      background-color: #ff5f3d;
    }
  }

  &.status-grey {
    background-color: #e0e0e0;
    color: #828282;

    .dot {
      background-color: #828282;
    }
  }
}

// Enhanced Custom Paginator Styles
.paginator {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 16px;
  padding: 12px 24px;
  // margin-top: 20px;
  flex-wrap: wrap;
  background: #fafafa;
  border-top: 1px solid #E6E6E6;

  .page-size-section {
    display: flex;
    align-items: center;
    gap: 8px;

    .page-label,
    .of-label {
      font-size: 14px;
      color: #4a4a4a;
      font-weight: 400;
      white-space: nowrap;
    }

    .page-size-select {
      border: 1px solid #d1d5db;
      border-radius: 4px;
      padding: 6px 10px;
      background: white;
      font-size: 14px;
      color: #374151;
      font-weight: 400;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 60px;
      appearance: none;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 8px center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 32px;

      &:focus {
        outline: none;
        border-color: #000;
        box-shadow: 0 0 0 1px #000;
      }
    }
  }

  .navigation-section {
    display: flex;
    align-items: center;
    gap: 2px;

    .nav-button {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 12px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      background: white;
      color: #6b7280;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
      transition: all 0.2s ease;
      white-space: nowrap;

      svg {
        width: 14px;
        height: 14px;
        flex-shrink: 0;
      }

      // &:hover:not(:disabled) {
      //   background: #f9fafb;
      //   border-color: #9ca3af;
      //   color: #374151;
      // }

      &:disabled {
        background: #f9fafb;
        border-color: #e5e7eb;
        color: #d1d5db;
        cursor: not-allowed;

        svg {
          color: #d1d5db;
        }
      }
    }

    .page-numbers {
      display: flex;
      align-items: center;
      gap: 2px;
      margin: 0 4px;

      .page-button {
        min-width: 36px;
        height: 36px;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        background: white;
        color: #6b7280;
        font-size: 14px;
        font-weight: 400;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;

        &:hover:not(.active):not(.ellipsis) {
          background: #eaecef;
          // border-color: #9ca3af;
          // color: #374151;
        }

        &.active {
          background: $navy-blue;
          border-color: $navy-blue;
          color: white;
          font-weight: 500;
        }

        &.ellipsis {
          border: none;
          background: transparent;
          cursor: default;
          color: #9ca3af;

          &:hover {
            background: transparent;
            color: #9ca3af;
          }
        }
      }
    }
  }
}

// RTL Support for enhanced paginator
[dir="rtl"] {
  .paginator {
    .page-size-section {
      .page-size-select {
        background-position: left 8px center;
        padding-left: 32px;
        padding-right: 10px;
      }
    }

    .navigation-section {
      .nav-button {
        svg {
          transform: scaleX(-1);
        }
      }
    }
  }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .paginator {
    flex-direction: column;
    gap: 12px;
    padding: 16px;

    .page-size-section {
      order: 2;
    }

    .navigation-section {
      order: 1;

      .nav-button {
        padding: 6px 10px;
        font-size: 13px;

        svg {
          width: 12px;
          height: 12px;
        }
      }

      .page-numbers {
        .page-button {
          min-width: 32px;
          height: 32px;
          font-size: 13px;
        }
      }
    }
  }
}

// Extra small devices
@media (max-width: 480px) {
  .paginator {
    .navigation-section {
      .page-numbers {
        .page-button {
          min-width: 28px;
          height: 28px;
          font-size: 12px;
        }
      }
    }
  }
}
