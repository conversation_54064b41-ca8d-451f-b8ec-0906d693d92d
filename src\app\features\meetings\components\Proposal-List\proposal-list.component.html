<div class="meetings">
  <!-- Breadcrumb -->
  <!-- <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div> -->

  <!-- Page Header -->
  <div class="page-header-section">

    <app-page-header
      [title]="'INVESTMENT_FUNDS.MEETING.PROPOSAL_TITLE' | translate"
      [showSearch]="true"  
      [showFilter]="true"
      [searchPlaceholder]="translateService.instant('INVESTMENT_FUNDS.MEETING.SEARCH_PLACEHOLDER')"
      [restrictSearchToNumbers]="false"
      [showCreateButton]="isHasPermissionAdd"
      createButtonText="INVESTMENT_FUNDS.MEETING.ADD"
      (search)="onSearch($event)" (filter)="openFilter()"
      (create)="addNewProposalMeeting()">
    </app-page-header>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !hasError && MeetingsProposals.length === 0" class="empty-state text-center py-5">
    <p>{{ 'INVESTMENT_FUNDS.MEETING.NO_PROPOSALS_FOUND' | translate }}</p>
  </div>

  <!-- Meetings Grid -->
  <div *ngIf="!isLoading && !hasError && MeetingsProposals.length > 0" class="resolutions-grid mb-3 mt-5">
    <!-- Card -->
    <div class="resolution-card" *ngFor="let meeting of MeetingsProposals">
      <ng-container>
        <div class="card-header">
          <h3 class="resolution-title"  [title]="meeting.subject"  matTooltip="{{ meeting.subject }}">
          {{ meeting!.subject!.length > 30 ? (meeting.subject | slice:0:30) + '...': meeting.subject }}
          </h3>


          <div class="card-actions">
            <button class="action-btn details-btn" *ngIf="meeting.canViewDetails"
              (click)="viewMeetingDetails(meeting.id)"
              [title]="'COMMON.VIEW_DETAILS' | translate">
              <img src="assets/images/eye.png" alt="details" />
            </button>

             <button class="action-btn details-btn" *ngIf="meeting.canVote"
              (click)="VoteMeetingsProposal(meeting.id)"
              [title]="'COMMON.Vote' | translate">
               <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15.668 19.208C15.7895 19.2081 15.9063 19.2569 15.9922 19.3428C16.078 19.4287 16.126 19.5456 16.126 19.667C16.1259 19.7884 16.0781 19.9053 15.9922 19.9912C15.9063 20.0771 15.7894 20.1249 15.668 20.125H4.33301C4.21157 20.1249 4.09467 20.0771 4.00879 19.9912C3.92291 19.9053 3.87509 19.7884 3.875 19.667C3.875 19.5456 3.92305 19.4287 4.00879 19.3428C4.09467 19.2569 4.21157 19.2081 4.33301 19.208H15.668ZM15.2979 3.875C15.5959 3.87512 15.8818 3.99349 16.0928 4.2041L19.7969 7.9082C20.0076 8.11938 20.126 8.40575 20.126 8.7041C20.1258 9.00228 20.0075 9.28796 19.7969 9.49902L19.042 10.2539V10.2549C18.8295 10.4657 18.5473 10.5829 18.2471 10.583H18.2432C18.095 10.5829 17.9482 10.5533 17.8115 10.4961C17.7091 10.4532 17.6135 10.3949 17.5283 10.3242L17.4463 10.249L13.7637 6.52441L13.7627 6.52344C13.5547 6.31174 13.4385 6.02631 13.4395 5.72949C13.4405 5.43255 13.5589 5.14782 13.7686 4.9375L14.502 4.2041L14.585 4.12988C14.7852 3.96608 15.0369 3.875 15.2979 3.875ZM15.2559 4.79395C15.2426 4.79656 15.2294 4.80047 15.2168 4.80566C15.1916 4.81605 15.1687 4.83135 15.1494 4.85059L14.416 5.58398C14.3772 5.62305 14.3557 5.67635 14.3555 5.73145C14.3553 5.78606 14.3761 5.83869 14.4141 5.87793L18.0977 9.60352C18.1336 9.6397 18.1803 9.6597 18.2305 9.66309C18.2688 9.65387 18.2972 9.64915 18.3281 9.63867C18.3601 9.62785 18.3799 9.61634 18.3926 9.60352L19.1475 8.84961L19.1738 8.81738C19.1964 8.78332 19.208 8.74261 19.208 8.70117C19.2079 8.65992 19.1962 8.61986 19.1738 8.58594L19.1475 8.55371L15.4434 4.85059C15.4241 4.83144 15.4011 4.81599 15.376 4.80566C15.351 4.79545 15.3238 4.78998 15.2969 4.79004L15.2559 4.79395Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M17.6763 9.27441C17.7378 9.28493 17.7968 9.30778 17.8491 9.3418C17.9015 9.37584 17.9464 9.42066 17.981 9.47266C18.0155 9.52461 18.0391 9.58315 18.0503 9.64453C18.0614 9.706 18.0595 9.76922 18.0454 9.83008C18.0313 9.89093 18.0053 9.94875 17.9683 9.99902L17.9673 9.99805L15.6323 13.5977L15.6313 13.5967C15.6149 13.6227 15.5972 13.6479 15.5757 13.6699H15.5747L12.6548 16.6357V16.6367C12.5024 16.7904 12.3206 16.9122 12.1206 16.9951C11.9707 17.0572 11.8127 17.0969 11.6519 17.1123L11.4897 17.1191C10.9096 17.119 10.3851 16.8198 10.0874 16.3203C9.70451 15.6766 9.83115 14.8412 10.3813 14.2881L12.6499 12.0146C12.5386 11.9028 12.4085 11.8112 12.2632 11.748C12.1281 11.6893 11.9837 11.6551 11.8374 11.6465H11.689C11.4769 11.6571 11.2711 11.7216 11.0903 11.833C10.9096 11.9444 10.7596 12.0995 10.6548 12.2842L10.6499 12.292C10.5853 12.3886 10.4866 12.4577 10.3735 12.4844C10.2605 12.511 10.1415 12.493 10.0405 12.4355C9.93956 12.378 9.8633 12.2848 9.82861 12.1738C9.79395 12.0629 9.80282 11.9427 9.85303 11.8379L9.85693 11.8301C10.0379 11.5118 10.2962 11.2439 10.6079 11.0518C10.9194 10.8598 11.2747 10.7492 11.6401 10.7305C12.006 10.7106 12.3713 10.7839 12.7017 10.9424C13.032 11.1009 13.3173 11.3399 13.5308 11.6377L13.6343 11.7822L13.6753 11.8506C13.7097 11.9218 13.7247 12.0012 13.7192 12.0811C13.7119 12.1877 13.668 12.2889 13.5942 12.3662L13.5923 12.3682L11.0288 14.9336L11.0298 14.9346C10.7706 15.1956 10.7144 15.5833 10.8735 15.8506V15.8516C11.1037 16.2388 11.6875 16.3058 12.0044 15.9902L14.8921 13.0566L17.2046 9.49316C17.235 9.44171 17.275 9.39639 17.3228 9.36035C17.3726 9.32273 17.43 9.29602 17.4907 9.28125C17.5514 9.26648 17.6147 9.26392 17.6763 9.27441ZM14.3218 5.90918C14.3835 5.91579 14.4434 5.93444 14.4976 5.96484C14.5517 5.99525 14.5994 6.03665 14.6372 6.08594C14.6749 6.13516 14.702 6.19188 14.7173 6.25195C14.7325 6.31194 14.7356 6.37441 14.7261 6.43555C14.7164 6.4969 14.6937 6.55582 14.6606 6.6084C14.6276 6.66097 14.5848 6.7069 14.5337 6.74219C14.4841 6.77646 14.4276 6.79891 14.3687 6.81152L14.3696 6.8125L10.7231 7.73926H10.7222C10.2568 7.85709 9.82203 8.07347 9.44775 8.37402C9.07349 8.67457 8.76875 9.05256 8.55322 9.48145C8.17344 10.239 8.09737 11.1133 8.34131 11.9248V11.9258C8.3616 11.9939 8.36628 12.0657 8.354 12.1357C8.34162 12.206 8.31257 12.2728 8.27002 12.3301C8.22754 12.3872 8.17243 12.4337 8.10889 12.4658C8.04535 12.4978 7.97496 12.5144 7.90381 12.5146H7.90283C7.80442 12.5146 7.7084 12.4835 7.62939 12.4248C7.5503 12.366 7.49274 12.2828 7.46436 12.1885C7.3454 11.7931 7.28461 11.3826 7.28467 10.9697C7.28468 10.32 7.44032 9.66471 7.73193 9.07324L7.84033 8.87109C8.10515 8.40718 8.45506 7.99653 8.87256 7.66113C9.34973 7.27783 9.90417 7.00159 10.4976 6.85156L14.144 5.9248V5.92578C14.2017 5.90883 14.2619 5.90278 14.3218 5.90918Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M12.9819 11.874C13.1031 11.8741 13.2194 11.9222 13.3052 12.0078C13.3911 12.0937 13.4398 12.2106 13.4399 12.332C13.4399 12.4536 13.3911 12.5703 13.3052 12.6562C13.2193 12.742 13.1033 12.7909 12.9819 12.791H6.12549V19.208H13.8872V14.2676C13.8872 14.146 13.936 14.0293 14.022 13.9434C14.1078 13.8576 14.2239 13.8087 14.3452 13.8086C14.4668 13.8086 14.5835 13.8574 14.6694 13.9434C14.7554 14.0293 14.8042 14.146 14.8042 14.2676V19.666C14.8042 19.7876 14.7554 19.9043 14.6694 19.9902C14.5835 20.0762 14.4668 20.125 14.3452 20.125H5.6665C5.54507 20.1249 5.42816 20.0761 5.34229 19.9902C5.25656 19.9043 5.2085 19.7874 5.2085 19.666V12.332L5.21729 12.2422C5.23494 12.1542 5.2779 12.0722 5.34229 12.0078C5.42816 11.9219 5.54507 11.8741 5.6665 11.874H12.9819Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M10.5 12.0003C10.8333 11.667 11.8 11.2003 13 12.0003H10.5Z" fill="#D9D9D9"/>
          </svg>
            </button>

          </div>
        </div>
        <div class="card-content clickable-card" (click)="viewMeetingDetails(meeting.id)">

          <!-- <p class="title mb-3">{{ meeting.description }}</p> -->
            <p class="title mb-3"
              [title]="meeting.description"
              matTooltip="{{ meeting.description }}">
              {{ meeting!.description!.length > 60 ? (meeting.description | slice:0:60) + '...': meeting.description }}
            </p>


          <div class="resolution-meta">
            <div class="meta-item">
              <!-- Created Date -->
              <p class="meta-label gregorian">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M4 8C4.55228 8 5 7.55229 5 7C5 6.44771 4.55228 6 4 6C3.44772 6 3 6.44771 3 7C3 7.55229 3.44772 8 4 8ZM5 10C5 10.5523 4.55228 11 4 11C3.44772 11 3 10.5523 3 10C3 9.44771 3.44772 9 4 9C4.55228 9 5 9.44771 5 10ZM7 8C7.55229 8 8 7.55229 8 7C8 6.44771 7.55229 6 7 6C6.44771 6 6 6.44771 6 7C6 7.55229 6.44771 8 7 8ZM8 10C8 10.5523 7.55229 11 7 11C6.44771 11 6 10.5523 6 10C6 9.44771 6.44771 9 7 9C7.55229 9 8 9.44771 8 10ZM10 8C10.5523 8 11 7.55229 11 7C11 6.44771 10.5523 6 10 6C9.44771 6 9 6.44771 9 7C9 7.55229 9.44771 8 10 8ZM14 2.5C14 1.11929 12.8807 0 11.5 0H2.5C1.11929 0 0 1.11929 0 2.5V11.5C0 12.8807 1.11929 14 2.5 14H11.5C12.8807 14 14 12.8807 14 11.5V2.5ZM1 4H13V11.5C13 12.3284 12.3284 13 11.5 13H2.5C1.67157 13 1 12.3284 1 11.5V4ZM2.5 1H11.5C12.3284 1 13 1.67157 13 2.5V3H1V2.5C1 1.67157 1.67157 1 2.5 1Z"
                    fill="#616161" />
                </svg>
                {{ meeting.createdDate | georgianDate }}
              </p>

              <!-- Total Votes -->
              <p class="meta-label gregorian">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M12 6V12L16 14M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                    stroke="#181D27" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
                {{ meeting.totalVotes }} {{ 'INVESTMENT_FUNDS.MEETING.VOTES' | translate }}
              </p>

              <!-- Proposed Dates Count -->
              <p class="meta-label gregorian">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.3503 17.623 3.8507 18.1676 4.55231C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89317 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z"
                    stroke="#181D27" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
                {{ meeting.proposedDates }} {{ 'INVESTMENT_FUNDS.MEETING.PROPOSED_DATES' | translate }}
              </p>

            </div>
            <div class="status">
              <p class="status" [ngClass]="getStatusClass(meeting.statusId)">
                {{ meeting.status.localizedName}}
              </p>

            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>

  <!-- Pagination Section -->
  <div class="pagination-section" *ngIf="totalCount > 0 && !isLoading && !hasError">
    <!-- Pagination Controls -->
    <div class="pagination-controls">
      <!-- Previous Page Button -->
      <button class="pagination-btn prev-btn"
              [disabled]="!canGoPrevious()"
              (click)="onPreviousPage()"
              [title]="'PAGINATION.PREVIOUS' | translate">
        <img [src]="isEnglish() ? 'assets/images/arrow-left2.png' : 'assets/images/arrow-right.png'" class="mx-2" alt="previous">
        <span class="btn-text">{{ 'PAGINATION.PREVIOUS' | translate }}</span>
      </button>

      <!-- Page Numbers -->
      <div class="page-numbers">
        <button *ngFor="let page of getPageNumbers()"
                class="pagination-btn page-number-btn"
                [class.active]="page === currentPage"
                (click)="onPageChange(page)">
          {{page}}
        </button>
      </div>

      <!-- Next Page Button -->
      <button class="pagination-btn next-btn"
              [disabled]="!canGoNext()"
              (click)="onNextPage()"
              [title]="'PAGINATION.NEXT' | translate">
        <span class="btn-text">{{ 'PAGINATION.NEXT' | translate }}</span>
        <img [src]="isEnglish() ? 'assets/images/arrow-right.png' : 'assets/images/arrow-left2.png'" class="mx-2" alt="next">
      </button>
    </div>
  </div>
