import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";
import { FormBuilderComponent } from "@shared/components/form-builder/form-builder.component";
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import {
  MeetingsProposalStatusDto,
  MeetingsProposalStatusDtoIEnumerableBaseResponse,
  MeetingsProposalServiceProxy
} from '@core/api/api.generated';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-meetings-proposal-filter',
  standalone: true,
  imports: [
    CustomButtonComponent,
    FormBuilderComponent,
    ReactiveFormsModule,
    MatDialogModule,
    TranslateModule,
    CommonModule
  ],
  templateUrl: './meetings-proposal-filter.component.html',
  styleUrl: './meetings-proposal-filter.component.scss'
})
export class MeetingsProposalFilterComponent implements OnInit, OnDestroy {
  formGroup!: FormGroup;
  isFormSubmitted = false;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  // API data
  statusOptions: MeetingsProposalStatusDto[] = [];
  isLoadingData = false;

  // Lifecycle management
  private destroy$ = new Subject<void>();

  formControls: IControlOption[] = [
    {
      type: InputType.Dropdown,
      formControlName: 'status',
      id: 'status',
      name: 'status',
      label: 'INVESTMENT_FUNDS.MEETING.FILTER_BY_STATUS',
      placeholder: 'INVESTMENT_FUNDS.MEETING.ALL_STATUSES',
      isRequired: false,
      class: 'col-md-12',
      options: [], // Will be populated dynamically from API
    },
  ];

  constructor(
    public dialogRef: MatDialogRef<MeetingsProposalFilterComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private formBuilder: FormBuilder,
    private MeetingsProposalServiceProxy: MeetingsProposalServiceProxy,
    private translateService: TranslateService
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    // Load status options from API
    this.loadStatusOptions();

    // Pre-populate form with existing filter data
    if (this.data) {
      this.formGroup.patchValue(this.data);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      search: [''],
      status: [''],
      fromDate: [''],
      toDate: [''],
      createdBy: [''],
    });
  }

  /**
   * Load status options from API
   */
  private loadStatusOptions(): void {
    this.isLoadingData = true;

    this.MeetingsProposalServiceProxy.getMeetingsProposalStatus()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: MeetingsProposalStatusDtoIEnumerableBaseResponse) => {
          if (response.successed && response.data) {
            this.statusOptions = response.data;
            this.updateStatusFormControl();
          } else {
            console.warn('Failed to load status options:', response.message);
            //this.setDefaultStatusOptions();
          }
          this.isLoadingData = false;
        },
        error: (error: any) => {
          console.error('Error loading status options:', error);
          //this.setDefaultStatusOptions();
          this.isLoadingData = false;
        }
      });
  }

  /**
   * Update status form control with API data
   */
  private updateStatusFormControl(): void {
    const statusControl = this.formControls.find(
      (control) => control.formControlName === 'status'
    );
    if (statusControl) {
      statusControl.options = [
        {
          id: '',
          name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.ALL_STATUSES'),
        },
        ...this.statusOptions.map((status) => ({
          id: status.id,
          name: status.localizedName || status.nameAr || status.nameEn || `Status ${status.id}`,
        })),
      ];
    }
  }

  /**
   * Set default status options as fallback
   */
  private setDefaultStatusOptions(): void {
    const statusControl = this.formControls.find(
      (control) => control.formControlName === 'status'
    );
    if (statusControl) {
      statusControl.options = [
        { id: '', name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.ALL_STATUSES') },
        { id: 1, name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.STATUS.DRAFT') },
        { id: 2, name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.STATUS.PENDING') },
        { id: 3, name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.STATUS.ACTIVE') },
        { id: 4, name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.STATUS.COMPLETED') },
        { id: 5, name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.STATUS.CANCELLED') },
      ];
    }
  }

  dropdownChanged(event: any): void {
    // Handle dropdown changes if needed
    console.log('Dropdown changed:', event);
  }

  applyFilters(): void {
    this.isFormSubmitted = true;
    if (this.formGroup.valid) {
      const filters = this.formGroup.value;
      console.log('Raw form values:', filters);

      // Remove empty values
      const cleanFilters = Object.keys(filters).reduce((acc: any, key) => {
        const value = filters[key];
        console.log(
          `Checking filter key: ${key}, value: ${value}, type: ${typeof value}`
        );

        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value;
          console.log(`Added ${key} to clean filters:`, value);
        } else {
          console.log(
            `Filtered out ${key} because it was empty/null/undefined`
          );
        }
        return acc;
      }, {});

      console.log('Clean filters being sent:', cleanFilters);
      this.dialogRef.close(cleanFilters);
    }
  }

  resetFilters(): void {
    this.formGroup.reset();
    this.isFormSubmitted = false;
    this.applyFilters();
  }

  closeDialog(): void {
    this.dialogRef.close();
  }
 
}
