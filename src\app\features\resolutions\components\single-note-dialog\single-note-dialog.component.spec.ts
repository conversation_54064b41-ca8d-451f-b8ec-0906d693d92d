import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SingleNoteDialogComponent } from './single-note-dialog.component';

describe('SingleNoteDialogComponent', () => {
  let component: SingleNoteDialogComponent;
  let fixture: ComponentFixture<SingleNoteDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SingleNoteDialogComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SingleNoteDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
