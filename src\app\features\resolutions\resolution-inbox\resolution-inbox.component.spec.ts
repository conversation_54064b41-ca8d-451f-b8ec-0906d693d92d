import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';

import { ResolutionInboxComponent } from './resolution-inbox.component';
import { ResolutionsServiceProxy, SingleResolutionResponsePaginatedResult, HttpStatusCode } from '@core/api/api.generated';
import { TokenService } from '../../auth/services/token.service';
import { ErrorModalService } from '@core/services/error-modal.service';

describe('ResolutionInboxComponent', () => {
  let component: ResolutionInboxComponent;
  let fixture: ComponentFixture<ResolutionInboxComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: jasmine.SpyObj<ActivatedRoute>;
  let mockResolutionsProxy: jasmine.SpyObj<ResolutionsServiceProxy>;
  let mockTokenService: jasmine.SpyObj<TokenService>;
  let mockTranslateService: jasmine.SpyObj<TranslateService>;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockErrorModalService: jasmine.SpyObj<ErrorModalService>;

  beforeEach(async () => {
    // Create spies for dependencies
    mockRouter = jasmine.createSpyObj('Router', ['navigate', 'navigateByUrl']);
    mockActivatedRoute = jasmine.createSpyObj('ActivatedRoute', [], {
      params: of({}),
      queryParams: of({})
    });
    mockResolutionsProxy = jasmine.createSpyObj('ResolutionsServiceProxy', [
      'resolutionInbox',
      'checkAddPermission'
    ]);
    mockTokenService = jasmine.createSpyObj('TokenService', [
      'hasPermission',
      'hasRole'
    ]);
    mockTranslateService = jasmine.createSpyObj('TranslateService', [
      'instant',
      'get'
    ], {
      currentLang: 'en'
    });
    mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
    mockErrorModalService = jasmine.createSpyObj('ErrorModalService', [
      'showError',
      'showSuccess'
    ]);

    await TestBed.configureTestingModule({
      imports: [ResolutionInboxComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: ResolutionsServiceProxy, useValue: mockResolutionsProxy },
        { provide: TokenService, useValue: mockTokenService },
        { provide: TranslateService, useValue: mockTranslateService },
        { provide: MatDialog, useValue: mockDialog },
        { provide: ErrorModalService, useValue: mockErrorModalService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ResolutionInboxComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct breadcrumb items', () => {
    expect(component.breadcrumbItems).toEqual([
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'RESOLUTIONS.INBOX_TITLE', url: '', disabled: true }
    ]);
  });

  it('should set isHasPermissionAdd based on token service permission', () => {
    mockTokenService.hasPermission.and.returnValue(true);
    
    component.ngOnInit();
    
    expect(component.isHasPermissionAdd).toBe(true);
    expect(mockTokenService.hasPermission).toHaveBeenCalledWith('Resolution.Create');
  });

  it('should call resolutionInbox API on loadResolutions', () => {
    const mockResponse = new SingleResolutionResponsePaginatedResult({
      statusCode: HttpStatusCode._200,
      successed: true,
      message: '',
      data: [],
      errors: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
      pageSize: 12,
      hasPreviousPage: false,
      hasNextPage: false
    });
    mockResolutionsProxy.resolutionInbox.and.returnValue(of(mockResponse));

    component.loadResolutions();

    expect(mockResolutionsProxy.resolutionInbox).toHaveBeenCalledWith(
      undefined, // fundId
      undefined, // search
      1, // pageNumber
      12, // pageSize
      'resolutionDate desc' // orderBy
    );
  });

  it('should navigate to funds list when addNewResolution is called', () => {
    mockTokenService.hasPermission.and.returnValue(true);
    mockRouter.navigate.and.returnValue(Promise.resolve(true));

    component.addNewResolution();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/admin/investment-funds']);
  });

  it('should navigate to resolution details with fundId', () => {
    const mockResolution = { id: 123, fundId: 456 } as any;

    component.viewResolutionDetails(mockResolution);

    expect(mockRouter.navigate).toHaveBeenCalledWith([
      '/admin/investment-funds/resolutions/details',
      123
    ], {
      queryParams: { fundId: 456 }
    });
  });

  it('should handle role-based filtering for FundManager', () => {
    mockTokenService.hasRole.and.returnValue(true);

    component['initializeRoleBasedFiltering']();

    expect(component.userRole).toBe('FundManager');
    expect(component.allowedStatusIds).toEqual([1, 2, 3, 4, 5, 6]);
  });

  it('should handle role-based filtering for BoardMember', () => {
    mockTokenService.hasRole.and.callFake((role: string) => role === 'BoardMember');

    component['initializeRoleBasedFiltering']();

    expect(component.userRole).toBe('BoardMember');
    expect(component.allowedStatusIds).toEqual([3, 5, 6]);
  });

  it('should update breadcrumb correctly for inbox context', () => {
    component['updateBreadcrumb']();

    expect(component.breadcrumbItems).toEqual([
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'RESOLUTIONS.INBOX_TITLE', url: '', disabled: true }
    ]);
  });
});
