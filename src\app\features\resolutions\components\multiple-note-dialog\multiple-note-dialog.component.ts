import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { MemberNoteComponent } from "../member-note/member-note.component";
import { ResolutionItemVoteCommentDto } from '@core/api/api.generated';
import { DateTime } from 'luxon';

export interface MultipleNoteDialogData {

   comments: any[];
   canAddComment : boolean;
}

@Component({
  selector: 'app-multiple-note-dialog',
  standalone: true,
  imports: [CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule, CustomButtonComponent, MemberNoteComponent],
    templateUrl: './multiple-note-dialog.component.html',
    styleUrl: './multiple-note-dialog.component.scss'
})
export class MultipleNoteDialogComponent {
 body ='';
    buttonEnum = ButtonTypeEnum;
    IconEnum = IconEnum;
    comments: any;
    canAddComment : boolean;
    dateNow = new Date().toISOString();
    formSubmitted = false

  constructor(
    public dialogRef: MatDialogRef<MultipleNoteDialogComponent>,
    private translateService: TranslateService,
    @Inject(MAT_DIALOG_DATA) public data: MultipleNoteDialogData
  ) {

    this.comments = data.comments;
    this.canAddComment = data.canAddComment;
  }


  ngOnInit(): void {

  }

  onCancel(): void {
    this.dialogRef.close();
  }

   addNote(){
    this.formSubmitted = true;
    let commentObj ={
      id:0,
      comment:this.body
    }
 if (this.body?.trim()) {
    this.dialogRef.close(commentObj);
  }  }

    getNotificationTime(createdAt: Date | any): string {
    const now = new Date();
    const notificationDate = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.NOW');
    } else if (diffInMinutes < 60) {
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+`${diffInMinutes}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.MINUTE');
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+` ${hours}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.HOUR');
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+` ${days}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.DAY');
    }
  }
}
