import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormControl, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from "@shared/components/breadcrumb/breadcrumb.component";
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { MatRadioModule } from "@angular/material/radio";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { CommonModule } from '@angular/common';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import {
  MeetingsProposalServiceProxy,
  MeetingsProposalForVotingDto,
  CastVoteOnMeetingsProposalCommand
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { GeorgianDatePipe } from '@shared/pipes/georgian-date/georgian-date.pipe';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-vote-proposed-meeting',
  standalone: true,
  imports: [
    BreadcrumbComponent,
    CustomButtonComponent,
    TranslateModule,
    MatRadioModule,
    MatProgressSpinnerModule,
    ReactiveFormsModule,
    CommonModule,
    GeorgianDatePipe,
    DateHijriConverterPipe
  ],
  templateUrl: './vote-proposed-meeting.component.html',
  styleUrl: './vote-proposed-meeting.component.scss'
})
export class VoteProposedMeetingComponent implements OnInit, OnDestroy {

  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  // Form and data properties
  voteForm: FormGroup;
  proposalData: MeetingsProposalForVotingDto | null = null;
  timeSlots: any[] = []; // meetingProposalDates from API response
  votingResults: any[] = []; // meetingProposalVotesResult from API response

  // State management
  isLoading = false;
  isSubmitting = false;
  hasError = false;
  errorMessage = '';

  // Route parameters
  proposalId = 0;
  fundId = 0;

  // Lifecycle management
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private meetingsProposalServiceProxy: MeetingsProposalServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService,
    private breadcrumbService: BreadcrumbService
  ) {
    this.voteForm = new FormGroup({
      selectedTimeSlot: new FormControl(null, [Validators.required])
    });
  }

ngOnInit(): void {
    this.route.queryParams.subscribe((queryParams) => {
      this.proposalId = +queryParams['proposalId'] || 0;
      this.fundId = +queryParams['fundId'] || 0;

      if (this.proposalId > 0) {
        this.loadProposalForVoting();
      } else {
        this.hasError = true;
        this.errorMessage = 'INVESTMENT_FUNDS.MEETING.INVALID_PROPOSAL_ID';
      }
    });
    this.initializeBreadcrumbs();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onBreadcrumbClicked(event: IBreadcrumbItem): void {
    if (!event.disabled && event.url) {
      this.router.navigateByUrl(event.url);
    }
  }


  loadProposalForVoting(): void {
    this.isLoading = true;
    this.hasError = false;

    this.meetingsProposalServiceProxy.getProposalsForVoting(this.proposalId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.proposalData = response.data;
            // Use the correct property names from the API response
            this.timeSlots = response.data.meetingProposalDates || [];
            this.votingResults = response.data.meetingProposalVotesResult || [];

            if (response?.data?.currentUserVoted) {
              if (response?.data?.currentUserVoteResultId) {
                this.voteForm.patchValue({
                  selectedTimeSlot: response?.data?.currentUserVoteResultId
                });
              }
              this.voteForm.disable();
            }
          } else {
            this.hasError = true;
            this.errorMessage = response.message || 'INVESTMENT_FUNDS.MEETING.LOAD_ERROR';
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.hasError = true;
          this.errorMessage = 'INVESTMENT_FUNDS.MEETING.VOTE_SUBMIT_ERROR';
        }
      });
  }

  /**
   * Submit vote for selected time slot
   */
  confirmVote(): void {
    if (this.isSubmitting) {
          return;
      }

    const selectedTimeSlotId = this.voteForm.get('selectedTimeSlot')?.value;
    if (!selectedTimeSlotId || !this.proposalData) {
      this.isSubmitting = false;
      this.isLoading = false;
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.SELECT_TIME_SLOT_REQUIRED')
      );
      return;
    }

    this.isSubmitting = true;

    const voteCommand = new CastVoteOnMeetingsProposalCommand({
      id: 0, // This might be auto-generated by the API
      meetingsProposalId: this.proposalData.id,
      proposedDateId: selectedTimeSlotId
    });

    this.meetingsProposalServiceProxy.voteOnMeetingProposal(voteCommand)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isSubmitting = false;
          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('INVESTMENT_FUNDS.MEETING.VOTE_SUBMITTED_SUCCESSFULLY')
            );
            // Navigate back to meetings list
            this.router.navigate(['/admin/investment-funds/meetings'], {
              queryParams: { fundId: this.fundId }
            });
          } else {
            this.errorModalService.showError(
              response.message || this.translateService.instant('INVESTMENT_FUNDS.MEETING.VOTE_SUBMIT_ERROR')
            );
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorModalService.showError(
            this.translateService.instant('INVESTMENT_FUNDS.MEETING.VOTE_SUBMIT_ERROR')
          );
        }
      });
  }

  /**
   * Get form control for template access
   */
  get selectedTimeSlotControl() {
    return this.voteForm.get('selectedTimeSlot');
  }

  /**
   * Format date for display in Arabic/English based on current language
   */
  formatDate(date: any): string {
    if (!date) return '';

    try {
      const dateObj = new Date(date);
      const day = dateObj.getDate().toString().padStart(2, '0');
      const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
      const year = dateObj.getFullYear();

      // Get localized day name
      const dayNames = this.getLocalizedDayNames();
      const dayName = dayNames[dateObj.getDay()];

      return `${day}-${month}-${year} ${dayName}`;
    } catch (error) {
      return date;
    }
  }

  /**
   * Format time with localized AM/PM based on current language
   */
  formatTime(time: string): string {
    if (!time) return '';

    try {
      // Parse time (assuming format like "10:00:00" or "10:00")
      const timeParts = time.split(':');
      let hours = parseInt(timeParts[0]);
      const minutes = timeParts[1] || '00';

      // Get localized AM/PM strings
      const localizedTime = this.getLocalizedAmPm();

      // Determine AM/PM
      let period = '';
      if (hours === 0) {
        hours = 12;
        period = localizedTime.am;
      } else if (hours < 12) {
        period = localizedTime.am;
      } else if (hours === 12) {
        period = localizedTime.pm;
      } else {
        hours = hours - 12;
        period = localizedTime.pm;
      }

      return `${hours}:${minutes} ${period}`;
    } catch (error) {
      return time;
    }
  }

  /**
   * Format date and time for display (legacy method for compatibility)
   */
  formatDateTime(date: any, time: string): string {
    if (!date || !time) return '';
    return `${this.formatDate(date)} - ${this.formatTime(time)}`;
  }

  /**
   * TrackBy function for time slots to improve performance
   */
  trackByTimeSlotId(_index: number, timeSlot: any): number {
    return timeSlot.id;
  }

  /**
   * Get localized day names based on current language
   */
  private getLocalizedDayNames(): string[] {
    const currentLang = this.translateService.currentLang || 'ar';

    if (currentLang === 'ar') {
      return ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    } else {
      return ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    }
  }

  /**
   * Get localized AM/PM strings based on current language
   */
  private getLocalizedAmPm(): { am: string; pm: string } {
    const currentLang = this.translateService.currentLang || 'ar';

    if (currentLang === 'ar') {
      return { am: 'صباحاً', pm: 'مساءً' };
    } else {
      return { am: 'AM', pm: 'PM' };
    }
  }

  private initializeBreadcrumbs(): void {
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      {
        label: 'INVESTMENT_FUNDS.MEETING.MEETINGS',
        url: `/admin/investment-funds/meetings?fundId=${this.fundId}`
      },
      { label: 'INVESTMENT_FUNDS.MEETING.VOTE_MEETING_TITLE', url: '', disabled: true },
    ];
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }
  goBack() {
    this.router.navigate(['/admin/investment-funds/meetings'], {
      queryParams: { fundId: this.fundId }
    });
  }
}
