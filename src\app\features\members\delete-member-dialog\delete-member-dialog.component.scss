.delete-dialog-container {
  padding: 24px;
  min-width: 400px;
  max-width: 500px;

  .dialog-header {
    text-align: center;
    margin-bottom: 20px;

    .dialog-title {
      color: #dc3545;
      font-weight: 600;
      margin: 0;

      &.error-title {
        color: #dc3545;
      }
    }
  }

  .dialog-content {
    text-align: center;
    margin-bottom: 24px;

    .warning-icon {
      margin-bottom: 16px;

      img {
        width: 48px;
        height: 48px;
      }
    }

    .member-info {
      margin-bottom: 20px;

      .member-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .member-details {
        color: #666;
        font-size: 14px;
        margin: 0;
      }
    }

    .confirmation-message {
      p {
        color: #333;
        margin-bottom: 16px;
        line-height: 1.5;
      }
    }

    .error-message {
      p {
        color: #dc3545;
        margin-bottom: 16px;
        line-height: 1.5;
        font-weight: 500;
        text-align: center;
      }
    }
  }

  .dialog-actions {
    display: flex;
    gap: 12px;
    justify-content: space-between;

    &.single-action {
      justify-content: center;
    }

    .btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px 20px;
      border: none;
      border-radius: 6px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      img {
        width: 16px;
        height: 16px;
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }

      &.cancel-btn {
        background-color: #f8f9fa;
        color: #6c757d;
        border: 1px solid #dee2e6;

        &:hover:not(:disabled) {
          background-color: #e9ecef;
        }
      }

      &.close-btn {
        background-color: #6c757d;
        color: white;
        min-width: 120px;

        &:hover:not(:disabled) {
          background-color: #5a6268;
        }
      }

      &.delete-btn {
        background-color: #dc3545;
        color: white;

        &:hover:not(:disabled) {
          background-color: #c82333;
        }

        &:disabled {
          background-color: #6c757d;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .delete-dialog-container {
    .dialog-content {
      .confirmation-message {
        .warning-message {
          flex-direction: row-reverse;
        }
      }
    }

    .dialog-actions {
      .btn {
        flex-direction: row-reverse;
      }
    }
  }
}
