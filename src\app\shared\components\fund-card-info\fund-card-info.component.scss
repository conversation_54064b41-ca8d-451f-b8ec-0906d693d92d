@import "../../../../assets/scss/variables";

.fund-card-info {
  background-color: #fff;
  border: 1px solid #eaeef1;
  border-radius: 10px;
  padding: 16px;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;


  &.disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }

  .card-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-direction: row-reverse;
    margin-bottom: 33px;

     @media (max-width: 480px) {
       margin-bottom: 0;
      }
       .notification-container {
              @media (max-width: 480px) {
                 img{
          width: 21px;
          height: 24px;
          }
        }

      @media (max-width: 380px) {
          img{
          width: 21px;
          height: 24px;
          }
        }
            }


    .icon-container {


      @media (max-width: 480px) {
        svg {
          width: 24px;
          height: 24px;
        }

        .title {
          font-size: 14px;
        }

        .notification-container {
          width: 21px;
          height: 24px;

        }
      }
  @media (max-width: 380px) {

 .title {
          font-size: 11px;
        }
    }

    }

    .notification-container {
      position: relative;
      display: inline-block;
      font-size: 24px;
      color: #333;
    }

    .notification-badge {
      background-color: #dc3545;
      color: white;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      position: absolute;
      top: -10px;
      right: 10px;

  @media (max-width: 480px) {
      width: 20px;
      height: 20px;
       top: -5px;
      right: 10px;

        }

      @media (max-width: 380px) {
          width: 20px;
          height: 20px;
          top: -5px;
          right: 10px;
        }
    }

    i {
      font-size: 24px;
      color: #00205a;
    }
  }

  .title {
    color: #333;
    font-size: 22px;
    font-weight: 700;
    margin: 0 0 8px 0;
  }

  .card-content {
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;

    .count {
      color: #00205a;
      font-size: 32px;
      font-weight: bold;
      // margin: auto;
      margin-inline: 60px;

        @media (max-width: 480px) {

      margin-inline: 0;

        }

      @media (max-width: 380px) {
              margin-inline: 0;

      }
    }
  }
}
