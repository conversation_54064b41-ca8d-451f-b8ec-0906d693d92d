<div class="dialog-container">
  <h2 class=" header title">
    {{'INVESTMENT_FUNDS.MEMBERS.EDIT_MEMBER' | translate}} : {{ member.memberName}}
  </h2>
  <hr>
  <div class="form-container">
    <!-- Warning message for member type restriction -->
    <!-- <div *ngIf="showMemberTypeWarning" class="warning-message">
      <img src="assets/icons/warning-icon.png" alt="warning" />
      <span>{{'INVESTMENT_FUNDS.MEMBERS.MEMBER_TYPE_RESTRICTION_WARNING' | translate}}</span>
    </div> -->

      <div class="w-100 flex-grow-1 flex-lg-grow-0" *ngIf="showMemberTypeWarning">
    <app-alert [hasClose]="false" [isStaticPosition]="true"  [alertType]="AlertType.Warning" [msg]="'INVESTMENT_FUNDS.MEMBERS.MEMBER_TYPE_RESTRICTION_WARNING' | translate"></app-alert>
  </div>
    <div class="form-container mt-3 p-0">

    <app-form-builder
      (dropdownChanged)="dropdownChanged($event)"
      [formGroup]="formGroup"
      [formControls]="formControls"
      [isFormSubmitted]="isValidationFire"
      (formSubmit)="onSubmit($event)">
    </app-form-builder>
  </div>
  </div>
  <hr>
  <div class="dialog-actions">
    <!-- <button class="btn cancel-btn w-50" (click)="onClose()" type="button">
      {{'INVESTMENT_FUNDS.MEMBERS.CANCEL' | translate}}
    </button>

    <button
      class="btn save-btn w-50"
      type="button"
      [disabled]="isSubmitting"
      (click)="onSubmit($event)">
      <span *ngIf="!isSubmitting">{{'INVESTMENT_FUNDS.MEMBERS.SAVE_CHANGES' | translate}}</span>
      <span *ngIf="isSubmitting">{{'COMMON.SAVING' | translate}}</span>
    </button> -->




     <button class="btn cancel-btn w-50" (click)="onClose()" type="button">
        <img src="assets/icons/cancel-icon.png" class="mx-2" alt="verify">

        {{'INVESTMENT_FUNDS.MEMBERS.CANCEL' | translate}}

      </button>
      <button type="button" class="btn primary-btn w-50"
        (click)="onSubmit($event)" >
        <img src="assets/icons/verify-icon.png" class="mx-2" alt="verify">
        <span *ngIf="!isSubmitting">{{'INVESTMENT_FUNDS.MEMBERS.SAVE_CHANGES' | translate}}</span>
        <span *ngIf="isSubmitting">{{'COMMON.SAVING' | translate}}</span>
      </button>
  </div>
</div>
