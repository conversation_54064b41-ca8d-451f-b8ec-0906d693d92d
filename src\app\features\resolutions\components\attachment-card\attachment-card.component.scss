.attachment-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e0e6ed;
  border-radius: 12px;
  padding: 12px 10px;
  background-color: #fff;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.02);
  // max-width: 320px;

  .file-download{
    width: 80%;

    .file-info {
      display: flex;
      flex-direction: column;
      color: #002b55;
      align-items: start;
      cursor: pointer;
      width: 85%;

      .file-name {
        font-weight: 600;
        font-size: 16px;
        color: #00205a;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
      }
      .file-size {
        color: #4f4f4f;
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        line-height: 10px;
      }
    }
  }
}

.download-icon {
  font-size: 22px;
  cursor: pointer;
  color: #002b55;
  transition: transform 0.2s ease;
}

// .download-icon:hover {
//   transform: scale(1.2);
// }

.remove-icon {
  // width: 24px;
  // height: 24px;
  cursor: pointer;
  // margin-left: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  // &:hover:not(.disabled) {
  //   transform: scale(1.1);
  // }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  img {
    width: 100%;
    height: 100%;
    filter: hue-rotate(0deg) saturate(1.5) brightness(0.8);
  }

  .spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #ff4757;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// @keyframes spin {
//   0% { transform: rotate(0deg); }
//   100% { transform: rotate(360deg); }
// }
