import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

export interface AnalyticsCardItem {
  title: string;
  value: number;
  icon: string;
  color: 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'secondary';
}

@Component({
  selector: 'app-dashboard-analytics-card',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './dashboard-analytics-card.component.html',
  styleUrl: './dashboard-analytics-card.component.scss'
})
export class DashboardAnalyticsCardComponent {
  @Input() title: string = '';
  @Input() items: AnalyticsCardItem[] = [];
  @Input() cardType: 'fund' | 'resolution-status' | 'resolution-votes' | 'assessment' | 'document' = 'fund';
  @Input() showChart: boolean = false;

  /**
   * Get the main item (usually the first one with total count)
   */
  get mainItem(): AnalyticsCardItem | null {
    return this.items.length > 0 ? this.items[0] : null;
  }

  /**
   * Get sub items (excluding the main item)
   */
  get subItems(): AnalyticsCardItem[] {
    return this.items.slice(1);
  }

  /**
   * Get card CSS class based on type
   */
  get cardClass(): string {
    const baseClass = 'dashboard-analytics-card';
    return `${baseClass} ${baseClass}--${this.cardType}`;
  }

  /**
   * Get color class for item
   */
  getItemColorClass(color: string): string {
    return `text-${color}`;
  }

  /**
   * Get background color class for item
   */
  getItemBgClass(color: string): string {
    return `bg-${color}`;
  }

  /**
   * Calculate percentage for sub items
   */
  getPercentage(value: number): number {
    if (!this.mainItem || this.mainItem.value === 0) return 0;
    return Math.round((value / this.mainItem.value) * 100);
  }

  /**
   * Get chart data for visualization
   */
  getChartData(): any {
    if (!this.showChart || this.subItems.length === 0) return null;

    return {
      labels: this.subItems.map(item => item.title),
      data: this.subItems.map(item => item.value),
      colors: this.subItems.map(item => this.getColorHex(item.color))
    };
  }

  /**
   * Convert color name to hex value
   */
  private getColorHex(color: string): string {
    const colorMap: { [key: string]: string } = {
      'primary': '#0d6efd',
      'success': '#198754',
      'info': '#0dcaf0',
      'warning': '#ffc107',
      'danger': '#dc3545',
      'secondary': '#6c757d'
    };
    return colorMap[color] || '#6c757d';
  }

  /**
   * TrackBy function for items
   */
  trackByTitle(index: number, item: AnalyticsCardItem): string {
    return item.title;
  }
}
