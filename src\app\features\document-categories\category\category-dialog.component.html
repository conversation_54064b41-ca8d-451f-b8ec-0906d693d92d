<div class="dialog-container" (click)="markAllFieldsTouched($event)">
  <h2 class=" header mb-5">
    {{ data.isEdit ? ('Document_Categories.EDIT_Document_Categories' | translate) :
    ('Document_Categories.ADD_Document_Categories' | translate) }}
  </h2>

  <div class="form-fields">
    <div class="form-container">
      <label class="required mb-3 ">{{'Document_Categories.Document_Categories_NAME_AR' | translate}} </label>
      <input
        class="form-control form-control-solid"
        type="text"
        [(ngModel)]="arabicName"
        name="arabicName"
        #arabicNameRef="ngModel"
        placeholder="{{'Document_Categories.Document_Categories_NAME_AR' | translate}}"
        maxlength="50"
        pattern="^[a-zA-Z\u0600-\u06FF\s]{1,50}$"
        required
        />
      <div *ngIf="arabicNameRef.invalid && arabicNameRef.touched"
        class="text-danger">
        <div *ngIf="arabicNameRef.errors?.['required']">{{'FORM.ERROR_REQUIRED' | translate}}</div>
        <div *ngIf="arabicNameRef.errors?.['pattern']">{{'FORM.ERROR_PATTERN' | translate}}</div>
        <div class="text-danger" *ngIf="arabicNameRef.errors?.['duplicate']">
          {{'FORM.ERROR_DUPLICATION' | translate}}
        </div>
      </div>
    </div>

    <div class="form-container">
      <label class="required mb-3">
        {{'Document_Categories.Document_Categories_Name_En' | translate}}
    </label>
      <input class="form-control form-control-solid" type="text"
        [(ngModel)]="englishName" placeholder="{{'Document_Categories.Document_Categories_Name_En' | translate}}" maxlength="50"
        pattern="^[a-zA-Z\u0600-\u06FF\s]{1,50}$"  #englishNameRef="ngModel"
        required>
        <div *ngIf="englishNameRef.invalid && englishNameRef.touched"
        class="text-danger">
        <div *ngIf="englishNameRef.errors?.['required']">{{'FORM.ERROR_REQUIRED' | translate}}</div>
        <div *ngIf="englishNameRef.errors?.['pattern']">{{'FORM.ERROR_PATTERN' | translate}}</div>
        <div class="text-danger" *ngIf="englishNameRef.errors?.['duplicate']">

          {{'FORM.ERROR_DUPLICATION' | translate}}
        </div>
      </div>
    </div>

     <div class="form-container">
      <label class="required mb-3">
        {{'Document_Categories.DISPLAY_ORDER' | translate}}
    </label>
      <input class="form-control form-control-solid" type="text"
        [(ngModel)]="displayOrder" placeholder="{{'Document_Categories.DISPLAY_ORDER' | translate}}" maxlength="50"
        pattern="^[a-zA-Z\u0600-\u06FF\s]{1,50}$"  #displayOrderRef="ngModel"
        required>
        <div
        class="text-danger">
        <div *ngIf="englishNameRef.errors?.['required']">{{'FORM.ERROR_REQUIRED' | translate}}</div>


      </div>
    </div>
  </div>

  <div class="dialog-actions">
    <button class="btn cancel-btn w-50" (click)="onCancel()">
      <img src="assets/icons/cancel-icon.png" class="mx-2" alt="verify">
      {{ 'Document_Categories.CANCEL' | translate }}
    </button>
    <button class="btn primary-btn w-50" (click)="onSubmit()">
      <img src="assets/icons/verify-icon.png" class="mx-2" alt="verify">
      {{ data.isEdit ? ('Document_Categories.SAVE' | translate) : ('Document_Categories.ADD' | translate) }}
      <!-- {{ ('FUND_STRATEGIES.SAVE' | translate) }} -->
    </button>
  </div>
</div>


