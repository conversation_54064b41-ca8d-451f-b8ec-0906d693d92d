import { Pipe, PipeTransform } from '@angular/core';
import { DateTime } from 'luxon';
import { LanguageService } from '@core/gl-services/language-services/language.service';

@Pipe({
  name: 'georgianDate',
  standalone: true,
  pure: false

})
export class GeorgianDatePipe implements PipeTransform {

  // English month names
  private englishMonths = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Arabic month names
  private arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  private currentLang: string = 'ar';

  constructor(private languageService: LanguageService) {
    this.currentLang = this.languageService.currentLang;

    this.languageService.currentLang$.subscribe(lang => {
      this.currentLang = lang;
    });
  }

  transform(dateTime: string | DateTime | Date, format: 'short' | 'long' = 'long'): string {
    if (!dateTime) return '';

    try {
      let date: Date;

      // Convert input to Date object
      if (typeof dateTime === 'string') {
        date = new Date(dateTime);
      } else if (dateTime instanceof DateTime) {
        date = dateTime.toJSDate();
      } else {
        date = dateTime;
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        console.warn('Invalid date provided to georgianDate pipe:', dateTime);
        return '';
      }

      const day = date.getDate();
      const month = date.getMonth(); // 0-based
      const year = date.getFullYear();

      // Get month names based on current language
      const months = this.currentLang === 'ar' ? this.arabicMonths : this.englishMonths;
      const monthName = months[month];

      if (format === 'short') {
        // Format: "Jan 15, 2024" or "يناير 15, 2024"
        const shortMonthName = this.currentLang === 'ar' ? monthName : monthName.substring(0, 3);
        return `${shortMonthName} ${day}, ${year}`;
      } else {
        // Format: "January 15, 2024" or "15 يناير 2024"
        if (this.currentLang === 'ar') {
          return `${day} ${monthName} ${year}`;
        } else {
          return `${day} ${monthName} ${year}`;
        }
      }
    } catch (error) {
      console.error('Error in georgianDate pipe:', error, 'for date:', dateTime);
      return '';
    }
  }
}
