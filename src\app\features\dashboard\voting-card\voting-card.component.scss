@import "../../../../assets/scss/variables";

.voting-container {
  background-color: $card-background ;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 8px 24px;
  max-width: 100%;
}

.voting-header {
  text-align: right;
  margin-bottom: 20px;


  .user-info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 12px;

    .profile-image {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-left: 12px;
      margin: 0 30px 0 0;
    }

    .user-name {
      font-size: 16px;

      @media (max-width: 320px) {
              font-size: 10px;

      }


      span {
        font-weight: 300;
      }
    }
  }

  hr {
    color: $border-color;
    border: 1px solid;
    margin: 12px 0 14px 0;
  }


  .voting-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
    text-align: center;

  }

  .fund-select {
      width: 200px;
      height: 16px;
      padding: 10px;
      font-size: 16px;
      border: 2px solid transparent;
      border-radius: 4px;
      background: transparent;
      display: flex;
      justify-content: center;
      align-items: center;
    margin: auto;
    margin-bottom: 14px;
    margin-top: 4px;
    }
}

.voting-content {
  text-align: center;

  .fund-selection {
    text-align: right;
    margin-bottom: 1rem;

    .form-label {
      display: block;
      margin-bottom: 0.5rem;
    }



    ::ng-deep .ng-select.ng-select-single .ng-select-container,
    .ng-select.ng-select-opened.ng-select-top>.ng-select-container {
      border: 1px solid transparent;
      background-color: transparent;
    }


    .fund-dropdown {
      position: relative;
      display: inline-block;
      width: 100%;
      max-width: 300px;

      // Dropdown Trigger
      .dropdown-trigger {
        background: transparent;
        border: 1px solid transparent;
        padding: 8px 12px;
        cursor: pointer;
        outline: none;
        width: 100%;
        transition: all 0.2s ease;
        border-radius: 6px;
        min-height: 40px;
        display: flex;
        align-items: center;

        &:hover:not(.fund-dropdown--disabled &) {
          background: #f8f9fa;
          border-color: #dee2e6;
        }

        &:focus {
          outline: 2px solid #0d6efd;
          outline-offset: 2px;
          border-radius: 4px;
        }

        .selected-fund {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 8px;
          min-height: 32px;

          .fund-name {
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            text-align: right;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &.placeholder {
              color: #6c757d;
              font-style: italic;
            }
          }

          .loading-spinner {
            display: flex;
            align-items: center;

            .spinner-border-sm {
              width: 1rem;
              height: 1rem;
              border-width: 0.1em;
            }
          }

          .dropdown-arrow {
            display: flex;
            align-items: center;
            color: #6c757d;
            transition: transform 0.2s ease;

            i {
              font-size: 0.8rem;
            }

            &.rotated {
              transform: rotate(180deg);
            }
          }
        }
      }

      // Dropdown Menu
      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1050;
        margin-top: 4px;
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        max-height: 300px;
        overflow: hidden;
        animation: dropdownFadeIn 0.2s ease;

        .dropdown-content {
          max-height: 300px;
          overflow-y: auto;
          padding: 4px 0;

          // Custom scrollbar
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f8f9fa;
          }

          &::-webkit-scrollbar-thumb {
            background: #dee2e6;
            border-radius: 3px;

            &:hover {
              background: #adb5bd;
            }
          }
        }

        .dropdown-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          cursor: pointer;
          transition: all 0.2s ease;
          border-bottom: 1px solid #f8f9fa;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background: #f8f9fa;
          }

          &.selected {
            background: #e3f2fd;
            border-left: 3px solid #0d6efd;
            padding-left: 13px;
          }

          .fund-info {
            flex: 1;
            text-align: right;

            .fund-name {
              font-size: 0.9rem;
              font-weight: 500;
              color: #2c3e50;
              margin-bottom: 2px;
              line-height: 1.3;
            }

            .fund-id {
              font-size: 0.75rem;
              color: #6c757d;
            }
          }

          .selected-indicator {
            margin-left: 8px;

            i {
              font-size: 0.9rem;
            }
          }
        }

        .dropdown-empty {
          padding: 20px;
          text-align: center;

          .empty-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            i {
              font-size: 1.5rem;
              opacity: 0.5;
            }

            .empty-text {
              font-size: 0.85rem;
              color: #6c757d;
            }
          }
        }
      }

      // Dropdown Backdrop (for mobile)
      .dropdown-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1040;
        background: transparent;
      }

      // State Modifiers
      &.fund-dropdown--loading {
        .dropdown-trigger {
          cursor: wait;
        }
      }

      &.fund-dropdown--disabled {
        .dropdown-trigger {
          cursor: not-allowed;
          opacity: 0.6;

          .selected-fund .fund-name {
            color: #6c757d;
          }
        }
      }

      &.fund-dropdown--empty {
        .dropdown-trigger {
          cursor: default;
        }
      }
    }
  }

  .loading-text {
    color: #6c757d;
    font-style: italic;
  }

  .remaining-elements {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
  }

  .vote-button {
    //background-color: #0f3c6c;
    //color: white;
    // border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    padding: 5px 16.5px 9px;
    transition: background-color 0.2s ease;
    font-size: 16px;
    font-weight: 400;
    line-height: 10px;
    margin-top: 14px !important;

    &:hover:not(:disabled) {
      background-color: #0a2d52;
    }

    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }
}
