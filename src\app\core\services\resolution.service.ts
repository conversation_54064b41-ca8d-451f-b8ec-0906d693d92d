import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResolutionMembersDtoBaseResponse, SingleResolutionResponseBaseResponse } from '@core/api/api.generated';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ResolutionService {

baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

    getResolutionById(id: number): Observable<SingleResolutionResponseBaseResponse> {
      const url = `${this.baseUrl}/api/Resolutions/GetResolutionById/${id}`;
      return this.http.get<SingleResolutionResponseBaseResponse>(url);
    }

    getResolutionMemberVoteByResolutionId(id: number, memberId?: number): Observable<ResolutionMembersDtoBaseResponse> {
      let url = `${this.baseUrl}/api/ResolutionMemberVote/MemberVote?id=${id}`;
       if (memberId !== undefined) {
             url += `&memberId=${memberId}`;
        }
            return this.http.get<ResolutionMembersDtoBaseResponse>(url);
    }
     GetResolutionStatusById(id: number): Observable<SingleResolutionResponseBaseResponse> {
      const url = `${this.baseUrl}/api/Resolutions/GetResolutionStatusById/${id}`;
      return this.http.get<SingleResolutionResponseBaseResponse>(url);

    }
}
