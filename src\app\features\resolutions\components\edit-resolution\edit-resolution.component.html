<div class="edit-resolution-page" >
  <!-- Breadcrumb -->
  <!-- <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div> -->

  <!-- Page Header -->
  <div class="page-header-section">
    <app-page-header
      [title]="title"
      [showCreateButton]="false"
      [showSearch]="false"
      [showFilter]="false"
      [showSubHeader]="currentResolution!= null && currentResolution.parentResolutionId != null"
      [subHeaderValue]="currentResolution != null ? 'INVESTMENT_FUNDS.RESOLUTIONS.REFERRED_RESOLUTION_CODE+#'+ currentResolution.parentResolutionCode : ''"
      (subHeaderClick)="onReferralCodeClick()">
    </app-page-header>
  </div>



  <!-- Main Content -->
  <div class="main-content" *ngIf="!isLoading && currentResolution">

    <!-- Edit Form -->
    <div class="edit-form-container">

        <!-- Form Container using reusable form-builder -->
        <div class="form-section">
          <!-- Form Builder Component -->
          <app-form-builder
            (dropdownChanged)="dropdownChanged($event)"
            [formControls]="formControls"
            [formGroup]="formGroup"
            [isFormSubmitted]="isValidationFire"
            (dateSelected)="dateSelected($event)"
            (fileUploaded)="onFileUpload($event)"
            (valueChanged)="onValueChange($event, $event.control)">
            <p slot="top" class="header mt-2">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.BASIC_INFO' | translate }}</p>
            <!-- <div slot="between" class="hr-first-container">
              <hr class="hr-first"/>
            </div> -->
            <!-- <ng-template #customTemplate let-element="element">
              <div class="custom-section">
                <p class="header">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}</p>
                <hr class="hr-first" />
              </div>
            </ng-template> -->

          </app-form-builder>
        </div>


          <!-- Resolution Items Section -->
          <div class="form-section mb-3" *ngIf="CanAddItems">
            <div class="section-header">
              <div class="header-content">
                <div class="d-flex align-items-center gap-2">
                  <h6>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_ITEMS' | translate }}</h6><span class="items-num" *ngIf="resolutionItems.length">{{resolutionItems.length}} {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEMS' | translate }}</span>
                </div>
                <button
                        *ngIf="resolutionItems.length"
                        type="button"
                        class="btn add-item-btn"
                        (click)="addResolutionItem()"
                        [disabled]="isSubmitting">
                  <!-- <i class="fas fa-plus"></i> -->

                  <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13.0855 0.5H3.91447C2.8772 0.502951 1.88325 0.916315 1.14978 1.64978C0.416315 2.38325 0.00295102 3.3772 0 4.41447L0 13.5855C0.00295102 14.6228 0.416315 15.6167 1.14978 16.3502C1.88325 17.0837 2.8772 17.497 3.91447 17.5H13.0855C14.1228 17.497 15.1167 17.0837 15.8502 16.3502C16.5837 15.6167 16.997 14.6228 17 13.5855V4.41447C16.997 3.3772 16.5837 2.38325 15.8502 1.64978C15.1167 0.916315 14.1228 0.502951 13.0855 0.5ZM11.9906 9.73257H9.41039C9.39052 9.73242 9.37082 9.73622 9.35244 9.74376C9.33405 9.75129 9.31735 9.76241 9.3033 9.77646C9.28925 9.79051 9.27813 9.80721 9.2706 9.8256C9.26306 9.84398 9.25926 9.86368 9.25941 9.88355V12.4671C9.25941 12.6685 9.1794 12.8617 9.03698 13.0041C8.89457 13.1465 8.70141 13.2265 8.5 13.2265C8.29859 13.2265 8.10543 13.1465 7.96302 13.0041C7.8206 12.8617 7.74059 12.6685 7.74059 12.4671V9.88355C7.74074 9.86368 7.73694 9.84398 7.7294 9.8256C7.72187 9.80721 7.71075 9.79051 7.6967 9.77646C7.68265 9.76241 7.66595 9.75129 7.64756 9.74376C7.62918 9.73622 7.60947 9.73242 7.58961 9.73257H5.00941C4.90968 9.73257 4.81093 9.71292 4.71879 9.67476C4.62666 9.6366 4.54294 9.58066 4.47243 9.51014C4.40191 9.43962 4.34597 9.35591 4.30781 9.26377C4.26964 9.17163 4.25 9.07288 4.25 8.97316C4.25 8.87343 4.26964 8.77468 4.30781 8.68255C4.34597 8.59041 4.40191 8.50669 4.47243 8.43618C4.54294 8.36566 4.62666 8.30972 4.71879 8.27156C4.81093 8.23339 4.90968 8.21375 5.00941 8.21375H7.58961C7.60947 8.2139 7.62918 8.21009 7.64756 8.20256C7.66595 8.19502 7.68265 8.18391 7.6967 8.16986C7.71075 8.15581 7.72187 8.1391 7.7294 8.12072C7.73694 8.10233 7.74074 8.08263 7.74059 8.06276V5.48257C7.74059 5.28116 7.8206 5.088 7.96302 4.94558C8.10543 4.80317 8.29859 4.72316 8.5 4.72316C8.70141 4.72316 8.89457 4.80317 9.03698 4.94558C9.1794 5.088 9.25941 5.28116 9.25941 5.48257V8.06276C9.25881 8.08291 9.26227 8.10297 9.26961 8.12175C9.27694 8.14053 9.28798 8.15763 9.30208 8.17204C9.31617 8.18645 9.33303 8.19787 9.35164 8.20561C9.37026 8.21336 9.39024 8.21726 9.41039 8.2171H11.9906C12.192 8.2171 12.3852 8.29711 12.5276 8.43953C12.67 8.58195 12.75 8.77511 12.75 8.97651C12.75 9.17792 12.67 9.37108 12.5276 9.5135C12.3852 9.65591 12.192 9.73592 11.9906 9.73592V9.73257Z" fill="#00205A"/>
                  </svg>
                  {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ITEM' | translate }}
                </button>
              </div>
            </div>
          </div>

          <!-- Items List -->
          <div class="items-container resolution-items" *ngIf="resolutionItems.length > 0">
            <div class="resolution-item-card" *ngFor="let item of resolutionItems; let i = index">
              <div class="item-header">
                <div class="item-info">
                  <div class="item-number">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEM' | translate }} {{ i + 1 }}</div>
                  <div class="item-actions">
                    <div class="item-conflict" *ngIf="item.hasConflict">
                      <!-- <div class="conflict-indicator">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.HAS_CONFLICT' | translate }}</span>
                      </div> -->
                      <button type="button"
                              class="btn view-conflict-btn"
                              (click)="viewConflictMembers(item)"
                              *ngIf="item.conflictMembersCount > 0">
                        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.HAS_CONFLICT' | translate }}
                        ({{ item.conflictMembersCount }} {{ 'INVESTMENT_FUNDS.RESOLUTIONS.MEMBERS' | translate }})
                      </button>
                    </div>
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary edit-btn"
                            (click)="editResolutionItem(item, i)"
                            title="{{ 'COMMON.EDIT' | translate }}">
                      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.3511 11.3427L9.3468 22.3593C9.29139 22.4149 9.2519 22.4844 9.23245 22.5605L8.01269 27.4614C7.99475 27.5341 7.99585 27.6102 8.01588 27.6824C8.03591 27.7546 8.0742 27.8204 8.12705 27.8734C8.20822 27.9544 8.31812 27.9999 8.43272 28C8.46807 28 8.50329 27.9956 8.53757 27.987L13.4333 26.7658C13.5094 26.7466 13.5789 26.7071 13.6343 26.6515L24.6396 15.6356L20.3511 11.3427ZM27.3658 9.84056L26.1408 8.61436C25.3221 7.79481 23.8952 7.79562 23.0775 8.61436L21.577 10.1165L25.8653 14.4092L27.3658 12.9072C27.7747 12.498 28 11.9533 28 11.374C28 10.7947 27.7747 10.25 27.3658 9.84056Z" fill="#EAA300"/>
                      </svg>
                    </button>
                    <button type="button"
                            class="btn btn-sm btn-outline-danger delete-btn"
                            (click)="deleteResolutionItem(item, i)"
                            title="{{ 'COMMON.DELETE' | translate }}">
                      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21.3333 13.0003V12.3337C21.3333 11.4002 21.3333 10.9335 21.1517 10.577C20.9919 10.2634 20.7369 10.0084 20.4233 9.84865C20.0668 9.66699 19.6001 9.66699 18.6667 9.66699H17.3333C16.3999 9.66699 15.9332 9.66699 15.5767 9.84865C15.2631 10.0084 15.0081 10.2634 14.8483 10.577C14.6667 10.9335 14.6667 11.4002 14.6667 12.3337V13.0003M16.3333 17.5837V21.7503M19.6667 17.5837V21.7503M10.5 13.0003H25.5M23.8333 13.0003V22.3337C23.8333 23.7338 23.8333 24.4339 23.5608 24.9686C23.3212 25.439 22.9387 25.8215 22.4683 26.0612C21.9335 26.3337 21.2335 26.3337 19.8333 26.3337H16.1667C14.7665 26.3337 14.0665 26.3337 13.5317 26.0612C13.0613 25.8215 12.6788 25.439 12.4392 24.9686C12.1667 24.4339 12.1667 23.7338 12.1667 22.3337V13.0003" stroke="#C50F1F" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <div class="item-body">
                <!-- <div class="item-title" *ngIf="item.title">{{ item.title }}</div> -->
                <div class="item-description" *ngIf="item.description">{{ item.description }}</div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="empty-items d-flex align-items-center justify-content-center" *ngIf="resolutionItems.length === 0 && CanAddItems">
            <!-- <div class="empty-icon">
              <i class="fas fa-list-ul"></i>
            </div> -->
            <!-- <p class="empty-message">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_ITEMS_YET' | translate }}</p> -->
            <button type="button" class="btn add-item-btn" (click)="addResolutionItem()"
              [disabled]="isSubmitting">
              <!-- <i class="fas fa-plus"></i> -->

              <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M13.0855 0.5H3.91447C2.8772 0.502951 1.88325 0.916315 1.14978 1.64978C0.416315 2.38325 0.00295102 3.3772 0 4.41447L0 13.5855C0.00295102 14.6228 0.416315 15.6167 1.14978 16.3502C1.88325 17.0837 2.8772 17.497 3.91447 17.5H13.0855C14.1228 17.497 15.1167 17.0837 15.8502 16.3502C16.5837 15.6167 16.997 14.6228 17 13.5855V4.41447C16.997 3.3772 16.5837 2.38325 15.8502 1.64978C15.1167 0.916315 14.1228 0.502951 13.0855 0.5ZM11.9906 9.73257H9.41039C9.39052 9.73242 9.37082 9.73622 9.35244 9.74376C9.33405 9.75129 9.31735 9.76241 9.3033 9.77646C9.28925 9.79051 9.27813 9.80721 9.2706 9.8256C9.26306 9.84398 9.25926 9.86368 9.25941 9.88355V12.4671C9.25941 12.6685 9.1794 12.8617 9.03698 13.0041C8.89457 13.1465 8.70141 13.2265 8.5 13.2265C8.29859 13.2265 8.10543 13.1465 7.96302 13.0041C7.8206 12.8617 7.74059 12.6685 7.74059 12.4671V9.88355C7.74074 9.86368 7.73694 9.84398 7.7294 9.8256C7.72187 9.80721 7.71075 9.79051 7.6967 9.77646C7.68265 9.76241 7.66595 9.75129 7.64756 9.74376C7.62918 9.73622 7.60947 9.73242 7.58961 9.73257H5.00941C4.90968 9.73257 4.81093 9.71292 4.71879 9.67476C4.62666 9.6366 4.54294 9.58066 4.47243 9.51014C4.40191 9.43962 4.34597 9.35591 4.30781 9.26377C4.26964 9.17163 4.25 9.07288 4.25 8.97316C4.25 8.87343 4.26964 8.77468 4.30781 8.68255C4.34597 8.59041 4.40191 8.50669 4.47243 8.43618C4.54294 8.36566 4.62666 8.30972 4.71879 8.27156C4.81093 8.23339 4.90968 8.21375 5.00941 8.21375H7.58961C7.60947 8.2139 7.62918 8.21009 7.64756 8.20256C7.66595 8.19502 7.68265 8.18391 7.6967 8.16986C7.71075 8.15581 7.72187 8.1391 7.7294 8.12072C7.73694 8.10233 7.74074 8.08263 7.74059 8.06276V5.48257C7.74059 5.28116 7.8206 5.088 7.96302 4.94558C8.10543 4.80317 8.29859 4.72316 8.5 4.72316C8.70141 4.72316 8.89457 4.80317 9.03698 4.94558C9.1794 5.088 9.25941 5.28116 9.25941 5.48257V8.06276C9.25881 8.08291 9.26227 8.10297 9.26961 8.12175C9.27694 8.14053 9.28798 8.15763 9.30208 8.17204C9.31617 8.18645 9.33303 8.19787 9.35164 8.20561C9.37026 8.21336 9.39024 8.21726 9.41039 8.2171H11.9906C12.192 8.2171 12.3852 8.29711 12.5276 8.43953C12.67 8.58195 12.75 8.77511 12.75 8.97651C12.75 9.17792 12.67 9.37108 12.5276 9.5135C12.3852 9.65591 12.192 9.73592 11.9906 9.73592V9.73257Z"
                  fill="#00205A" />
              </svg>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ITEM' | translate }}
            </button>
          </div>
        <!-- Form Actions -->
        <div class="form-actions">
          <div class="actions-container">
            <button type="button"
                    class="btn btn-secondary cancel-btn"
                    (click)="onCancel()">
              <!-- <i class="fas fa-times"></i> -->
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M2.0062 2.7529C2.22033 2.53883 2.51072 2.41857 2.8135 2.41857C3.11629 2.41857 3.40667 2.53883 3.62081 2.7529L9.66474 8.79683L15.7087 2.7529C15.814 2.64384 15.94 2.55685 16.0793 2.497C16.2186 2.43716 16.3685 2.40566 16.5201 2.40434C16.6717 2.40302 16.8221 2.43191 16.9624 2.48933C17.1027 2.54674 17.2302 2.63153 17.3374 2.73874C17.4446 2.84596 17.5294 2.97345 17.5868 3.11378C17.6443 3.25411 17.6731 3.40447 17.6718 3.55609C17.6705 3.70771 17.639 3.85754 17.5792 3.99686C17.5193 4.13617 17.4323 4.26217 17.3233 4.3675L11.2793 10.4114L17.3233 16.4554C17.5313 16.6707 17.6464 16.9592 17.6438 17.2586C17.6412 17.558 17.5211 17.8443 17.3094 18.0561C17.0977 18.2678 16.8113 18.3879 16.5119 18.3905C16.2125 18.3931 15.924 18.278 15.7087 18.07L9.66474 12.026L3.62081 18.07C3.40545 18.278 3.11701 18.3931 2.81761 18.3905C2.51822 18.3879 2.23182 18.2678 2.02011 18.0561C1.8084 17.8443 1.68831 17.558 1.68571 17.2586C1.6831 16.9592 1.7982 16.6707 2.0062 16.4554L8.05013 10.4114L2.0062 4.3675C1.79213 4.15337 1.67188 3.86298 1.67188 3.5602C1.67188 3.25742 1.79213 2.96703 2.0062 2.7529Z" fill="#4F4F4F"/>
              </svg>
              {{ 'COMMON.CANCEL' | translate }}
            </button>

            <button type="button"
                    class="btn btn-outline-primary save-draft-btn"
                    *ngIf="canSaveAsDraft"
                    (click)="onSubmit(true)"
                    [disabled]="isSubmitting">
              <!-- <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
              <i class="fas fa-save" *ngIf="!isSubmitting"></i> -->
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_4189_40187)">
                <path d="M19.3284 3.9358L15.5627 0.170176C15.4529 0.0602883 15.3038 -0.00145238 15.1484 -0.00146484L1.65492 -0.00146484C0.466719 -0.00146484 -0.5 0.965254 -0.5 2.1535V17.8436C-0.5 19.0318 0.466719 19.9985 1.65492 19.9985H17.345C18.5333 19.9985 19.5 19.0318 19.5 17.8436V4.3501C19.5 4.1947 19.4382 4.04568 19.3284 3.9358ZM13.3073 1.17041V5.91908C13.3073 6.46115 12.8663 6.90217 12.3242 6.90217H5.42055C4.87848 6.90217 4.43746 6.46115 4.43746 5.91908V1.17041H13.3073ZM4.43746 18.8267V12.8228C4.43746 12.2807 4.87848 11.8397 5.42055 11.8397H13.5794C14.1214 11.8397 14.5625 12.2807 14.5625 12.8228V18.8267H4.43746ZM18.3281 17.8436C18.3281 18.3856 17.8871 18.8267 17.345 18.8267H15.7344V12.8228C15.7344 11.6346 14.7677 10.6678 13.5794 10.6678H5.42055C4.2323 10.6678 3.26559 11.6346 3.26559 12.8228V18.8267H1.65492C1.11289 18.8267 0.671875 18.3856 0.671875 17.8436V2.1535C0.671875 1.61143 1.11289 1.17041 1.65492 1.17041H3.26559V5.91908C3.26559 7.10732 4.2323 8.07404 5.42055 8.07404H12.3242C13.5125 8.07404 14.4792 7.10732 14.4792 5.91908V1.17041H14.9057L18.3281 4.59283V17.8436Z" fill="#00205A"/>
                <path d="M11.3828 5.56372C11.7064 5.56372 11.9688 5.30138 11.9688 4.97778V3.09497C11.9688 2.77138 11.7064 2.50903 11.3828 2.50903C11.0592 2.50903 10.7969 2.77138 10.7969 3.09497V4.97778C10.7969 5.30138 11.0592 5.56372 11.3828 5.56372Z" fill="#00205A"/>
                </g>
                <defs>
                <clipPath id="clip0_4189_40187">
                <rect width="20" height="20" fill="white"/>
                </clipPath>
                </defs>
              </svg>
              {{ draftButtonTitle }}
            </button>

            <button type="submit"
                    class="btn btn-primary submit-btn"
                    *ngIf="canSend"
                    (click)="onSubmit(false)"
                    [disabled]="isSubmitting">
              <!-- <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
              <i class="fas fa-paper-plane" *ngIf="!isSubmitting"></i> -->
              <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.74121 9.48291C3.41817 9.48382 3.10198 9.57617 2.82923 9.74928C2.55648 9.92239 2.33831 10.1692 2.19997 10.4611C2.06163 10.753 2.00877 11.0782 2.0475 11.3989C2.08623 11.7196 2.21497 12.0228 2.41883 12.2734L6.76462 17.597C6.91957 17.7894 7.11819 17.942 7.34398 18.0423C7.56977 18.1425 7.81622 18.1874 8.06285 18.1732C8.59033 18.1449 9.06656 17.8627 9.37019 17.3988L18.3975 2.86031C18.399 2.85788 18.4005 2.85548 18.4021 2.85311C18.4868 2.72306 18.4593 2.46532 18.2845 2.30342C18.2365 2.25895 18.1799 2.22479 18.1181 2.20304C18.0564 2.18129 17.9909 2.1724 17.9256 2.17693C17.8603 2.18146 17.7966 2.19931 17.7385 2.22937C17.6803 2.25944 17.629 2.30109 17.5876 2.35176C17.5843 2.35574 17.581 2.35966 17.5775 2.36353L8.47338 12.6499C8.43874 12.689 8.39667 12.7209 8.3496 12.7436C8.30254 12.7664 8.25143 12.7795 8.19923 12.7824C8.14704 12.7852 8.0948 12.7776 8.04556 12.7601C7.99632 12.7425 7.95105 12.7154 7.91239 12.6802L4.89089 9.93063C4.57708 9.64296 4.16692 9.48324 3.74121 9.48291Z" fill="white"/>
              </svg>

              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.SEND' | translate }}
            </button>
          </div>
        </div>

    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="!isLoading && !currentResolution">
    <div class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      <h5>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_NOT_FOUND' | translate }}</h5>
      <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_NOT_FOUND_DESC' | translate }}</p>
      <button class="btn btn-primary" (click)="onCancel()">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.BACK_TO_LIST' | translate }}
      </button>
    </div>
  </div>
</div>
