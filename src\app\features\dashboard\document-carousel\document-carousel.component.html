<div class="document-carousel">

  <!-- Loading State -->
  <div class="carousel-loading" *ngIf="loading">
    <div class="d-flex justify-content-center align-items-center" style="height: 125px;">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">
          {{'DASHBOARD_PAGE.LOADING' | translate}}
        </span>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="carousel-empty" *ngIf="!loading && !hasDocuments">
    <div class="d-flex flex-column justify-content-center align-items-center" style="height: 157px;">
      <i class="fas fa-folder-open fa-3x text-muted mb-2"></i>
      <h6 class="text-muted">
         {{'DASHBOARD_PAGE.NO_DOCUMENTS_FOUND' | translate}}
        </h6>
      <p class="text-muted small">
           {{'DASHBOARD_PAGE.NO_ANY_DOC' | translate}}
      </p>
    </div>
  </div>

  <!-- Pixel Perfect Document Carousel -->
  <div class="document-carousel-wrapper" *ngIf="!loading && hasDocuments">

    <!-- Header Section -->

    <!-- Document Carousel Container -->
    <div class="document-carousel-container position-relative">

      <!-- Navigation Arrows -->
      <button
        class="carousel-nav-btn carousel-nav-prev"
        (click)="previousSlide()"
        [disabled]="currentSlideIndex === 0"
        *ngIf="getCarouselSlides().length > 1">
       <img src="assets/images/arrow-left2.png" class="mx-2" alt="previous">

      </button>

      <button
        class="carousel-nav-btn carousel-nav-next"
        (click)="nextSlide()"
        [disabled]="currentSlideIndex >= getCarouselSlides().length - 1"
        *ngIf="getCarouselSlides().length > 1">
       <img src="assets/images/arrow-right.png" class="mx-2" alt="previous">
      </button>

      <!-- Document Card -->
      <div class="document-card-container" [ngStyle]="{'padding':paddingStyle}">
        <div
          class="document-card"
          *ngFor="let document of getCurrentSlideDocuments(); trackBy: trackByDocumentId"
          (click)="onDocumentClick(document)"
          [attr.title]="document.fileName">

          <!-- Document Icon -->
          <div class="document-icon-container">
            <div class="document-icon-wrapper">
              <!-- <i [class]="document.icon" [style.color]="document.iconColor" class="document-icon"></i> -->
              <svg width="39" height="39" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <rect x="0.174805" width="38.77" height="38.77" fill="url(#pattern0_19233_3487)"/>
              <defs>
              <pattern id="pattern0_19233_3487" patternContentUnits="objectBoundingBox" width="1" height="1">
              <use xlink:href="#image0_19233_3487" transform="scale(0.00195312)"/>
              </pattern>
              <image id="image0_19233_3487" width="512" height="512" preserveAspectRatio="none" xlink:href="data:image/png;base64,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**************************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"/>
              </defs>
              </svg>

            </div>
          </div>

          <!-- Document Content -->
          <div class="document-content">
            <h6 class="document-title" [title]="document.fileName">
              {{ document.fileName || '' }}
            </h6>

          </div>

          <!-- View Button -->
          <div class="document-action d-flex justify-content-center align-items-center">
            <button

              class="view2-btn btn primary-btn"
              [disabled]="!document.downloadUrl && !document.previewUrl"
              (click)="onDocumentClick(document); $event.stopPropagation()">
             {{ document.previewUrl? ('DASHBOARD_PAGE.VIEW' | translate): (document.downloadUrl? ('DASHBOARD_PAGE.DOWNLOAD' | translate) : ('DASHBOARD_PAGE.NOT_AVAILABLE' | translate)) }}

            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
