import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../../environments/environment.prod';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { FilePreviewService } from '@core/services/file-preview.service';
import { ErrorModalService } from '@core/services/error-modal.service';

@Component({
  selector: 'app-attachment-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './attachment-card.component.html',
  styleUrl: './attachment-card.component.scss'
})
export class AttachmentCardComponent {
  private toastr = inject(ToastrService);
  private translate = inject(TranslateService);
  private filePreviewService = inject(FilePreviewService);

  constructor(
    private errorModalService: ErrorModalService,
  ) { }
  @Input() moduleType: any; // can be object or array
  @Input() meetingData: any; // can be object or array
  @Input() attachment: any; // can be object or array
  @Input() showRemoveButton: boolean = false; // Show remove button
  @Input() isDeleting: boolean = false; // Disable remove button during deletion
  @Output() removeAttachment = new EventEmitter<any>(); // Emit when remove is clicked

  get attachments(): any[] {
    if (Array.isArray(this.attachment)) {
      return this.attachment;
    } else if (this.attachment && typeof this.attachment === 'object') {
      return [this.attachment];
    }
    return [];
  }

  /**
   * Format file size in bytes to human readable format
   */
  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = [this.translate.instant('COMMON.B'), this.translate.instant('COMMON.KB'), this.translate.instant('COMMON.MB'), this.translate.instant('COMMON.GB')];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Check if the file type is supported for download
   */
  private isSupportedFileType(fileName: string): boolean {
    if (!fileName) return false;

    const supportedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.png', '.jpg', '.jpeg'];
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));

    return supportedExtensions.includes(extension);
  }

  canDownload(): boolean {
    if (this.moduleType == 'meeting')
      return this.meetingData?.meetingStatusId === 3 || this.meetingData?.meetingStatusId === 4;
    else
      return true;
  }


  downloadFile(attachment: any): void {
    if (!this.canDownload())
      return;

    if (!attachment) {
      this.toastr.error(
        this.translate.instant('COMMON.NO_ATTACHMENT_PROVIDED') || 'No attachment provided',
        this.translate.instant('COMMON.ERROR') || 'Error'
      );
      return;
    }

    // Check if we have a file path
    // if (!attachment.filePath) {
    //   this.toastr.error(
    //     this.translate.instant('COMMON.NO_FILE_PATH') || 'No file path provided',
    //     this.translate.instant('COMMON.ERROR') || 'Error'
    //   );
    //   return;
    // }

    // Try preview first, fallback to download
        if (this.moduleType == 'meeting')
    attachment.id = attachment.attachmentId;
    this.filePreviewService.previewFile(attachment, () => {
      this.fallbackDownload(attachment);
    });
  }

  private fallbackDownload(attachment: any): void {


    try {
      // Show loading toast
      this.toastr.info(
        this.translate.instant('COMMON.DOWNLOADING_FILE') || 'Downloading file...',
        this.translate.instant('COMMON.PLEASE_WAIT') || 'Please wait'
      );

      // Construct the full URL for the file
      const fullUrl = attachment.filePath;

      // Create a temporary link element
      const link = document.createElement('a');
      link.href = fullUrl;
      link.target = '_blank'; // Open in new tab as fallback
      link.download = attachment.fileName || 'download'; // Set download filename

      // Add to DOM temporarily to trigger download
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);

      // Show success message
      this.toastr.success(
        this.translate.instant('COMMON.DOWNLOAD_STARTED') || 'Download started successfully',
        this.translate.instant('COMMON.SUCCESS') || 'Success'
      );

      console.log('Download initiated for:', attachment.fileName);
    } catch (error: any) {
      console.error('Error downloading file:', error);

      this.errorModalService.showError(error.parsedMessage)


      // Fallback: try to open in new window
      try {
        const fullUrl = attachment.filePath;
        window.open(fullUrl, '_blank');

        this.toastr.info(
          this.translate.instant('COMMON.OPENED_IN_NEW_TAB') || 'File opened in new tab',
          this.translate.instant('COMMON.INFO') || 'Info'
        );
      } catch (fallbackError: any) {
        console.error('Fallback download also failed:', fallbackError);
        this.errorModalService.showError(error.parsedMessage)

      }
    }
  }

  /**
   * Handle remove attachment click
   */
  onRemoveAttachment(attachment: any): void {
    if (!this.isDeleting) {
      this.removeAttachment.emit(attachment);
    }
  }
}
