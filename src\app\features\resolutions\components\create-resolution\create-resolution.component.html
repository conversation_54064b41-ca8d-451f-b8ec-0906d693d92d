<div class="create-resolution-page" >
  <!-- Breadcrumb Navigation -->
  <!-- <app-breadcrumb
    (onClickEvent)="onBreadcrumbClicked($event)"
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium"
    divider=">">
  </app-breadcrumb> -->

  <!-- Page Header -->
  <div class="mt-3">
    <app-page-header [title]="'RESOLUTIONS.CREATE_TITLE' | translate" >
    </app-page-header>
  </div>

  <!-- Form Container -->
  <div class="form-container mt-3">
    <!-- <app-form-builder
      (dropdownChanged)="dropdownChanged($event)"
      [formControls]="visibleFormControls"
      [formGroup]="formGroup"
      [isFormSubmitted]="isValidationFire"
      (fileUploaded)="onFileUpload($event)">
    </app-form-builder> -->



    <app-form-builder
    (dropdownChanged)="dropdownChanged($event)"
    [formControls]="formControls" [formGroup]="formGroup" [isFormSubmitted]="isValidationFire"
      (dateSelected)="dateSelected($event)" (fileUploaded)="onFileUpload($event)" (valueChanged)="onValueChange($event, $event.control)">
      <p slot="top" class="header mt-2">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.BASIC_INFO' | translate }}</p>
    </app-form-builder>

    <!-- Action Buttons -->
    <div class="actions">
      <app-custom-button
        [btnName]="'COMMON.CANCEL' | translate"
        (click)="onCancel()"
        [buttonType]="buttonEnum.Secondary"
        [iconName]="IconEnum.cancel">
      </app-custom-button>

      <app-custom-button
        [btnName]="'RESOLUTIONS.SAVE_AS_DRAFT' | translate"
        [buttonType]="buttonEnum.OutLine"
        [iconName]="IconEnum.draft"
        (click)="onSubmit(true)">
      </app-custom-button>

      <app-custom-button
        [btnName]="'RESOLUTIONS.SEND' | translate"
        [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.verify"
        (click)="onSubmit(false)">
      </app-custom-button>
    </div>
  </div>
</div>
