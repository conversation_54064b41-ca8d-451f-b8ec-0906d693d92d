# Board Member Edit/Delete Implementation Guide

## Overview
This document outlines the implementation of edit and delete functionality for board members in the JadwaUI project, following established architectural patterns and business rules as specified in Jira tickets JDWA-2084 and JDWA-2088.

## Business Requirements

### Edit Member (JDWA-2084)
- **User Roles**: Legal Counsel, Board Secretary
- **Key Validations**:
  - Minimum 2 independent members must be maintained
  - Only one chairman per fund allowed
  - Cannot change independent to dependent if only 2 independent members exist
- **UI Behavior**:
  - **Member Name Field**: Read-only/disabled
  - **Member Type Radio Buttons**: Disabled when exactly 2 independent members exist and current member is independent
  - **Chairman Checkbox**: Disabled when fund already has a chairman and current member is not the chairman
  - **Warning Message**: Displayed when member type is restricted due to minimum independent member requirement
- **Error Messages**: Specific validation messages as per Jira specifications

### Delete Member (JDWA-2088)
- **User Roles**: Legal Counsel, Board Secretary
- **Key Validations**:
  - Cannot delete independent member if it reduces count below 2
  - Soft delete (status change to inactive)
- **UI Behavior**:
  - **Error Dialog**: When deleting would violate minimum independent members rule, show error popup with "Close" button only
  - **Confirmation Dialog**: When deletion is allowed, show standard confirmation with "Cancel" and "Delete" buttons
- **Notifications**: Send notification to deleted member (in-app & WhatsApp)

## Architecture Patterns

### Component Structure
```
src/app/features/members/
├── edit-member/
│   ├── edit-member.component.ts
│   ├── edit-member.component.html
│   └── edit-member.component.scss
├── delete-member-dialog/
│   ├── delete-member-dialog.component.ts
│   ├── delete-member-dialog.component.html
│   └── delete-member-dialog.component.scss
└── members.component.ts (updated)
```

### Key Architectural Decisions

#### 1. Dialog-Based Components
- **Edit Member**: Full dialog with form-builder integration
- **Delete Member**: Confirmation dialog with validation
- Both follow Material Dialog patterns used throughout JadwaUI

#### 2. Form Builder Integration
- Uses `app-form-builder` component with `IControlOption[]` configuration
- Maintains consistency with add-member and other form components
- Pre-populates form data for edit scenarios

#### 3. Validation Strategy
- **Client-side validation**: Immediate feedback for business rules
- **Server-side validation**: Backend validation when API endpoints available
- **Separation of concerns**: Dedicated validation methods for each rule

#### 4. API Integration Preparation
- Components structured for easy API integration
- Placeholder methods for actual API calls
- Error handling patterns consistent with existing components

## Implementation Details

### Edit Member Component

#### Form Controls Configuration
```typescript
formControls: IControlOption[] = [
  {
    type: InputType.Text,
    formControlName: 'memberName',
    isReadonly: true, // Read-only in edit mode
  },
  {
    type: InputType.RadioButton,
    formControlName: 'memberType',
    options: [
      { name: 'INVESTMENT_FUNDS.MEMBERS.INDEPENDENT', id: BoardMemberType._1 },
      { name: 'INVESTMENT_FUNDS.MEMBERS.DEPENDENT', id: BoardMemberType._2 }
    ],
  },
  {
    type: InputType.Checkbox,
    formControlName: 'isChairman',
    options: [{ name: 'INVESTMENT_FUNDS.MEMBERS.IS_CHAIRMAN', id: 1 }],
  }
];
```

#### Business Rule Validation
```typescript
private validateMinimumIndependentMembers(formValue: any): boolean {
  const isChangingToDependent = this.member.memberType === BoardMemberType._1 && 
                                formValue.memberType === BoardMemberType._2;
  
  if (isChangingToDependent && this.independentMembersCount <= 2) {
    this.errorModalService.showError('INVESTMENT_FUNDS.MEMBERS.ERROR_MIN_INDEPENDENT_MEMBERS');
    return false;
  }
  return true;
}
```

### Delete Member Component

#### Enhanced Dialog Behavior
The delete member dialog now shows different content based on validation:

```typescript
get showValidationError(): boolean {
  return this.member.memberType === BoardMemberType._1 && this.independentMembersCount <= 2;
}

get showConfirmationDialog(): boolean {
  return !this.showValidationError;
}
```

#### Validation Logic
```typescript
private validateDeletion(): boolean {
  if (this.member.memberType === BoardMemberType._1 &&
      this.independentMembersCount <= 2) {
    return false; // Show error dialog instead of confirmation
  }
  return true;
}
```

#### Dialog Templates
- **Error Dialog**: Shows error message with single "Close" button
- **Confirmation Dialog**: Shows confirmation message with "Cancel" and "Delete" buttons

## Localization Keys

### English (en.json)
```json
{
  "INVESTMENT_FUNDS": {
    "MEMBERS": {
      "EDIT_MEMBER": "Edit Member Details",
      "SAVE_CHANGES": "Save Changes",
      "EDIT_SUCCESS": "Member details updated successfully",
      "ERROR_MIN_INDEPENDENT_MEMBERS": "This change will reduce the number of independent members below the minimum of 2 which is not allowed",
      "ERROR_CHAIRMAN_EXISTS": "Board members include only one chairman",
      "MEMBER_TYPE_RESTRICTION_WARNING": "Member type cannot be changed as it would reduce the number of independent members below the required minimum of two",
      "DELETE_MEMBER_TITLE": "Delete Member",
      "DELETE_CONFIRMATION": "Are you sure you want to remove this member from the fund?",
      "DELETE_SUCCESS": "The member has been successfully removed from the fund",
      "ERROR_DELETE_MIN_INDEPENDENT": "This action cannot be completed as it would reduce the number of independent members below the required minimum of two"
    }
  }
}
```

### Arabic (ar.json)
```json
{
  "INVESTMENT_FUNDS": {
    "MEMBERS": {
      "EDIT_MEMBER": "تعديل بيانات العضو",
      "SAVE_CHANGES": "حفظ التغييرات",
      "EDIT_SUCCESS": "تم تحديث بيانات العضو بنجاح",
      "ERROR_MIN_INDEPENDENT_MEMBERS": "هذا التغيير سيقلل عدد الأعضاء المستقلين عن الحد الأدنى (2)، وهذا غير مسموح",
      "ERROR_CHAIRMAN_EXISTS": "مجلس أعضاء الصندوق يحوي رئيس مجلس إدارة واحد فقط",
      "MEMBER_TYPE_RESTRICTION_WARNING": "لا يمكن تغيير نوع العضو لأن ذلك سيقلل عدد الأعضاء المستقلين عن الحد الأدنى المطلوب وهو اثنان",
      "DELETE_MEMBER_TITLE": "حذف العضو",
      "DELETE_CONFIRMATION": "هل أنت متأكد من إزالة هذا العضو من الصندوق؟",
      "DELETE_SUCCESS": "تم إزالة العضو من الصندوق بنجاح",
      "ERROR_DELETE_MIN_INDEPENDENT": "لا يمكن إتمام هذا الإجراء لأنه سيقلل عدد الأعضاء المستقلين عن الحد الأدنى المطلوب وهو اثنان"
    }
  }
}
```

## Integration with Members Component

### Dialog Integration Pattern
```typescript
onEditMember(member: BoardMemberResponse): void {
  const dialogRef = this.dialog.open(EditMemberComponent, {
    width: '600px',
    data: {
      member: member,
      fundId: this.fundId,
      hasChairman: this.hasChairman,
      independentMembersCount: this.getIndependentMembersCount()
    } as EditMemberDialogData
  });

  dialogRef.afterClosed().subscribe(result => {
    if (result) {
      this.loadMembers(); // Refresh list on success
    }
  });
}
```

## Future API Integration

### Expected API Endpoints
- `PUT /api/BoardMembers/UpdateBoardMember` - Edit member
- `DELETE /api/BoardMembers/DeleteBoardMember` - Soft delete member

### Command Structures
```typescript
// Update Command
interface UpdateBoardMemberCommand {
  id: number;
  fundId: number;
  userId: number;
  memberType: BoardMemberType;
  isChairman: boolean;
  isActive: boolean;
}

// Delete Command  
interface DeleteBoardMemberCommand {
  id: number;
  fundId: number;
}
```

## Testing Considerations

### Test Scenarios
1. **Edit Member**:
   - Valid edits (type change, chairman status)
   - Invalid edits (violating minimum independent members)
   - Chairman conflict scenarios
   
2. **Delete Member**:
   - Valid deletions (non-independent or excess independent members)
   - Invalid deletions (violating minimum independent members)
   - Confirmation dialog behavior

### Validation Testing
- Test with exactly 2 independent members
- Test with more than 2 independent members  
- Test chairman assignment/removal scenarios
- Test form pre-population and readonly fields

## RTL/LTR Support
- All components include RTL-specific styling
- Icon positioning adjusted for RTL layouts
- Text alignment and spacing considerations implemented

## Conclusion
This implementation follows JadwaUI architectural patterns while meeting all business requirements specified in the Jira tickets. The modular approach ensures maintainability and consistency with existing codebase patterns.
