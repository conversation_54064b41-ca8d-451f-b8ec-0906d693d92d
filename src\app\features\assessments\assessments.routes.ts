import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';
import { CreateAssessmentGuard } from '@core/guards/create-assessment.guard';

export const ASSESSMENTS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./components/assessment-list/assessment-list.component').then(
        (m) => m.AssessmentListComponent
      ),
  },
  {
    path: 'create',
    loadComponent: () =>
      import('./components/create-assessment/create-assessment.component').then(
        (m) => m.CreateAssessmentComponent
      ),
      canActivate: [AuthGuard , CreateAssessmentGuard]
  },
  {
    path: 'edit/:id',
    loadComponent: () =>
      import('./components/edit-assessment/edit-assessment.component').then(
        (m) => m.EditAssessmentComponent
      ),
  },
  {
    path: 'details/:id',
    loadComponent: () =>
      import('./components/assessment-details/assessment-details.component').then(
        (m) => m.AssessmentDetailsComponent
      ),
  },
  {
    path: 'member-response',
    loadComponent: () =>
      import('./components/member-response/member-response.component').then(
        (m) => m.MemberResponseComponent
      ),
  },
  // {
  //   path: 'results/:id',
  //   loadComponent: () =>
  //     import('./components/assessment-results/assessment-results.component').then(
  //       (m) => m.AssessmentResultsComponent
  //     ),
  // },
  // {
  //   path: 'my-assessments',
  //   loadComponent: () =>
  //     import('./components/personal-assessments/personal-assessments.component').then(
  //       (m) => m.PersonalAssessmentsComponent
  //     ),
  // },
];
