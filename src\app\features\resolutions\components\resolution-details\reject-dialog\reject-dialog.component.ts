import { Component, EventEmitter, Inject, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgModel } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-reject-dialog',
  standalone: true,
  imports: [  CommonModule,
      FormsModule,
      MatDialogModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      TranslateModule],
  templateUrl: './reject-dialog.component.html',
  styleUrl: './reject-dialog.component.scss'
})
export class RejectDialogComponent {
  rejectReason: string = ''; // start empty
  @ViewChild('rejectReasonRef') rejectReasonRef!: NgModel;
  @Output() reason = new EventEmitter<string>();

  constructor(
    public dialogRef: MatDialogRef<RejectDialogComponent>,
    private translateService: TranslateService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // If you want to pre-fill reason only if provided
    if (data?.rejectReason) {
      this.rejectReason = data.rejectReason;
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSubmit(): void {

    if (this.rejectReasonRef.invalid) {
    // Mark the field as touched so all validation messages show
    this.rejectReasonRef.control.markAsTouched();
    return;
  }
    this.reason.emit(this.rejectReason.trim());
    this.dialogRef.close(this.rejectReason.trim());
  }

}
