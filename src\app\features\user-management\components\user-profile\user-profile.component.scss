@import '../../../../shared/styles/form-container';
@import '../../../../../assets/scss/variables';

.user-profile-page {
  padding: 0;

  .form-container {
    background: $white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    form {
      .profile-photo-section {
        border-bottom: 1px solid $border-color;
        padding-bottom: 24px;
        margin-bottom: 24px;

        .photo-container {
          position: relative;
          display: inline-block;

          .profile-photo-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid $navy-blue;
            overflow: hidden;
            position: relative;
            background: $border-color;
            display: flex;
            align-items: center;
            justify-content: center;

            .profile-photo {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: opacity 0.3s ease;

              &.loading {
                opacity: 0.5;
              }

              &.error {
                opacity: 0.8;
              }
            }
          }

          .photo-upload-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 36px;
            height: 36px;
            background: $navy-blue;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: 3px solid $white;

            i {
              color: $white;
              font-size: 14px;
            }

            &:hover {
              background: darken($navy-blue, 10%);
            }
          }
        }

        .photo-label {
          color: $dark-gray;
          font-size: 14px;
          margin: 0;
        }
      }

      .form-fields-section {
        .photo-upload-section {
          display: none; // Hide the form-builder file upload for photo since we have custom UI
        }

        // View Mode Styles
        .view-mode-fields {
          .form-label {
            font-weight: 600;
            color: $dark-gray;
            font-size: 14px;
            margin-bottom: 8px;
            display: block;
          }

          .field-value {
            padding: 12px 16px;
            font-size: 14px;
            color: $text-color;
            min-height: 44px;
            display: flex;
            align-items: center;

            &:empty::before {
              content: '-';
              color: $light-dark
            }

            .badge {
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 500;

              &.badge-success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
              }

              &.badge-secondary {
                background-color: #f8f9fa;
                color: #6c757d;
                border: 1px solid #dee2e6;
              }
            }

            i {
              color: $navy-blue;
            }
          }


          }
        }
      }

      .change-password-section,
      .change-password-toggle {
        border-top: 1px solid $border-color;
        padding-top: 24px;

        .change-password-link {
          color: $navy-blue;
          text-decoration: none;
          font-weight: 500;
          padding: 0;
          border: none;
          background: none;
          display: flex;
          align-items: center;
          transition: color 0.2s ease;

          &:hover {
            color: darken($navy-blue, 10%);
            text-decoration: underline;
          }

          i {
            color: $navy-blue;
            margin-right: 8px;
          }
        }
      }

      .actions {
        display: flex;
        gap: 12px;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid $border-color;

        app-custom-button {
          min-width: 120px;
        }
      }
    }
  }

  .loading-container {
    padding: 40px 20px;

    .spinner-border {
      width: 3rem;
      height: 3rem;
    }

    p {
      color: $dark-gray;
      margin-top: 16px;
      font-size: 14px;
    }
  }

  .error-container {
    padding: 40px 20px;

    .alert {
      max-width: 500px;
      margin: 0 auto 24px;
    }
  }

.attachment-card {
  display: flex;
  align-items: center;
  border: 1px solid #e0e6ed;
  border-radius: 12px;
  padding: 12px 10px;
  background-color: #fff;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.02);
  max-width: 320px;
  width: 100%;
}

.file-info {
  display: flex;
  flex-direction: column;
  color: #002b55;
  align-items: baseline;
  cursor: pointer;
}

.file-name {
  font-weight: 600;
  font-size: 16px;
  color: #00205a;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  margin-right: .5rem !important;
    margin-left: .5rem !important;
}

.file-size {
  color: #4f4f4f;
  font-size: 10px;
  font-weight: 400;
  line-height: 10px;
}

.download-icon {
  font-size: 22px;
  cursor: pointer;
  color: #002b55;
  transition: transform 0.2s ease;
}

.download-icon:hover {
  transform: scale(1.2);
}

// RTL Support
[dir="rtl"] {
  .user-profile-page {
    .form-container {
      form {
        .profile-photo-section {
          .photo-container {
            .photo-upload-overlay {
              left: 0;
              right: auto;
            }
          }
        }

        .change-password-section {
          .change-password-link {
            i {
              margin-left: 8px;
              margin-right: 0;
            }
          }
        }

        .form-fields-section {
          .view-mode-fields {
            .field-value {
              text-align: right;

              .badge {
                margin-left: 0;
                margin-right: auto;
              }

              i {
                margin-left: 8px;
                margin-right: 0;
              }

              .attachment-card {
                flex-direction: row-reverse;
                text-align: right;

                .file-info {
                  text-align: right;
                  cursor: pointer;
                }
              }
            }
          }
        }

        .actions {
          &.justify-content-end {
            justify-content: flex-start;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .user-profile-page {
    .form-container {
      padding: 16px;

      form {
        .profile-photo-section {
          .photo-container {
            .profile-photo-circle {
              width: 100px;
              height: 100px;
            }

            .photo-upload-overlay {
              width: 32px;
              height: 32px;

              i {
                font-size: 12px;
              }
            }
          }
        }

        .actions {
          flex-direction: column;

          app-custom-button {
            width: 100%;
          }
        }
      }
    }
  }
}

// Dark theme support (if needed in the future)
@media (prefers-color-scheme: dark) {
  .user-profile-page {
    .form-container {
      background: #1a202c;
      border-color: #2d3748;
      color: #e2e8f0;

      form {
        .profile-photo-section {
          border-color: #2d3748;

          .photo-container {
            .profile-photo-circle {
              background: #2d3748;
              border-color: $navy-blue;
            }
          }
        }

        .change-password-section {
          border-color: #2d3748;
        }

        .actions {
          border-color: #2d3748;
        }
      }
    }
  }
}
