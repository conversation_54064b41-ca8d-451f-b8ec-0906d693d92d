// Assessment Status Pie Chart Styles
// Responsive pie chart with breakdown visualization


.assessment-status-pie-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  // Loading State
  .chart-loading {
    .spinner-border {
      width: 2rem;
      height: 2rem;
    }
  }

  // Empty State
  .chart-empty {
    i {
      opacity: 0.5;
    }

    h6, p {
      margin: 0;
    }
  }

  // Chart Container
  .chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    // Chart Header
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 16px;
      padding: 0 8px;
      margin-bottom: 16px;

      .chart-title {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        flex: 1;
        min-width: 200px;
      }

      .chart-summary {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .summary-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 0.85rem;

          .summary-label {
            color: #6c757d;
            white-space: nowrap;
          }

          .summary-value {
            font-weight: 700;
            color: #2c3e50;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;

            &.completion-rate {
              color: #28a745;
              background: #d4edda;
            }
          }
        }
      }
    }

    // Pie Chart Wrapper
    .pie-chart-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
      margin-bottom: 20px;

      // NGX Charts Overrides
      ::ng-deep {
        .ngx-charts {
          .pie-chart {
            .pie-series {
              .pie-arc {
                stroke: #ffffff;
                stroke-width: 2px;
                transition: all 0.3s ease;

                &:hover {
                  opacity: 0.8;
                  transform: scale(1.05);
                }
              }
            }
          }

          .pie-label {
            font-size: 0.75rem;
            font-weight: 600;
            fill: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
          }

          .legend {
            .legend-labels {
              .legend-label {
                .legend-label-text {
                  font-size: 0.8rem;
                  fill: #2c3e50;
                }
              }
            }
          }
        }

        .tooltip-anchor {
          .ngx-tooltip {
            .tooltip-content {
              background: rgba(0, 0, 0, 0.9);
              color: white;
              border-radius: 6px;
              padding: 8px 12px;
              font-size: 0.85rem;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
          }
        }
      }
    }

    // Status Breakdown
    .status-breakdown {
      margin-bottom: 20px;

      .breakdown-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 12px;
        padding: 0 8px;
      }

      .breakdown-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 8px;

        .breakdown-item {
          background: #f8f9fa;
          border-radius: 6px;
          padding: 12px;
          border-left: 4px solid transparent;
          transition: all 0.2s ease;

          &:hover {
            background: #e9ecef;
            transform: translateX(2px);
          }

          .item-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            .item-color {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              flex-shrink: 0;
            }

            .item-label {
              font-size: 0.85rem;
              font-weight: 500;
              color: #2c3e50;
              flex: 1;
            }
          }

          .item-content {
            margin-bottom: 8px;

            .item-stats {
              display: flex;
              align-items: center;
              gap: 8px;

              .item-count {
                font-size: 1.1rem;
                font-weight: 700;
                color: #2c3e50;
              }

              .item-percentage {
                font-size: 0.8rem;
                color: #6c757d;
              }
            }
          }

          .item-progress {
            .progress {
              height: 4px;
              background-color: #e9ecef;
              border-radius: 2px;
              overflow: hidden;

              .progress-bar {
                transition: width 0.6s ease;
                border-radius: 2px;
              }
            }
          }
        }
      }
    }

    // Chart Statistics
    .chart-statistics {
      padding-top: 16px;
      border-top: 1px solid #e9ecef;

      .statistics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 16px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          background: #f8f9fa;
          border-radius: 8px;
          transition: all 0.2s ease;

          &:hover {
            background: #e9ecef;
            transform: translateY(-1px);
          }

          .stat-icon {
            font-size: 1.5rem;
            flex-shrink: 0;
          }

          .stat-content {
            flex: 1;
            min-width: 0;

            .stat-value {
              font-size: 1.1rem;
              font-weight: 700;
              color: #2c3e50;
              line-height: 1;
            }

            .stat-label {
              font-size: 0.75rem;
              color: #6c757d;
              margin-top: 2px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .assessment-status-pie-chart {
    .chart-container {
      .chart-header {
        flex-direction: column;
        align-items: flex-start;

        .chart-summary {
          align-self: stretch;

          .summary-item {
            justify-content: space-between;
          }
        }
      }

      .pie-chart-wrapper {
        min-height: 250px;

        ::ng-deep {
          .ngx-charts {
            .legend {
              .legend-labels {
                .legend-label {
                  .legend-label-text {
                    font-size: 0.7rem;
                  }
                }
              }
            }
          }
        }
      }

      .status-breakdown {
        .breakdown-grid {
          gap: 6px;

          .breakdown-item {
            padding: 10px;

            .item-content {
              .item-stats {
                .item-count {
                  font-size: 1rem;
                }
              }
            }
          }
        }
      }

      .chart-statistics {
        .statistics-grid {
          grid-template-columns: 1fr;
          gap: 8px;

          .stat-item {
            padding: 10px;

            .stat-content {
              .stat-value {
                font-size: 1rem;
              }

              .stat-label {
                font-size: 0.7rem;
              }
            }
          }
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .assessment-status-pie-chart {
    .chart-container {
      .chart-header {
        .chart-summary {
          .summary-item {
            flex-direction: row-reverse;
          }
        }
      }

      .status-breakdown {
        .breakdown-grid {
          .breakdown-item {
            border-left: none;
            border-right: 4px solid transparent;

            &:hover {
              transform: translateX(-2px);
            }

            .item-header {
              flex-direction: row-reverse;
            }
          }
        }
      }

      .chart-statistics {
        .statistics-grid {
          .stat-item {
            flex-direction: row-reverse;
          }
        }
      }
    }
  }
}

// Print Styles
@media print {
  .assessment-status-pie-chart {
    .chart-container {
      .pie-chart-wrapper {
        ::ng-deep {
          .ngx-charts {
            .pie-chart {
              .pie-series {
                .pie-arc {
                  stroke-width: 1px;
                }
              }
            }
          }
        }
      }

      .chart-statistics {
        page-break-inside: avoid;
      }
    }
  }
}
