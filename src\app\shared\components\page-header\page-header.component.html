<div class="page-header">
    <div class="container-fluid px-0">
        <div class="row align-items-center justify-content-between ">
            <!-- <div class="col-12 d-flex align-items-center gap-2" > -->
              <div class="title-container flex-wrap align-items-center d-flex gap-2 mb-3 mb-md-4">
                <span class="rotate-icon" *ngIf="showBackButton" (click)="onBackClick()">
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22"
                    viewBox="0 0 22 22" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M11.0572 0.75H10.9428C8.75212 0.749987 7.03144 0.749976 5.68802 0.930594C4.31137 1.11568 3.21911 1.50272 2.36091 2.36091C1.50271 3.21911 1.11568 4.31137 0.930593 5.68802C0.749975 7.03144 0.749987 8.75212 0.75 10.9428V11.0572C0.749987 13.2479 0.749975 14.9686 0.930593 16.312C1.11568 17.6886 1.50271 18.7809 2.36091 19.6391C3.21911 20.4973 4.31137 20.8843 5.68802 21.0694C7.03144 21.25 8.75212 21.25 10.9428 21.25H11.0572C13.2479 21.25 14.9686 21.25 16.312 21.0694C17.6886 20.8843 18.7809 20.4973 19.6391 19.6391C20.4973 18.7809 20.8843 17.6886 21.0694 16.312C21.25 14.9686 21.25 13.2479 21.25 11.0572V10.9428C21.25 8.75212 21.25 7.03144 21.0694 5.68802C20.8843 4.31137 20.4973 3.21911 19.6391 2.36091C18.7809 1.50272 17.6886 1.11568 16.312 0.930594C14.9686 0.749976 13.2479 0.749987 11.0572 0.75ZM16.1121 2.41722C17.3224 2.57994 18.0454 2.88853 18.5784 3.42157C19.1115 3.95462 19.4201 4.67757 19.5828 5.8879C19.7484 7.11979 19.75 8.73963 19.75 11C19.75 13.2604 19.7484 14.8802 19.5828 16.1121C19.4201 17.3224 19.1115 18.0454 18.5784 18.5784C18.0454 19.1115 17.3224 19.4201 16.1121 19.5828C14.8802 19.7484 13.2604 19.75 11 19.75C8.73963 19.75 7.11979 19.7484 5.88789 19.5828C4.67757 19.4201 3.95462 19.1115 3.42157 18.5784C2.88853 18.0454 2.57994 17.3224 2.41722 16.1121C2.25159 14.8802 2.25 13.2604 2.25 11C2.25 8.73963 2.25159 7.11979 2.41722 5.8879C2.57994 4.67757 2.88853 3.95462 3.42157 3.42157C3.95462 2.88853 4.67757 2.57994 5.88789 2.41722C7.11979 2.25159 8.73963 2.25 11 2.25C13.2604 2.25 14.8802 2.25159 16.1121 2.41722Z"
                      fill="#00205A" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M11.9622 7.97726C11.6735 8.27428 11.6802 8.74911 11.9773 9.03781C12.1388 9.19487 12.396 9.3971 12.6407 9.58933C12.6596 9.60416 12.6786 9.61906 12.6976 9.63405C12.9434 9.82696 13.2061 10.0333 13.4548 10.2439C13.4572 10.246 13.4595 10.248 13.4619 10.25L7 10.25C6.58579 10.25 6.25 10.5858 6.25 11C6.25 11.4142 6.58579 11.75 7 11.75L13.4619 11.75C13.4595 11.752 13.4572 11.754 13.4548 11.7561C13.2061 11.9667 12.9434 12.173 12.6976 12.3659C12.6786 12.3809 12.6596 12.3958 12.6407 12.4107C12.396 12.6029 12.1388 12.8051 11.9773 12.9622C11.6802 13.2509 11.6735 13.7257 11.9622 14.0227C12.2509 14.3198 12.7257 14.3265 13.0227 14.0378C13.114 13.9491 13.2958 13.8035 13.5672 13.5903C13.5869 13.5748 13.6069 13.5592 13.6272 13.5432C13.8693 13.3532 14.1534 13.1302 14.4245 12.9005C14.715 12.6543 15.0168 12.3787 15.2515 12.1032C15.369 11.9652 15.485 11.8096 15.5746 11.6422C15.661 11.4807 15.75 11.2583 15.75 11C15.75 10.7417 15.661 10.5193 15.5746 10.3578C15.485 10.1904 15.369 10.0348 15.2515 9.89679C15.0168 9.62131 14.715 9.34574 14.4245 9.09954C14.1534 8.8698 13.8693 8.64683 13.6272 8.45676C13.6069 8.44084 13.5869 8.42515 13.5672 8.40971C13.2958 8.19651 13.114 8.05089 13.0227 7.96219C12.7257 7.67349 12.2509 7.68023 11.9622 7.97726Z"
                      fill="#00205A" />
                  </svg>
                </span>
                <h2 [ngStyle]="{'margin-bottom':'8px'}" class="header d-flex">{{ title | translate }}
                  <span class="span-value" *ngIf="showSpan">{{spanValue}}</span>
                </h2>
                <div class="col-12 col-lg-5" *ngIf="showSlot">
                 <ng-content select="[slot='between']"></ng-content>
               </div>
                <p *ngIf="showSubHeader" class="sub-header" (click)="onSubHeaderClick()">{{subHeaderValue.split('+')[0] | translate}}: <span>{{subHeaderValue.split('+')[1]}}</span></p>
              </div>
            <div class="col-md-4 col-lg-3" *ngIf="showSearch">
              <div class="search-filter">
                <mat-form-field appearance="outline" class="search-input" title="">
                  <input
                    matInput  title=""
                    [placeholder]="searchPlaceholder | translate"
                    [(ngModel)]="searchValue"
                    (input)="onInput($event)"
                    (keydown.enter)="onSearch()"
                    (keydown)="restrictToNumbers($event)" />
                  <span matPrefix class="search-icon">
                    <!-- <img src="assets/images/search.png" alt="" /> -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M8.16699 0.0419922C3.67968 0.0419922 0.0419922 3.67968 0.0419922 8.16699C0.0419922 12.6543 3.67968 16.292 8.16699 16.292C10.1857 16.292 12.0324 15.5558 13.4534 14.3373L16.8917 17.7756C17.1358 18.0197 17.5315 18.0197 17.7756 17.7756C18.0197 17.5315 18.0197 17.1358 17.7756 16.8917L14.3373 13.4534C15.5558 12.0324 16.292 10.1857 16.292 8.16699C16.292 3.67968 12.6543 0.0419922 8.16699 0.0419922ZM1.29199 8.16699C1.29199 4.37003 4.37004 1.29199 8.16699 1.29199C11.964 1.29199 15.042 4.37003 15.042 8.16699C15.042 11.964 11.964 15.042 8.16699 15.042C4.37004 15.042 1.29199 11.964 1.29199 8.16699Z" fill="#4F4F4F"/>
                    </svg>
                  </span>
                </mat-form-field>

                <div *ngIf="showFilter" (click)="onFilterClick()" [title]="'COMMON.FILTER' | translate">
                  <img src="assets/images/filter.png" alt="" /> <!-- Disables img tooltip -->
                </div>
              </div>
            </div>
            <div class="col-12 create-button-container" 
            [ngClass]="{
              'col-md-4': showSearch,
              'col-md-12': !showSearch
            }" *ngIf="showCreateButton">
                <app-custom-button class="header-button" [disabled]="exceedMaxNumber" [btnName]="createButtonText | translate"
                    [iconName]="createButtonIcon" (click)="onCreateClick($event)"></app-custom-button>
            </div>
        <!-- </div> -->
    </div>

</div>
