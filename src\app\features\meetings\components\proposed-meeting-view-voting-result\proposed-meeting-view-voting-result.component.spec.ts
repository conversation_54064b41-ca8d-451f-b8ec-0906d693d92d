import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProposedMeetingViewVotingResultComponent } from './proposed-meeting-view-voting-result.component';

describe('ProposedMeetingViewVotingResultComponent', () => {
  let component: ProposedMeetingViewVotingResultComponent;
  let fixture: ComponentFixture<ProposedMeetingViewVotingResultComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProposedMeetingViewVotingResultComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ProposedMeetingViewVotingResultComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
