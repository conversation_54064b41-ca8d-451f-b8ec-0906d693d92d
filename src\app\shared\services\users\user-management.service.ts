// user-management.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { PaginatedResponse, UserDto } from '@shared/interfaces/user-management-response';
import { IUserCreateRequest, IUserUpdateRequest } from 'src/app/features/user-management/interfaces/user.interface';

@Injectable({
  providedIn: 'root',
})
export class UserManagementService {
  private baseUrl =environment.apiUrl+ '/api/Users/<USER>';
  private rolesURL =environment.apiUrl+ '/api/Users/<USER>/RoleList';

  constructor(private http: HttpClient) {}

  getUserList(
    role?: string,
    isActive?: boolean,
    name?: string,
    pageNumber: number = 1,
    pageSize: number = 10,
    search?: string,
    orderBy?: string
  ): Observable<any> {
    let params = new HttpParams();

    if (role) params = params.set('Role', role);
    if (isActive !== undefined) params = params.set('IsActive', isActive.toString());
    if (name) params = params.set('Name', name);
    if (pageNumber) params = params.set('PageNumber', pageNumber.toString());
    if (pageSize) params = params.set('PageSize', pageSize.toString());
    if (search) params = params.set('Search', search);
    if (orderBy) params = params.set('OrderBy', orderBy);

    return this.http.get<any>(`${this.baseUrl}/UserList`, { params });
  }

  /**
   * Get user by ID
   */
  getUserById(id: number): Observable<any> {
    const params = new HttpParams().set('userId', id.toString());

    return this.http.get<any>(`${this.baseUrl}/GetUserProfile`, { params });
  }

   getUserNameById(id: number): Observable<any> {
    const params = new HttpParams().set('id', id.toString());

    return this.http.get<any>(`${this.baseUrl}/GetUserById`, { params });
  }

  // ==================== USER CRUD OPERATIONS ====================

  /**
   * Create a new user
   */
  createUser(userData: IUserCreateRequest): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/AddUser`, userData);
  }
  updateUser(userData: any): Observable<any> {

    return this.http.put<any>(`${this.baseUrl}/UpdateUser`, userData);
  }

  /**
   * Delete user by ID
   */
  deleteUser(id: number): Observable<any> {
    const params = new HttpParams().set('id', id.toString());

    return this.http.delete<any>(`${this.baseUrl}/DeleteUser`, { params });
  }


  activateUser(body:any): Observable<any> {


    return this.http.post<any>(`${this.baseUrl}/ActivateUser`, body);
  }

  getUserProfile(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/GetUserProfile`);
  }
  updateUserProfile(profileData: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/UpdateUserProfile`, profileData);
  }
 getRolesList(){
  return this.http.get<any>(this.rolesURL);
 }


  getFundManagerUsers(fundId?: number): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/GetFundManagerUsers?fundId=${fundId ?? 0}`);
  }

  getBoardSecretaryUsers(fundId?: number): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/GetBoardSecretaryUsers?fundId=${fundId ?? 0}`);
  }

  getLegalCouncilUsers(): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/GetLegalCouncilUsers`);
  }
  getAssociateFundManagerUsers(fundId?: number): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/GetAssociateFundManagersUsers?fundId=${fundId ?? 0}`);
  }
  CheckRoleAvailability(): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/CheckRoleAvailability`);
  }

  userListForBoardMembers(fundId: number): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/UserListForBoardMembers?fundId=${fundId}`);
  }

  changePasswordForUser(data: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/ChangePasswordForUser`, data);
  }

  setNewPasswordForUser(data: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/setNewPasswordForUser`, data);
  }
 adminResetPassword(data: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/adminResetPassword`, data);
  }

  resendRegistrationMessage(data: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/ResendRegistrationMessage`, data);
  }


  deactivateUser(userId: number): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/DeactivateUser`, { userId });
  }
  checkUserWithUniqueRole(roleName: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/CheckUserWithUniqueRole?roleName=${encodeURIComponent(roleName)}`);
  }
}
