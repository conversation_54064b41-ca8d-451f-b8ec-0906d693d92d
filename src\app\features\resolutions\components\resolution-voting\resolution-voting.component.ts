import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { Location } from '@angular/common';
// Core imports
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// API imports
import {
  ResolutionsServiceProxy,
  SingleResolutionResponse,
  ResolutionStatusEnum,
  RejectResolutionCommand,
  ResolutionItemDto,
  ResolutionMemberVoteServiceProxy,
  EditResolutionMemberVoteCommand,
  ResolutionItemVoteCommentDto,
} from '@core/api/api.generated';
import { TokenService } from '../../../auth/services/token.service';
import Swal from 'sweetalert2';
import { ConflictsPopupComponent } from '../conflicts-popup/conflicts-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { AttachmentCardComponent } from '../attachment-card/attachment-card.component';
import { TimelineComponent } from '../timeline/timeline.component';
import { ResolutionStatus } from '@shared/enum/resolution-status';
import { ResolutionService } from '@core/services/resolution.service';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ConflictMembersDialogData } from '../edit-resolution/conflict-members-dialog/conflict-members-dialog.component';
import { MemberNoteComponent } from '../member-note/member-note.component';
import { SingleNoteDialogComponent } from '../single-note-dialog/single-note-dialog.component';
import { VotingResult } from '@shared/enum/votingResult';
import {
  MultipleNoteDialogComponent,
  MultipleNoteDialogData,
} from '../multiple-note-dialog/multiple-note-dialog.component';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-resolution-voting',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    AttachmentCardComponent,
    TimelineComponent,
    DateHijriConverterPipe,
    MemberNoteComponent,
  ],
  providers: [ResolutionMemberVoteServiceProxy],
  templateUrl: './resolution-voting.component.html',
  styleUrl: './resolution-voting.component.scss',
})
export class ResolutionVotingComponent implements OnInit {
  // Data properties
    // Track if navigation came from inbox
  fromInbox: boolean = false;
  resolution: any | null = null;
  resolutionId: number = 0;
  fundId: number = 0;
  fundName: string = '';
submitted: boolean = false;
  // Loading and error states
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Role-based access control
  userRole: string = '';
  canConfirmReject = false;
  canSendToVote = false;
  canViewDetails = false;

  // UI state
  breadcrumbSizeEnum = SizeEnum;
  buttonTypeEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [];
  isExpanded: boolean = true;
  isExpandedItem: boolean = true;
  isExpandedAction: boolean = true;
  resolutionStatus: any | null = null;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  resolutionStatusEnum = ResolutionStatus;
  isExpandedNotes: boolean = true;
  isActiveReject: boolean = false;
  isActiveApprove: boolean = false;
  votingResult = VotingResult;
  voteComments: { id: number; comment: string }[] = [];
  itemComments: { id: number; comment: string }[] = [];
  fullNameMemberComment: string | undefined;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private resolutionsProxy: ResolutionsServiceProxy,
    public tokenService: TokenService,
    private translateService: TranslateService,
    private dialog: MatDialog,
    private resolutionService: ResolutionService,
    private errorModalService: ErrorModalService,
    private resolutionMemberVoteServiceProxy: ResolutionMemberVoteServiceProxy,
    private TokenService: TokenService,
     private location: Location,
     private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit(): void {
     // Check if navigation came from inbox
    const fromInboxValue = localStorage.getItem('fromInbox');
    this.fromInbox = fromInboxValue === 'true';
    localStorage.removeItem('fromInbox');

    this.fullNameMemberComment =this.TokenService.getFullName()
    this.initializeComponent();
  }
    goBack() {
    this.location.back();
  }
  private initializeComponent(): void {
    // Get route parameters
    this.route.params.subscribe((params) => {
      this.resolutionId = +params['id'];

      // Get fundId from query params or route params
      this.route.queryParams.subscribe((queryParams) => {
        this.fundId = +queryParams['fundId'] || +params['fundId'] || 0;

        // Always setup breadcrumbs, even if fundId is invalid
        this.setupBreadcrumbs();

        if (this.resolutionId && this.fundId) {
          this.initializeRoleBasedAccess();
          this.loadResolutionDetails();

        } else {
          // Still initialize role-based access for basic functionality
          this.initializeRoleBasedAccess();
          this.handleError('RESOLUTIONS.INVALID_PARAMETERS');
        }
      });
    });
  }

  private initializeRoleBasedAccess(): void {
    // Determine user role based on permissions
    if (this.tokenService.hasRole('fundmanager')) {
      this.userRole = 'fundmanager';
      this.canViewDetails = true;
      this.canConfirmReject = true; // Fund manager can confirm/reject waiting for confirmation resolutions
      this.canSendToVote = false;
    } else if (
      this.tokenService.hasRole('legalcouncil') ||
      this.tokenService.hasRole('boardsecretary')
    ) {
      this.userRole = 'legalcouncil';
      this.canViewDetails = true;
      this.canConfirmReject = false; // Legal council/board secretary complete data, don't confirm/reject
      this.canSendToVote = true; // Legal council/board secretary can send confirmed resolutions to vote
    } else if (this.tokenService.hasRole('boardmember')) {
      this.userRole = 'boardmember';
      this.canViewDetails = true;
      this.canConfirmReject = false;
      this.canSendToVote = false;
    } else {
      this.userRole = 'Default';
      this.canViewDetails = false;
      this.canConfirmReject = false;
      this.canSendToVote = false;
    }
  }

  private setupBreadcrumbs(): void {
    if (this.fundId && this.fundId > 0) {
      this.updateBreadcrumb();
    } else {
      this.updateBreadcrumbWithFallback();
    }
  }

  private updateBreadcrumb(): void {
    if(this.fromInbox){
      this.breadcrumbItems = [
        { label: 'RESOLUTIONS.INBOX_TITLE', url: '/admin/investment-funds/resolutions/inbox'},

        {
          label: 'INVESTMENT_FUNDS.VOTING.TITLE',
          url: '',
          disabled: true,
        },
      ];
    }else{
      let currentFundName = localStorage.getItem('fundName') || "";
      this.breadcrumbItems = [
        {
          label: 'INVESTMENT_FUNDS.TITLE',
          url: '/admin/investment-funds',
          icon: 'fas fa-home',
        },
        {
          label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
          url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
        },
        {
          label: 'RESOLUTIONS.TITLE',
          url: `/admin/investment-funds/resolutions?fundId=${this.fundId}`
        },
        {
          label: 'INVESTMENT_FUNDS.VOTING.TITLE',
          url: '',
          disabled: true,
        },
     ];
    }

    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  private updateBreadcrumbWithFallback(): void {
    // Fallback breadcrumb when fundId is not available
    this.breadcrumbItems = [
      { label: 'RESOLUTIONS.INBOX_TITLE', url: '/admin/investment-funds/resolutions/inbox'},

      {
        label: 'INVESTMENT_FUNDS.VOTING.TITLE',
        url: '',
        disabled: true,
      },
    ];
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  loadResolutionDetails(): void {
    this.isLoading = true;
    this.hasError = false;

    this.resolutionService
      .getResolutionMemberVoteByResolutionId(this.resolutionId)
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.resolution = response.data;
             debugger
          if(this.resolution?.resolutionMemberVote?.voteResult === 2 || this.resolution?.resolutionMemberVote?.voteResult === 3){
            this.router.navigate(['/admin/investment-funds/resolutions/view-voting-result',this.resolutionId], {
              queryParams: { fundId: this.fundId },
            });
          }

          } else {
            this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error loading resolution details:', error);
          // this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
        },
      });
  }




  private handleError(messageKey: string): void {
    this.hasError = true;
    this.errorMessage = this.translateService.instant(messageKey);

    Swal.fire({
      title: this.translateService.instant('COMMON.ERROR'),
      text: this.errorMessage,
      icon: 'error',
      confirmButtonText: this.translateService.instant('COMMON.OK'),
    });
  }
  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    console.log('Breadcrumb clicked:', item);

    if (!item) {
      console.warn('Breadcrumb item is null or undefined');
      return;
    }

    if (item.disabled) {
      console.log('Breadcrumb item is disabled, ignoring click');
      return;
    }

    if (!item.url) {
      console.warn('Breadcrumb item has no URL:', item);
      return;
    }

    console.log('Navigating to:', item.url);
    this.router.navigateByUrl(item.url).catch((error) => {
      console.error('Navigation failed:', error);
    });
  }

  onBackToList(): void {
    // this.router.navigate(['/admin/investment-funds/resolutions'], {
    //   queryParams: { fundId: this.fundId },
    // });
    this.navigateAfterAction();
  }


  shouldShowConfirmRejectButtons(): boolean {
    // Show confirm/reject buttons for fund manager when resolution is waiting for confirmation
    return (
      this.canConfirmReject &&
      this.resolution?.status === ResolutionStatusEnum._4
    ); // Waiting for confirmation status
  }

  shouldShowSendToVoteButton(): boolean {
    // Show send to vote button for legal council/board secretary when resolution is confirmed
    return (
      this.canSendToVote && this.resolution?.status === ResolutionStatusEnum._5
    ); // Confirmed status
  }

  // Status-specific UI methods
  shouldShowBasicInfo(): boolean {
    // Basic info is shown for all authorized users and statuses
    return true;
  }

  shouldShowResolutionItems(): boolean {
    // Resolution items are shown for completing data, waiting for confirmation, confirmed, and rejected statuses
    if (!this.resolution) return false;

    const statusesWithItems = [
      ResolutionStatusEnum._3, // Approved/Confirmed
      ResolutionStatusEnum._4, // Rejected/Cancelled
      ResolutionStatusEnum._5, // Voting in progress (completing data equivalent)
      ResolutionStatusEnum._6, // Not approved (waiting for confirmation equivalent)
    ];

    return statusesWithItems.includes(this.resolution.status);
  }

  shouldShowResolutionHistory(): boolean {
    // History is shown for all statuses except draft
    if (!this.resolution) return false;
    return this.resolution.status !== ResolutionStatusEnum._1; // Not draft
  }

  shouldShowRejectionReason(): boolean {
    // Rejection reason is shown only for rejected status
    if (!this.resolution) return false;
    return this.resolution.status === ResolutionStatusEnum._4; // Rejected/Cancelled
  }



  formatDate(date: any): string {
    if (!date) return '';
    try {
      return new Date(date.toString()).toLocaleDateString('ar-SA');
    } catch {
      return '';
    }
  }

  // File operation methods
  onDownloadFile(): void {
    // TODO: Implement file download using FileManagementServiceProxy
    // Note: attachmentId property doesn't exist in SingleResolutionResponse
    console.log('Downloading file - feature not yet implemented');
  }

  onOpenFile(): void {
    // TODO: Implement file opening/preview
    // Note: attachmentId property doesn't exist in SingleResolutionResponse
    console.log('Opening file - feature not yet implemented');
  }

  onDownloadAttachment(item: any): void {
    // TODO: Implement attachment download
    console.log('Downloading attachment:', item);
  }



  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }
  toggleExpandItems() {
    this.isExpandedItem = !this.isExpandedItem;
  }

  toggleExpandActions() {
    this.isExpandedAction = !this.isExpandedAction;
  }

  toggleExpandNotes() {
    this.isExpandedNotes = !this.isExpandedNotes;
  }

  getStatusVoteClass(statusId: number) {
    switch (statusId) {
      case 1:
        return 'approved';
      case 2:
        return 'rejected';
      default:
        return '';
    }
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.resolutionStatusEnum.Draft:
        return 'draft';
      case this.resolutionStatusEnum.Pending:
        return 'pending';
      case this.resolutionStatusEnum.CompletingData:
        return 'completing-data';
      case this.resolutionStatusEnum.WaitingForConfirmation:
        return 'waiting-for-confirmation';
      case this.resolutionStatusEnum.Confirmed:
        return 'confirmed';
      case this.resolutionStatusEnum.Rejected:
        return 'rejected';
      case this.resolutionStatusEnum.VotingInProgress:
        return 'voting-inProgress';
      case this.resolutionStatusEnum.Approved:
        return 'approved';
      case this.resolutionStatusEnum.NotApproved:
        return 'not-approved';
      case this.resolutionStatusEnum.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }




  cancel() {
    // this.router.navigate(['/admin/investment-funds/resolutions'], {
    //   queryParams: { fundId: this.fundId },
    // });
    this.navigateAfterAction();
  }

  /**
   * Navigate to the parent/referral resolution when referral code is clicked
   */
  onReferralCodeClick(): void {
    if (this.resolution?.parentResolutionId && this.fundId) {
      this.router.navigate(['/admin/investment-funds/resolutions/details/'+this.resolution.parentResolutionId], {
        queryParams: {
          fundId: this.fundId
        }
      });
    }
  }

  addNote(isResolution: boolean, item?: any) {
    debugger
    this.dialog.closeAll();
    let newVoteComments :any[]= []
    let newItemComments :any[]= []
    const dialogRef = this.dialog.open(SingleNoteDialogComponent, {
      width: '700px',
      data: { isResolution , canAddComment : this.resolution?.canAddComment },
    });

    dialogRef
      .afterClosed()
      .subscribe((result: { id: number; comment: string; createdAt: any ;createdByName:any;userRoleOrBoardMemberType:string;}) => {
        if (result) {
          debugger
          if (isResolution) {
             result.createdByName = this.resolution?.resolutionMemberVote.boardMemberName;
           result.userRoleOrBoardMemberType = this.resolution?.resolutionMemberVote.boardMemberType;
            this.resolution?.resolutionMemberVote?.voteComments.push(result);
            console.log(this.resolution?.resolutionMemberVote?.voteComments);
          } else {
            item.itemComments = item.itemComments || [];
            result.createdAt = new Date();
          result.createdByName = this.resolution?.resolutionMemberVote.boardMemberName;
           result.userRoleOrBoardMemberType = this.resolution?.resolutionMemberVote.boardMemberType;
             item.itemComments.push(result);
            console.log(item.itemComments);
          }

        }
      });
  }

  openNotes(item: any) {
    debugger
    this.dialog.closeAll();
        let newItemComments :any[]= []

    if (!item || item.length === 0) {
      return;
    }

    const dialogData: MultipleNoteDialogData = {
      comments: item,
      canAddComment : this.resolution?.canAddComment

    };

    const dialogRef = this.dialog.open(MultipleNoteDialogComponent, {
      width: '700px',
      data: dialogData,
      disableClose: false,
    });

    dialogRef
      .afterClosed()
      .subscribe((result: { id: number; comment: string ;createdByName:any;userRoleOrBoardMemberType:string}) => {
        debugger
        if (result) {
          item.itemComments = item.itemComments || [];
           result.createdByName = this.resolution?.resolutionMemberVote.boardMemberName;
           result.userRoleOrBoardMemberType = this.resolution?.resolutionMemberVote.boardMemberType;
           item.itemComments.push(result);
          console.log(item.itemComments);
        }
      });
  }



  toggleRejectButton(item: any): void {
    item.voteResult =
      item.voteResult === this.votingResult.Reject
        ? null
        : this.votingResult.Reject;
  }

  toggleApproveButton(item: any): void {
    item.voteResult =
      item.voteResult === this.votingResult.Accept
        ? null
        : this.votingResult.Accept;
  }

  getNotificationTime(createdAt: Date): string {
    const now = new Date();
    const notificationDate = new Date(createdAt);
    const diffInMinutes = Math.floor(
      (now.getTime() - notificationDate.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) {
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.NOW');
    } else if (diffInMinutes < 60) {
      return (
        this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE') +
        ' ' +
        `${diffInMinutes}` +
        this.translateService.instant('INVESTMENT_FUNDS.VOTING.MINUTE')
      );
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return (
        this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE') +
        ' ' +
        ` ${hours}` +
        this.translateService.instant('INVESTMENT_FUNDS.VOTING.HOUR')
      );
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return (
        this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE') +
        ' ' +
        ` ${days}` +
        this.translateService.instant('INVESTMENT_FUNDS.VOTING.DAY')
      );
    }
  }

    // Action methods
  onConfirmResolution(resolution: any): void {
    // Prevent double hits by checking if already loading
    if (this.isLoading) {
      return;
    }

    let obj: any = {
      resolutionId: resolution.id,
      itemsVote: [],
      voteComments: resolution?.resolutionMemberVote?.voteComments.filter((item:any) => item.id === 0),
      id: resolution.resolutionMemberVote.id,
      voteResult: 2
    };

    this.isLoading = true;
    this.resolutionMemberVoteServiceProxy.submitVote(obj).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.successed) {
          this.errorModalService.showSuccess(
            'INVESTMENT_FUNDS.VOTING.VOTING_SEND_SUCCESSFUL'
          );
          // this.router.navigate(['/admin/investment-funds/resolutions'], {
          //   queryParams: { fundId: this.fundId },
          // });
          this.navigateAfterAction();
        } else {
          this.handleError('RESOLUTIONS.REJECT_FAILED');
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error confirming resolution:', error);
      },
    });
  }

  onRejectResolution(resolution: any): void {
    // Prevent double hits by checking if already loading
    if (this.isLoading) {
      return;
    }

    let obj: any = {
      resolutionId: resolution.id,
      itemsVote: [],
      voteComments: resolution?.resolutionMemberVote?.voteComments.filter((item:any) => item.id === 0),
      id: resolution.resolutionMemberVote.id,
      voteResult: 3
    };

    this.isLoading = true;
    this.resolutionMemberVoteServiceProxy.submitVote(obj).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.successed) {
          this.errorModalService.showSuccess(
            'INVESTMENT_FUNDS.VOTING.VOTING_SEND_SUCCESSFUL'
          );
          // this.router.navigate(['/admin/investment-funds/resolutions'], {
          //   queryParams: { fundId: this.fundId },
          // });
          this.navigateAfterAction();
        } else {
          this.handleError('RESOLUTIONS.REJECT_FAILED');
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error rejecting resolution:', error);
      },
    });
  }

    completeResolution(resolution: any): void {
    debugger
    // Prevent double hits by checking if already loading
    if (this.isLoading) {
      return;
    }

   const invalidVotesExist = this.resolution.resolutionMemberVote.itemsVote?.some((item:any) => ![0, 2, 3].includes(item.voteResult));
   if (invalidVotesExist) {
      this.errorModalService.showError('INVESTMENT_FUNDS.VOTING.VOTING_FAILED');
       return;
   }
    let obj: any = {
      resolutionId: resolution.id,
      itemsVote:  resolution.resolutionMemberVote.itemsVote,
      voteComments: resolution?.resolutionMemberVote?.voteComments.filter((item:any) => item.id === 0),
      id: resolution.resolutionMemberVote.id,
    };

    // const hasNoVotedYet = obj.itemsVote?.some((item: { voteResult: number; }) => item.voteResult === 1);
    // if (obj.itemsVote.length != 0 && hasNoVotedYet) {
    //   this.errorModalService.showError('INVESTMENT_FUNDS.VOTING.VOTING_FAILED');
    //   return;
    // }

    this.isLoading = true;
    this.resolutionMemberVoteServiceProxy.submitVote(obj).subscribe({
      next: (res) => {
        this.isLoading = false;
        if (res.successed) {
          this.errorModalService.showSuccess(
            this.translateService.instant(
              'INVESTMENT_FUNDS.VOTING.VOTING_SEND_SUCCESSFUL'
            )
          );
          this.submitted = true;
          // this.router.navigate(['/admin/investment-funds/resolutions'], {
          //   queryParams: { fundId: this.fundId },
          // });
          this.navigateAfterAction();
        } else {
          this.handleError('RESOLUTIONS.REJECT_FAILED');
          this.submitted = true;
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error completing resolution:', error);
        this.submitted = true;
      }
    });
  }
  /**
   * Navigate to appropriate page based on navigation source and clean up localStorage
   */
  private navigateAfterAction(): void {

    if (this.fromInbox) {
      // Navigate back to resolution inbox
      console.log('Navigating back to resolution inbox');
      this.router.navigate(['/admin/investment-funds/resolutions/inbox']);
    } else {
      // Navigate to regular resolutions list
      console.log('Navigating to resolutions list with fundId:', this.fundId);
      this.router.navigate(['/admin/investment-funds/resolutions'], {
        queryParams: { fundId: this.fundId },
      });
    }
  }

}


