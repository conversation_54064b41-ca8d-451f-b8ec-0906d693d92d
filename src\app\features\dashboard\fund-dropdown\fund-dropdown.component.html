
  <!-- Simple HTML Select -->
  <div>

    <ng-select
      id="fundSelect"
      [(ngModel)]="selectedFundId"
      (change)="onFundSelectionChange($event)"
      [clearable]="false"
      [disabled]="loading || disabled"
      style="width: 280px; padding: 10px; font-size: 16px; border: 2px solid transparent; border-radius: 4px; background: transparent;">

      <ng-option *ngFor="let fund of funds; trackBy: trackByFundId" [value]="fund.fundId">
        {{ getFundDisplayName(fund) }}
      </ng-option>
    </ng-select>
  </div>



