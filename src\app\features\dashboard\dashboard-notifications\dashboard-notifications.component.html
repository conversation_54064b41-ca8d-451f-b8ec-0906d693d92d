<div class="dashboard-notifications">

  <!-- Loading State -->
  <div class="notifications-loading" *ngIf="loading">
    <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">جاري التحميل...</span>
      </div>
    </div>
  </div>

  <!-- Notifications Content -->
  <div class="notifications-container" *ngIf="!loading">

    <!-- Tab Header -->
    <div class="notifications-header">
      <div class="tab-navigation">
        <button
          class="tab-btn"
          [class.active]="activeTab === 'meetings'"
          (click)="switchTab('meetings')">
          <span class="tab-text">الاجتماعات</span>
          <span class="tab-count" *ngIf="upcomingMeetingsCount > 0">({{ upcomingMeetingsCount }})</span>
        </button>
        <button
          class="tab-btn"
          [class.active]="activeTab === 'requests'"
          (click)="switchTab('requests')">
          <span class="tab-text">الطلبات</span>
          <span class="tab-count" *ngIf="unreadRequestsCount > 0">({{ unreadRequestsCount }})</span>
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="notifications-content">

      <!-- Empty State -->
      <div class="empty-state" *ngIf="!hasItems">
        <div class="empty-state-content">
          <i class="fas fa-bell-slash fa-2x text-muted mb-3"></i>
          <h6 class="text-muted">{{ emptyStateText }}</h6>
          <p class="text-muted small">{{ emptyStateDescription }}</p>
        </div>
      </div>

      <!-- Items List -->
      <div class="notifications-list" *ngIf="hasItems">
        <div
          class="notification-item"
          *ngFor="let item of displayItems; trackBy: trackByItemId"
          (click)="onItemClick(item)"
          [class.unread]="isNotificationItem(item) && !item.isRead">

          <!-- Status Indicator -->
          <div class="status-indicator">
            <div
              class="status-dot"
              [style.background-color]="getStatusColor(item.status)">
            </div>
            <span class="status-text">{{ item.statusDisplay || item.status }}</span>
          </div>

          <!-- Content -->
          <div class="item-content">
            <div class="fund-name">
              {{ isNotificationItem(item) ? item.fundName : item.title }}
            </div>
          </div>

          <!-- Action/Type -->
          <div class="item-action">
            <span class="action-text">
              {{ isNotificationItem(item) ? item.requestType : 'اجتماع' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Actions (if needed) -->
    <div class="notifications-footer" *ngIf="hasItems">
      <button class="footer-btn" (click)="markAllAsRead()" *ngIf="activeTab === 'requests' && unreadRequestsCount > 0">
        <i class="fas fa-check-double me-1"></i>
        تحديد الكل كمقروء
      </button>
      <button class="footer-btn" (click)="viewAll()">
        <i class="fas fa-external-link-alt me-1"></i>
        {{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}
      </button>
    </div>
  </div>

</div>
