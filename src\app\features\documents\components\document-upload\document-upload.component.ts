import { Component, Output, EventEmitter, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Observable, forkJoin } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

// Shared components and interfaces
import { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';

// API and services
import {
  DocumentServiceProxy,
  DocumentCategoryDto,
  GetDocumentCategoriesQuery,
  AddDocumentCommand
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { AttachmentModule } from '@shared/enum/AttachmentModule';

export interface DocumentUploadDialogData {
  fundId?: number;
  selectedCategory?: DocumentCategoryDto;
  documentCategories?: DocumentCategoryDto[]; // Pass categories from parent to avoid duplicate API calls
}

@Component({
  selector: 'app-document-upload',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    FileUploadComponent,
    CustomButtonComponent,
    FormBuilderComponent
  ],
  templateUrl: './document-upload.component.html',
  styleUrls: ['./document-upload.component.scss']
})
export class DocumentUploadComponent implements OnInit {
  @Output() uploadComplete = new EventEmitter<any>();

  // Form-related properties
  formGroup!: FormGroup;
  isFormSubmitted: boolean = false;
  isValidationFire: boolean = false;
  isLoading: boolean = false;
  private isSubmitting: boolean = false;

  // File upload data
  uploadedFiles: any[] = []; // Store uploaded file data with attachment IDs

  // Document categories
  documentCategories: DocumentCategoryDto[] = [];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // Form controls configuration
  formControls: IControlOption[] = [
    {
      type: InputType.Dropdown,
      formControlName: 'categoryId',
      id: 'categoryId',
      name: 'categoryId',
      label: 'DOCUMENTS.CATEGORY',
      placeholder: 'DOCUMENTS.SELECT_CATEGORY',
      isRequired: true,
      class: 'col-md-12',
      options: [],
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: 'RESOLUTIONS.CHOOSE_FILE',
      placeholder: 'INVESTMENT_FUNDS.FORM.DRAG_DROP_FILES',
      isRequired: true,
      class: 'col-md-12',
      allowedTypes: ['pdf'],
      moduleId: AttachmentModule.Documents
    },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private documentProxy: DocumentServiceProxy,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService,
    private dialogRef: MatDialogRef<DocumentUploadComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentUploadDialogData
  ) { }

  ngOnInit(): void {
    console.log('DocumentUploadComponent ngOnInit - Dialog data:', this.data);
    this.initForm();
    this.initializeCategories();
  }
  
  onFileUpload(data: any) {
    this.formGroup.get(data.control.formControlName)?.setValue(data.file.id);
  }
 private initForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }
      formGroup[control.formControlName] = [null, validators];
    });

    this.formGroup = this.formBuilder.group(formGroup);
  }

  private initializeCategories(): void {
    console.log('Initializing categories...');

    // Use categories from parent if available to avoid duplicate API calls
    if (this.data?.documentCategories && this.data.documentCategories.length > 0) {
      console.log('Using categories from parent:', this.data.documentCategories);
      this.documentCategories = this.data.documentCategories;
      this.populateDropdownOptions();
    } else {
      console.log('Loading categories from API...');
      this.loadCategoriesFromAPI();
    }
  }

  private populateDropdownOptions(): void {
    // Update the dropdown options
    const categoryOptions = this.documentCategories.map(category => ({
      id: category.id,
      name: category.name || this.translateService.instant('DOCUMENTS.UNKNOWN_CATEGORY')
    }));

    console.log('Category options:', categoryOptions);

    const categoryDropdown = this.formControls.find(control => control.formControlName === 'categoryId');
    if (categoryDropdown) {
      categoryDropdown.options = categoryOptions;
      console.log('Updated dropdown options:', categoryDropdown.options);
    }

    // Pre-select category if provided
    if (this.data?.selectedCategory) {
      console.log('Pre-selecting category:', this.data.selectedCategory);
      this.formGroup.get('categoryId')?.setValue(this.data.selectedCategory.id);
    }

    // If no categories available, show warning
    if (this.documentCategories.length === 0) {
      console.warn('No document categories available for upload');
    }
  }

  private loadCategoriesFromAPI(): void {
    // Use NSwag-generated proxy with proper typing
    const query = new GetDocumentCategoriesQuery();

    this.documentProxy.categories(query).subscribe({
      next: (response) => {
        console.log('Categories API response:', response);
        // Response is properly typed as DocumentCategoryDtoListBaseResponse
        if (response?.data && Array.isArray(response.data)) {
          this.documentCategories = response.data;
          this.populateDropdownOptions();
        } else {
          this.documentCategories = [];
          console.warn('No categories received from API');
        }
      },
      error: (error: any) => {
        console.error('Error loading categories:', error);
        this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.LOAD_CATEGORIES_FAILED'));
        this.documentCategories = [];
      }
    });
  }


  onSubmitClick(): void {
    console.log('Submit clicked - Form valid:', this.formGroup.valid);
    console.log('Form values:', this.formGroup.value);
    console.log('Form errors:', this.getFormErrors());

    // This method is called by the button click
    // It triggers form validation and submission
    if (this.formGroup.valid) {
      this.onSubmit();
    } else {
      this.isSubmitting = false;
      this.isValidationFire = true;
      console.log('Form validation failed');
    }
  }

  private getFormErrors(): any {
    const errors: any = {};
    Object.keys(this.formGroup.controls).forEach(key => {
      const control = this.formGroup.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }
    });
    return errors;
  }

  onSubmit(_data?: any): void {
    console.log('onSubmit called with data:', _data);

    // Guard against multiple submissions
    if (this.isSubmitting || this.isLoading) {
      console.log('Submission blocked - already submitting or loading');
      return;
    }

    this.isSubmitting = true;
    this.isFormSubmitted = true;
    this.isLoading = true;

    const formValues = this.formGroup.value;
    console.log('Form values for submission:', formValues);

    const body = new AddDocumentCommand({
      documentCategoryId: formValues.categoryId ?? 0,
      attachmentId: formValues.attachmentId ?? 0,
      fundId: this.data.fundId ?? 0
    });

    console.log('Submitting document command:', body);

    this.documentProxy.add(body).subscribe({
      next: (response: any) => {
        console.log('Document upload success:', response);
        this.isLoading = false;
        this.isSubmitting = false;

        if (response.successed) {
          this.errorModalService.showSuccess('DOCUMENTS.UPLOAD_SUCCESS');
          this.dialogRef.close(true);
        } else {
          this.errorModalService.showError(response.message || 'DOCUMENTS.UPLOAD_FAILED');
          this.isFormSubmitted = false;
          this.isValidationFire = false;
        }
      },
      error: (error: any) => {
        console.error('Document upload error:', error);
        this.isLoading = false;
        this.isSubmitting = false;
        this.isFormSubmitted = false;
        this.isValidationFire = false;
        this.errorModalService.showError('DOCUMENTS.UPLOAD_FAILED');
      },
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
