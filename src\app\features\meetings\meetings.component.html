<div class="meetings-container">
  <!-- Breadcrumb -->
  <!-- <app-breadcrumb
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium"
    divider=">"
    data-testid="breadcrumb"
    (onClickEvent)="onBreadcrumbClicked($event)">
  </app-breadcrumb> -->

 <div class="mb-4">

        <app-page-header title="FUND_DETAILS.MEETINGS" [showBackButton]="true" [showSearch]="false" [showFilter]="false"></app-page-header>

    </div>
  <!-- Document Tabs -->
  <div *ngIf="!isLoading && documentCategories.length > 0" class="meetings-content">
    <mat-tab-group
      [(selectedIndex)]="selectedTabIndex"
      (selectedTabChange)="onTabChange($event.index)"
      class="meetings-tabs"
      data-testid="document-tabs">

      <mat-tab
        *ngFor="let category of documentCategories; let i = index">
        <ng-template mat-tab-label>
            {{ category.name | translate  }}
            <span *ngIf="category.component === 'proposal'" class="span-value">{{totalCount}}</span>
        </ng-template>
        <app-proposal-list
            *ngIf="category.component === 'proposal'"
            (dataSelected)="handleData($event)">
        </app-proposal-list>

        <!-- Show meetings list when i === 1 -->
        <app-meetings-list
            *ngIf="category.component === 'meetings'">
        </app-meetings-list>
      </mat-tab>
    </mat-tab-group>
  </div>

  <div *ngIf="!isLoading && documentCategories.length === 0" class="empty-state">
    <div class="empty-state-content">
      <h3>{{ 'DOCUMENTS.NO_CATEGORIES' | translate }}</h3>
      <p>{{ 'DOCUMENTS.NO_CATEGORIES_MESSAGE' | translate }}</p>
    </div>
  </div>
</div>
