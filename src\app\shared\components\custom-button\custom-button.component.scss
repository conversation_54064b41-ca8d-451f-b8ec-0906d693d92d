img{
  width: 24px;
  height: 24px;
}
// Mobile responsive styles

@media (max-width: 1024px) {
   img {
    width: 15px !important;
    height: 15px !important;
  }
}
@media (max-width: 1024px) {
  :host {
    width: auto;

    button {
      width: 100% !important;
      padding: 12px 24px;
      font-size: 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 15px;
        height: 15px;
      }
    }
  }
}

// Extra small devices
@media (max-width: 480px) {
    img {
    width: 12px !important;
    height: 12px !important;
  }

  :host button {
    padding: 6px 16px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 400;
    span{
      position: relative;
      bottom: 2px;
}
  }
}

@media (max-width: 320px) {
  img {
    width: 12px !important;
    height: 12px !important;
  }

  :host button {
    font-size: 10px;
    padding: 6px 12px;
  }
}

