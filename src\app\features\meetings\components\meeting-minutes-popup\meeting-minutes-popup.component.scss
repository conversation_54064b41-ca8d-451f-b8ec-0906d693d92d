.meeting-minutes-popup {
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 12px;
  overflow: hidden;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 16px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;

    .popup-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      background: none;
      border: none;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #666;
      transition: all 0.2s ease;

      &:hover {
        background: #e9ecef;
        color: #333;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .meeting-info {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;

    .meeting-subject {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #555;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 24px;
    text-align: center;

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 24px;
    text-align: center;

    .error-icon {
      margin-bottom: 16px;

      i {
        font-size: 48px;
        color: #dc3545;
      }
    }

    .error-message {
      margin: 0 0 24px;
      color: #666;
      font-size: 14px;
    }

    .retry-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: background-color 0.2s ease;

      &:hover {
        background: #0056b3;
      }

      i {
        font-size: 12px;
      }
    }
  }

  .content-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px;

    .status-container {
      margin-bottom: 20px;

      .status-badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.status-draft {
          background: #fff3cd;
          color: #856404;
          border: 1px solid #ffeaa7;
        }

        &.status-final {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }
      }
    }

    .minutes-content {
      .content-wrapper {
        line-height: 1.6;
        color: #333;
        font-size: 14px;

        // Style for rich text content
        ::ng-deep {
          h1, h2, h3, h4, h5, h6 {
            margin-top: 24px;
            margin-bottom: 12px;
            font-weight: 600;
          }

          h1 { font-size: 24px; }
          h2 { font-size: 20px; }
          h3 { font-size: 18px; }
          h4 { font-size: 16px; }
          h5 { font-size: 14px; }
          h6 { font-size: 12px; }

          p {
            margin-bottom: 12px;
          }

          ul, ol {
            margin-bottom: 12px;
            padding-left: 24px;

            li {
              margin-bottom: 6px;
            }
          }

          blockquote {
            margin: 16px 0;
            padding: 12px 16px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            font-style: italic;
          }

          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 16px;

            th, td {
              padding: 8px 12px;
              border: 1px solid #dee2e6;
              text-align: left;
            }

            th {
              background: #f8f9fa;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .no-minutes-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 24px;
    text-align: center;

    .no-minutes-icon {
      margin-bottom: 16px;

      i {
        font-size: 48px;
        color: #6c757d;
      }
    }

    .no-minutes-message {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .popup-footer {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;

    .close-btn-footer {
      background: #6c757d;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s ease;

      &:hover {
        background: #545b62;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .meeting-minutes-popup {
    max-width: 95vw;
    margin: 20px;

    .popup-header,
    .meeting-info,
    .content-container,
    .popup-footer {
      padding-left: 16px;
      padding-right: 16px;
    }

    .popup-header .popup-title {
      font-size: 18px;
    }
  }
}
