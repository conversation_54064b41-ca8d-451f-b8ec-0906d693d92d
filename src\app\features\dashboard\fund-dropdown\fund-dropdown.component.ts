import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges, HostListener, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// API Types
import { DefaultFundInfoDto } from '@core/api/api.generated';
import { NgSelectModule } from "@ng-select/ng-select";

@Component({
  selector: 'app-fund-dropdown',
  standalone: true,
  imports: [CommonModule, FormsModule, TranslateModule, NgSelectModule],
  templateUrl: './fund-dropdown.component.html',
  styleUrl: './fund-dropdown.component.scss'
})
export class FundDropdownComponent implements OnChanges {
  @Input() funds: DefaultFundInfoDto[] = [];
  @Input() selectedFundId: number | null = null;
  @Input() loading: boolean = false;
  @Input() disabled: boolean = false;

  @Output() fundChanged = new EventEmitter<number>();

  // Component state
  selectedFund: DefaultFundInfoDto | null = null;
  dropdownOpen: boolean = false;
  clickCount: number = 0;

  constructor(private elementRef: ElementRef) {
    // Add some test funds if none provided
     this.updateSelectedFund();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['funds'] || changes['selectedFundId']) {
      this.updateSelectedFund();
    }
  }

  /**
   * Update selected fund based on selectedFundId or default to first fund
   */
  private updateSelectedFund(): void {
    if (!this.funds || this.funds.length === 0) {
      this.selectedFund = null;
      return;
    }

    // If selectedFundId is provided, find the matching fund
    if (this.selectedFundId !== null) {
      const fund = this.funds.find(f => f.fundId === this.selectedFundId);
      if (fund) {
        this.selectedFund = fund;
        return;
      }
    }

    // Default to first fund if no selection or fund not found
    this.selectedFund = this.funds[0];
    if (this.selectedFund && this.selectedFund.fundId !== this.selectedFundId) {
      // Emit the default selection
      this.onFundSelect(this.selectedFund);
    }
  }

  /**
   * Handle fund selection
   */
  onFundSelect(fund: DefaultFundInfoDto): void {
    if (this.disabled || this.loading) return;

    this.selectedFund = fund;
    this.dropdownOpen = false;

    if (fund.fundId !== undefined) {
      this.fundChanged.emit(fund.fundId);
    }
  }

  /**
   * Handle trigger click with aggressive debugging
   */
  onTriggerClick(event: Event): void {
    // Prevent any event propagation
    event.preventDefault();
    event.stopPropagation();
    // Force toggle regardless of state
    this.dropdownOpen = !this.dropdownOpen;
  }

  /**
   * Handle fund selection change from select element
   */
 onFundSelectionChange(fundId: number): void {
  if (fundId && !isNaN(fundId)) {
    this.selectedFundId = fundId;
    this.updateSelectedFund();
    this.fundChanged.emit(fundId);
  }
}


  /**
   * Toggle dropdown open/close
   */
  toggleDropdown(): void {
    this.dropdownOpen = !this.dropdownOpen;
  }

  /**
   * Close dropdown
   */
  closeDropdown(): void {
    this.dropdownOpen = false;
  }

  /**
   * Handle keyboard navigation
   */
  onKeyDown(event: KeyboardEvent): void {
    if (this.disabled || this.loading) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        this.toggleDropdown();
        break;
      case 'Escape':
        event.preventDefault();
        this.closeDropdown();
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!this.dropdownOpen) {
          this.toggleDropdown();
        } else {
          this.navigateToNextFund();
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (this.dropdownOpen) {
          this.navigateToPreviousFund();
        }
        break;
    }
  }

  /**
   * Navigate to next fund in list
   */
  private navigateToNextFund(): void {
    if (!this.selectedFund || !this.funds.length) return;

    const currentIndex = this.funds.findIndex(f => f.fundId === this.selectedFund?.fundId);
    const nextIndex = (currentIndex + 1) % this.funds.length;
    this.onFundSelect(this.funds[nextIndex]);
  }

  /**
   * Navigate to previous fund in list
   */
  private navigateToPreviousFund(): void {
    if (!this.selectedFund || !this.funds.length) return;

    const currentIndex = this.funds.findIndex(f => f.fundId === this.selectedFund?.fundId);
    const previousIndex = currentIndex > 0 ? currentIndex - 1 : this.funds.length - 1;
    this.onFundSelect(this.funds[previousIndex]);
  }

  /**
   * Handle click outside dropdown to close it
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (!this.dropdownOpen) return;

    const target = event.target as HTMLElement;
    const dropdownElement = this.elementRef.nativeElement;

    // Check if click is outside the dropdown component
    if (dropdownElement && !dropdownElement.contains(target)) {
      this.closeDropdown();
    }
  }

  /**
   * Get display name for fund
   */
  getFundDisplayName(fund: DefaultFundInfoDto): string {
    return fund.fundName  || `Fund ${fund.fundId}`;
  }

  /**
   * Get selected fund display name
   */
  get selectedFundDisplayName(): string {
    if (!this.selectedFund) {
      // Show placeholder text or first fund name if available
      if (this.funds && this.funds.length > 0) {
        return this.getFundDisplayName(this.funds[0]);
      }
      return 'اختر صندوق'; // Arabic for "Select Fund"
    }
    return this.getFundDisplayName(this.selectedFund);
  }

  /**
   * Check if dropdown has funds
   */
  get hasFunds(): boolean {
    return this.funds && this.funds.length > 0;
  }

  /**
   * Get dropdown CSS classes
   */
  get dropdownClasses(): string {
    const classes = ['fund-dropdown'];

    if (this.dropdownOpen) classes.push('fund-dropdown--open');
    if (this.loading) classes.push('fund-dropdown--loading');
    if (this.disabled) classes.push('fund-dropdown--disabled');
    if (!this.hasFunds) classes.push('fund-dropdown--empty');

    return classes.join(' ');
  }

  /**
   * TrackBy function for funds list
   */
  trackByFundId(_index: number, fund: DefaultFundInfoDto): number | undefined {
    return fund.fundId;
  }
}
