import { Component } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { CustomButtonComponent } from '../custom-button/custom-button.component';
import { ButtonTypeEnum } from '@core/enums/icon-enum';

@Component({
  selector: 'app-unauthorized',
  standalone: true,
  imports: [CommonModule, TranslateModule, CustomButtonComponent],
  templateUrl: './unauthorized.component.html',
  styleUrls: ['./unauthorized.component.scss']
})
export class UnauthorizedComponent {
  ButtonTypeEnum = ButtonTypeEnum; // Make enum available in template

  constructor(
    private location: Location,
    private router: Router
  ) {}

  goBack(): void {
    this.location.back();
  }

  goToHomePage(): void {
    // localStorage.removeItem('auth_token');
    // localStorage.removeItem('refresh_token');
    // this.router.navigate(['/auth/login']);
    this.router.navigate(['/admin/dashboard']);
  }

}
