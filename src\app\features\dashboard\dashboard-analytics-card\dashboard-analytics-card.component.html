<div class="dashboard-analytics-card" [ngClass]="cardClass">
  <!-- Main Card Header with Total Count -->
  <div class="card-header" *ngIf="mainItem">
    <div class="main-metric">
      <div class="metric-value">{{ mainItem.value }}</div>
      <div class="metric-title">{{ mainItem.title | translate }}</div>
    </div>
  </div>

  <!-- Sub Items Grid -->
  <div class="card-body" *ngIf="subItems.length > 0">
    <div class="sub-metrics-grid">
      <div
        class="sub-metric-item"
        *ngFor="let item of subItems; trackBy: trackByTitle"
        [ngClass]="'sub-metric-item--' + item.color">

        <!-- Icon and Value -->
        <div class="metric-content">
          <div class="metric-icon" [ngClass]="getItemColorClass(item.color)">
            <i [class]="item.icon" aria-hidden="true"></i>
          </div>
          <div class="metric-details">
            <div class="metric-value">{{ item.value }}</div>
            <div class="metric-title">{{ item.title | translate }}</div>
          </div>
        </div>

        <!-- Progress Bar (for visual representation) -->
        <div class="metric-progress" *ngIf="mainItem && mainItem.value > 0">
          <div class="progress">
            <div
              class="progress-bar"
              [ngClass]="getItemBgClass(item.color)"
              [style.width.%]="getPercentage(item.value)"
              [attr.aria-valuenow]="item.value"
              [attr.aria-valuemax]="mainItem.value">
            </div>
          </div>
          <small class="percentage-text">{{ getPercentage(item.value) }}%</small>
        </div>
      </div>
    </div>
  </div>

  <!-- Chart Section (if enabled) -->
  <div class="card-footer" *ngIf="showChart && getChartData()">
    <div class="chart-container">
      <!-- Placeholder for chart implementation -->
      <div class="chart-placeholder">
        <i class="fas fa-chart-pie text-muted"></i>
        <small class="text-muted">{{ 'DASHBOARD.CHART_PLACEHOLDER' | translate }}</small>
      </div>
    </div>
  </div>
</div>
