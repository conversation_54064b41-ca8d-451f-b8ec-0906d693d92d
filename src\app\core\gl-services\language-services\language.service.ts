import { EventEmitter, Injectable } from '@angular/core';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';
import { UserManagementServiceProxy, EditUserPreferredLanguageCommand } from '@core/api/api.generated';
import { TokenService } from 'src/app/features/auth/services/token.service';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {

  currentLanguageEvent = new EventEmitter<LanguageEnum>();
  private currentLangSubject = new BehaviorSubject<string>('');
  currentLang$ = this.currentLangSubject.asObservable();

  constructor(
    private translate: TranslateService,
    private userManagementService: UserManagementServiceProxy,
    private tokenService: TokenService
  ) {}

  initLang(){
    let currentLang = JSON.parse(localStorage.getItem("lang") || '{}');
    if (currentLang && Object.keys(currentLang).length === 0 && currentLang.constructor === Object){
      this.switchLangCallBack(LanguageEnum.ar, false); // Don't reload during initialization
    }
    else{
      this.switchLangCallBack(currentLang as LanguageEnum, false); // Don't reload during initialization
    }
  }
  setLanguage(lang: string) {
    localStorage.setItem('lang', lang);
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    this.currentLangSubject.next(lang);
  }
  switchLang(language: LanguageEnum,shouldReload: boolean) {
     this.updateUserLanguagePreference(language,shouldReload);
  }
  switchLangCallBack(language: LanguageEnum, shouldReload: boolean ) {
    this.translate.use(language).subscribe(_res => {
      this.currentLanguageEvent.emit(language);
    })

    /*used for refreshment case*/
    localStorage.setItem('lang', JSON.stringify(language));

    // Map language enum to proper locale codes for HTML lang attribute
    const localeMap: { [key in LanguageEnum]: string } = {
      [LanguageEnum.ar]: 'ar-EG',
      [LanguageEnum.en]: 'en-US'
    };

    if (language === LanguageEnum.ar) {
      document.getElementsByTagName('html')[0].setAttribute('dir', 'rtl');
      document.getElementsByTagName('html')[0].setAttribute('lang', localeMap[LanguageEnum.ar]);
      console.log('Set direction to RTL for Arabic with locale:', localeMap[LanguageEnum.ar]); // Debug log
      console.log('Current HTML dir attribute:', document.documentElement.getAttribute('dir')); // Debug log
    } else {
      document.getElementsByTagName('html')[0].setAttribute('dir', 'ltr');
      document.getElementsByTagName('html')[0].setAttribute('lang', localeMap[LanguageEnum.en]);
      console.log('Set direction to LTR for English with locale:', localeMap[LanguageEnum.en]); // Debug log
      console.log('Current HTML dir attribute:', document.documentElement.getAttribute('dir')); // Debug log
    }

    // Update the BehaviorSubject
    this.currentLangSubject.next(language);

    // Reload the page to ensure all components reflect the new language
    if (shouldReload) {
      // Use setTimeout to ensure localStorage is updated before reload
     // setTimeout(() => {
        window.location.reload();
     // }, 400);
    }
  }

  get currentLang(): string {
    return this.currentLangSubject.value;
  }

  private updateUserLanguagePreference(language: LanguageEnum,shouldReload: boolean): void {
    if (!this.tokenService.isLoggedIn()) {
      return;
    }
    const userId = this.tokenService.getuserId();
    if (!userId) {
      console.warn('User ID not found, cannot update language preference');
      return;
    }
    const languageMap: { [key in LanguageEnum]: string } = {
      [LanguageEnum.ar]: 'ar-EG',
      [LanguageEnum.en]: 'en-US'
    };
    const command = new EditUserPreferredLanguageCommand({
      id: parseInt(userId),
      userPreferredLanguage: languageMap[language]
    });
    this.userManagementService.updateUserLanguage(command).subscribe({
      next: () => {
        this.switchLangCallBack(language,shouldReload);
      },
      error: (error: any) => {
        console.error('Failed to update user language preference:', error);
      }
    });
  }
}
