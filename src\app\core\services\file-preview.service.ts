import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MinIOFileServiceProxy, MinIOPreviewCommand, API_BASE_URL } from '@core/api/api.generated';
import { Inject, Optional } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class FilePreviewService {
  private baseUrl: string;

  constructor(
    private minIOService: MinIOFileServiceProxy,
    private http: HttpClient,
    @Optional() @Inject(API_BASE_URL) baseUrl?: string
  ) {
    this.baseUrl = baseUrl ?? '';
  }


  previewFile(file: any, fallbackCallback?: () => void): void {
    const attachmentId = this.extractAttachmentId(file);
    
    if (!attachmentId) {
      console.warn('No attachment ID found for preview, falling back to download');
      if (fallbackCallback) {
        fallbackCallback();
      }
      return;
    }

    const fileName = file?.fileName || file?.name || '';

    // Create preview command
    const previewCommand = new MinIOPreviewCommand({
      id: attachmentId,
      bucketName: '',
      expiryInMinutes: 0 
    });
    const url = `${this.baseUrl}/api/MinIO/MinIOFile/Preview`;
    const content = JSON.stringify(previewCommand);
    const options = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
    };

    this.http.post(url, content, options).subscribe({
      next: (response: any) => {
        console.log('Preview request sent successfully', response);
        if (response && response?.data?.previewUrl) {
          this.downloadFile(response?.data?.previewUrl, fileName);
        } else {
          console.log('Preview API called successfully, no URL returned');
        }
      },
      error: (error) => {
        console.error('Error previewing file:', error);
        if (fallbackCallback) {
          fallbackCallback();
        }
      }
    });
  }


  previewFileById(attachmentId: number, fileName: string, fallbackCallback?: () => void): void { 
    // Create preview command
    const previewCommand = new MinIOPreviewCommand({
      id: attachmentId,
      bucketName: '',
      expiryInMinutes: 0
    });

    // Call preview API using direct HTTP call
    const url = `${this.baseUrl}/api/MinIO/MinIOFile/Preview`;
    const content = JSON.stringify(previewCommand);
    const options = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
    };

    this.http.post(url, content, options).subscribe({
      next: (response: any) => {
        console.log('Preview request sent successfully', response);
        if (response && (response?.previewUrl || response?.data?.previewUrl)) {
          this.downloadFile((response?.previewUrl || response?.data?.previewUrl), fileName);
        } else {
          console.log('Preview API called successfully, no URL returned');
        }
      },
      error: (error) => {
        console.error('Error previewing file:', error);
        if (fallbackCallback) {
          fallbackCallback();
        }
      }
    });
  }


  private extractAttachmentId(file?: any): number | undefined {
    if (!file) return undefined;

    // Try different possible property names for attachment ID
    return file.id || file.attachmentId || file.fileId || undefined;
  }


  downloadFile(url: string, fileName: string): void {
    if (!url) {
      console.error('No URL provided for download');
      return;
    }

    // If it's a preview URL (blob or data URL), open in new tab for preview
    if (url.startsWith('blob:') || url.startsWith('data:') || url.includes('preview')) {
      console.log('Opening preview URL in new tab:', url);
      window.open(url, '_blank');
    } else {
      // For regular file URLs, create download link
      console.log('Downloading file from URL:', url);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName || 'download';
      a.target = '_blank';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  }
}
