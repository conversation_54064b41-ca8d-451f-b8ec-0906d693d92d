<div class="form-container p-4 mt-3">
    <h3 class="header font-weight-bold">
        <span>
            <img src="assets/images/filter-1.png" alt="filter">
        </span>
        {{'INVESTMENT_FUNDS.MEETING.ADVANCED_SEARCH' | translate}}
    </h3>

    <!-- Form -->
     <div class="form-wrapper">
         <app-form-builder
             *ngIf="!isLoadingData"
             [formControls]="formControls"
             [formGroup]="formGroup"
             [isFormSubmitted]="isFormSubmitted"
             (dropdownChanged)="dropdownChanged($event)">
         </app-form-builder>

    <div class="mt-3 row">
        <div class="col-6 d-flex align-items-center">
            <app-custom-button
                class="w-100 p-1 fs-14"
                [btnName]="'INVESTMENT_FUNDS.MEETING.APPLY_FILTERS' | translate"
                [buttonType]="buttonEnum.Primary"
                [iconName]="IconEnum.verify"
                [disabled]="isLoadingData"
                (click)="applyFilters()">
            </app-custom-button> 
           
        </div>
        <div class="col-6 d-flex align-items-center">
            <app-custom-button
                class="w-100 p-1 fs-14"
                [btnName]="'USER_MANAGEMENT.FILTERS.RESET' | translate"
                [buttonType]="buttonEnum.OutLine"
                [iconName]="IconEnum.reset"
                (click)="resetFilters()">
            </app-custom-button>
        </div>
    </div>
     </div>
</div>
