<!-- Main Content -->
<div class="user-profile-page">
  <!-- Breadcrumb -->
  <!-- <app-breadcrumb [breadcrumbs]="breadcrumbItems"  [size]="breadcrumbSizeEnum.Medium" (onClickEvent)="onBreadcrumbClicked($event)" divider=">"></app-breadcrumb> -->

  <div class="mt-3">
    <!-- Page Header -->
    <app-page-header [title]="(isViewMode ? 'USER_PROFILE.VIEW_TITLE' : 'USER_PROFILE.PAGE_TITLE') | translate">
    </app-page-header>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading && !currentUserData" class="loading-container text-center">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="mt-2">{{ 'USER_PROFILE.LOADING_PROFILE' | translate }}</p>
  </div>

  <!-- Form Container -->
  <div *ngIf="!isLoading || currentUserData" class="form-container mt-3">
      <!-- Profile Photo Section -->
      <div class="profile-photo-section text-center mb-4">
        <div class="photo-container">
          <div class="profile-photo-circle">
            <img [src]="getUserPhotoUrl()" [alt]="'USER_PROFILE.PERSONAL_PHOTO' | translate" class="profile-photo"
              (error)="onImageError($event)">
          </div>
          <!-- Only show upload overlay in edit mode -->
          <div *ngIf="!isViewMode" class="photo-upload-overlay">
            <i class="fas fa-camera"></i>
          </div>
        </div>
        <p class="photo-label mt-2">{{ 'USER_PROFILE.PERSONAL_PHOTO' | translate }}</p>
      </div>

      <!-- Form Fields -->
      <div class="form-fields-section">
        <!-- View Mode - Display as Labels -->
        <div *ngIf="isViewMode" class="view-mode-fields">
          <div class="row">
            <!-- Basic Information Section -->
            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.NAME' | translate }}</label>
              <div class="field-value">{{ currentUserData?.data?.fullName || '-' }}</div>
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.EMAIL' | translate }}</label>
              <div class="field-value">{{ currentUserData?.data?.email || '-' }}</div>
            </div>


            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.MOBILE' | translate }}</label>
              <div class="field-value"> +966{{ currentUserData?.data?.userName || '-' }}</div>
            </div>

            <!-- Additional Information -->
            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.IBAN' | translate }}</label>
              <div class="field-value">{{ currentUserData?.data?.iban || '-' }}</div>
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.NATIONALITY' | translate }}</label>
              <div class="field-value">{{ currentUserData?.data?.nationality || '-' }}</div>
            </div>

            <!-- Document Information -->
            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.PASSPORT_NO' | translate }}</label>
              <div class="field-value">{{ currentUserData?.data?.passportNo || '-' }}</div>
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.CV' | translate }}</label>
              <div class="field-value">
                <!-- Show CV download if file exists -->
                <div *ngIf="currentUserData?.data?.cvFile" class="attachment-card mb-2">
                  <div class="download-icon" (click)="downloadFile()">
                    <img src="assets/icons/download.png" alt="download" />
                  </div>
                  <div class="file-info" (click)="downloadFile()">
                    <span class="file-name">
                      {{currentUserData?.data?.cvFile?.fileName || 'CV' }}
                    </span>
                  </div>
                </div>
                <!-- Show message when no CV is available -->
                <span *ngIf="!currentUserData?.data?.cvFile">-</span>
              </div>
            </div>

            <!-- Status Information -->
            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.STATUS' | translate }}</label>
              <div class="field-value">
                <span class="badge" [class]="currentUserData?.data?.isActive ? 'badge-success' : 'badge-secondary'">
                  {{ (currentUserData?.data?.isActive ? 'USER_MANAGEMENT.STATUS.ACTIVE' :
                  'USER_MANAGEMENT.STATUS.INACTIVE') | translate }}
                </span>
              </div>
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label">{{ 'USER_PROFILE.ROLE' | translate }}</label>
              <div class="field-value">
                {{ (currentUserData?.data?.roles && currentUserData?.data?.roles.length > 0) ?
                  currentUserData?.data?.roles.map((role: any) => role?.name).join(', ') : '-' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Edit Mode - Display as Form -->
        <div *ngIf="!isViewMode">
          <app-form-builder [formGroup]="userProfileForm" [formControls]="formControls"
            [isFormSubmitted]="isFormSubmitted" (valueChanged)="onValueChange($event.event, $event.control)"
            (keyPressed)="onKeyPressed($event.event, $event.control)"
            (dropdownChanged)="onDropdownChange($event.event, $event.control)" (fileUploaded)="onFileUploaded($event)">
          </app-form-builder>
        </div>
      </div>

      <!-- Change Password Section - Only show in edit mode -->
      <div *ngIf="!isViewMode && showChangePassword" class="mt-4 mb-4">
        <app-change-password
          [userId]="currentUserData?.data?.id"
          (passwordChanged)="onPasswordChanged()"
          (cancelled)="onChangePasswordCancelled()">
        </app-change-password>
      </div>

      <!-- Change Password Toggle Button - Only show in edit mode when not showing form -->
      <div *ngIf="!isViewMode && !showChangePassword" class="change-password-toggle mt-4 mb-4">
        <button type="button" class="btn btn-link change-password-link" (click)="onToggleChangePassword()">
          <i class="fas fa-key me-2"></i>
          {{ 'USER_PROFILE.CHANGE_PASSWORD' | translate }}
        </button>
      </div>

      <!-- Action Buttons -->
      <div class="actions justify-content-end">
        <app-custom-button [buttonType]="ButtonTypeEnum.Secondary"
          [btnName]="(isViewMode ? 'COMMON.BACK' : 'COMMON.CANCEL') | translate" (click)="onCancel()">
        </app-custom-button>

        <!-- Save button only in edit mode -->
        <app-custom-button *ngIf="!isViewMode" [buttonType]="ButtonTypeEnum.Primary"
          [btnName]="'COMMON.SAVE_CHANGES' | translate" type="submit">
        </app-custom-button>
      </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !currentUserData" class="error-container text-center">
    <div class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ 'USER_PROFILE.LOAD_ERROR' | translate }}
    </div>
    <app-custom-button [buttonType]="ButtonTypeEnum.Primary" [btnName]="'COMMON.RETRY' | translate"
      (click)="loadUserData()">
    </app-custom-button>
  </div>
</div>
