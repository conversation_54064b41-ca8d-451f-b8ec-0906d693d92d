<div class="meetings-attendance-pie-chart">
  <!-- Loading State -->
  <div *ngIf="loading" class="chart-loading d-flex justify-content-center align-items-center" style="height: 140px;">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !hasData" class="chart-empty d-flex flex-column justify-content-center align-items-center" style="height: 172px;">
    <i class="fas fa-calendar-times fa-3x mb-2 text-muted"></i>
    <h5 class="text-muted mb-0">{{ 'DASHBOARD_PAGE.NO_MEETING_DATA' | translate }}</h5>
    <p class="text-muted small mb-0">{{ 'DASHBOARD_PAGE.NO_MEETING_DATA_DESC' | translate }}</p>
  </div>

  <!-- Chart with Data -->
  <div *ngIf="!loading && hasData" class="chart-container">
    <div class="d-flex justify-content-between align-items-center">


    <!-- Pie Chart -->


    <!-- Chart Legend -->
    <div class="">
    <div class="chart-legend">
      <div class="legend-items">
        <div *ngFor="let item of chartData" class="legend-item">
          <div class="legend-color" [ngStyle]="{ 'background-color': item.extra?.color }"></div>
          <span class="legend-text">{{ item.name }} ({{ item.value }})</span>
        </div>
      </div>
    </div>
    </div>
      <div class="">
    <div class="chart-wrapper mb-0">
      <div class="donut-container">
      <ngx-charts-pie-chart
        [view]="view"
        [results]="chartData"
        [scheme]="colorScheme"
        [legend]="showLegend"
        [labels]="showLabels"
        [doughnut]="doughnut"
        [explodeSlices]="explodeSlices"
        [animations]="animations"
        [gradient]="gradient"
        [tooltipDisabled]="tooltipDisabled">
      </ngx-charts-pie-chart>
        <div class="donut-center">
    {{ centerPercentage }}%
  </div>
  </div>
    </div>
    </div>
  </div>
  </div>
</div>
