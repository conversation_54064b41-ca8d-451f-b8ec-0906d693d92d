<div class="meetings-attendance-pie-chart">
  <!-- Loading State -->
  <div *ngIf="loading" class="chart-loading d-flex justify-content-center align-items-center" style="height: 125px;">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !hasData" class="chart-empty d-flex flex-column justify-content-center align-items-center" style="height: 125px;">
    <i class="fas fa-calendar-times fa-3x mb-2 text-muted"></i>
    <h5 class="text-muted mb-0">{{ 'DASHBOARD_PAGE.NO_MEETING_DATA' | translate }}</h5>
    <p class="text-muted small mb-0">{{ 'DASHBOARD_PAGE.NO_MEETING_DATA_DESC' | translate }}</p>
  </div>

  <!-- Chart with Data -->
  <div *ngIf="!loading && hasData" class="chart-container">
    <!-- Chart Header with Meeting Type Dropdown -->
    <!-- <div class="chart-header">
      <div class="chart-title-section">
        <h6 class="chart-title">{{ 'DASHBOARD_PAGE.MEETING_ATTENDANCE' | translate }}</h6>
        <div class="chart-summary">
          <div class="summary-item">
            <span class="summary-label">{{ 'DASHBOARD_PAGE.TOTAL_MEETINGS' | translate }}:</span>
            <span class="summary-value">{{ getTotalMeetings() }}</span>
          </div>
        </div>
      </div>
    </div> -->

    <!-- Pie Chart -->
    <div class="chart-wrapper mb-0">
      <ngx-charts-pie-chart
        [view]="view"
        [results]="chartData"
        [scheme]="colorScheme"
        [legend]="showLegend"
        [labels]="showLabels"
        [doughnut]="doughnut"
        [explodeSlices]="explodeSlices"
        [animations]="animations"
        [gradient]="gradient"
        [tooltipDisabled]="tooltipDisabled">
      </ngx-charts-pie-chart>
    </div>

    <!-- Chart Legend -->
    <div class="chart-legend">
      <div class="legend-items">
        <div *ngFor="let item of chartData" class="legend-item">
          <div class="legend-color" [ngStyle]="{ 'background-color': item.extra?.color }"></div>
          <span class="legend-text">{{ item.name }} ({{ item.value }})</span>
        </div>
      </div>
    </div>
  </div>
</div>
