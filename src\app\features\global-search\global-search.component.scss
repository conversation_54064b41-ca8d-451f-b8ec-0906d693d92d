@import "../../../assets/scss/variables";

.search-result-content {
  padding: 10px 0;

  .search-result-item {
    padding: 16px;
    background: $card-background;
    border-radius: 16px;
    border: 1px solid $border-color;
  }
}


.result-category {
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  background-color: $BrandBackground2;
  border: 1px solid $BrandStroke2;
  border-radius: 9999px;
  color: $navy-blue;
  padding: 4px 10px;
}

.result-title {
  font-size: 18px;
  font-weight: 500;
  color: $navy-blue;
  line-height: 28px;
}

// No Results State
.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px 20px;
}

.no-results-content {
  text-align: center;
  max-width: 400px;
}

.no-results-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.no-results-message {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

// Loading State
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 40px 20px;
}

// RTL Support
[dir="rtl"] {
  .search-result-item {
    text-align: right;
  }

  .result-category {
    text-align: right;
  }

  .result-title {
    text-align: right;
  }
}

.pagination-section {
    margin-top: 32px;
    padding: 24px;
    // background: white;
    border-radius: 12px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .pagination-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: 16px;

      .records-info {
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }

      .page-size-selector {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666;
        font-size: 14px;

        label {
          margin: 0;
          font-weight: 500;
        }

        .page-size-select {
          padding: 6px 12px;
          border: 1px solid #ddd;
          border-radius: 6px;
          background: white;
          color: #333;
          font-size: 14px;
          cursor: pointer;
          transition: border-color 0.2s ease;

          &:hover {
            border-color: #007bff;
          }

          &:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
          }
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .pagination-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;

      .pagination-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        color: #333;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        font-weight: 500;
        min-width: 40px;
        justify-content: center;
        background: transparent;
        border: transparent;

        &:hover:not(:disabled) {
          //  background: #f8f9fa;
          border-color: #007bff;
          color: #007bff;
        }

        &:disabled {
          // background: #f8f9fa;
          color: #6c757d;
          cursor: not-allowed;
          opacity: 0.6;
        }

        &.active {
          background: #ebf3fc;
          color: white;
          border-color: #007bff;
          border-radius: 10px;
        }

        .btn-text {
          @media (max-width: 576px) {
            display: none;
          }
        }

        i {
          font-size: 12px;
        }
      }

      .page-numbers {
        display: flex;
        gap: 4px;
        margin: 0 8px;

        .page-number-btn {
          min-width: 44px;
          min-height: 44px;
          padding: 8px 4px;
          color: #00205a;
        }
      }

      @media (max-width: 576px) {
        gap: 4px;

        .pagination-btn {
          padding: 6px 8px;
          font-size: 12px;
        }
      }
    }

    .page-info {
      text-align: center;
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }
  }

// Responsive Design
@media (max-width: 768px) {
  .result-title {
    font-size: 14px;
  }

  .result-category {
    font-size: 11px;
  }
}