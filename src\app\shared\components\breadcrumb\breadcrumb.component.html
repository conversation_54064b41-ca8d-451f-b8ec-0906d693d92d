<div class="breadcrumb-container d-flex flex-wrap"
     [ngClass]="getBreadcrumbSize()">

  <ng-container *ngFor="let item of getDisplayedBreadcrumbs(); let last = last">

    <!-- Ellipsis breadcrumb -->
    <ng-container *ngIf="item.isEllipsis">
      <span class="breadcrumb-item disabled">...</span>
    </ng-container>

    <!-- Visible breadcrumb -->
    <ng-container *ngIf="!item.isEllipsis && !item.hidden">
      <a class="breadcrumb-item"
         [class.disabled]="item.disabled"
         (click)="!item.disabled && onBreadcrumbClick(item)">
        {{ item.label | translate }}
      </a>
    </ng-container>

    <!-- Divider -->
    <ng-container *ngIf="!last && !item.hidden">
      <span class="breadcrumb-divider">{{ divider }}</span>
    </ng-container>

  </ng-container>
</div>
