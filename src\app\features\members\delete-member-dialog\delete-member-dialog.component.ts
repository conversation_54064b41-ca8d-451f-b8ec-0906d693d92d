import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import {
  BoardMembersServiceProxy,
  BoardMemberResponse,
  BoardMemberType,
  DeleteBoardMemberCommand
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';

export interface DeleteMemberDialogData {
  member: BoardMemberResponse;
  fundId: number;
  independentMembersCount: number;
}

@Component({
  selector: 'app-delete-member-dialog',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './delete-member-dialog.component.html',
  styleUrl: './delete-member-dialog.component.scss'
})
export class DeleteMemberDialogComponent {
  isDeleting = false;
  member: BoardMemberResponse;
  fundId: number;
  independentMembersCount: number;

  constructor(
    public dialogRef: MatDialogRef<DeleteMemberDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DeleteMemberDialogData,
    private boardMembersService: BoardMembersServiceProxy,
    private errorModalService: ErrorModalService
  ) {
    this.member = data.member;
    this.fundId = data.fundId;
    this.independentMembersCount = data.independentMembersCount;
  }

  onCancel() {
    this.dialogRef.close(false);
  }

  onConfirm() {
    this.deleteMember();
  }

  private validateDeletion(): boolean {
    // Check if deleting an independent member would violate minimum requirement
    if (this.member.memberType === BoardMemberType._1 && // Member is independent
        this.independentMembersCount <= 2) { // And there are only 2 or fewer independent members

      return false;
    }

    return true;
  }

  get showValidationError(): boolean {
    return this.member.memberType === BoardMemberType._1 && this.independentMembersCount <= 2;
  }

  get showConfirmationDialog(): boolean {
    return !this.showValidationError;
  }

  private deleteMember() {
    this.isDeleting = true;

    const deleteCommand = this.buildDeleteCommand();

    // TODO: Replace with actual API call when backend endpoints are available
    this.boardMembersService.deleteBoardMember(deleteCommand).subscribe({
      next: (response) => {
        this.handleDeleteSuccess(response);
      },
      error: (error) => {
        this.handleDeleteError(error);
      }
    });
  }

  private buildDeleteCommand() : DeleteBoardMemberCommand  {
    return {
      id: this.member.id,
      fundId: this.fundId,
      userId: this.member.userId
    } as DeleteBoardMemberCommand;
  }

  private handleDeleteSuccess(response: any) {
    this.isDeleting = false;
    
    if (response.successed) {
      this.errorModalService.showSuccess(
        'INVESTMENT_FUNDS.MEMBERS.DELETE_SUCCESS'
      );
      this.dialogRef.close(true); // Return true to indicate success
    } else {
      this.errorModalService.showError(
        response.message || 'INVESTMENT_FUNDS.MEMBERS.ERROR_UNKNOWN'
      );
    }
  }

  private handleDeleteError(error: any) {
    this.isDeleting = false;
    console.error('Error deleting board member:', error);
    
    // Handle specific error cases
    if (error.status === 400) {
      this.errorModalService.showError(
        error.error?.message || 'INVESTMENT_FUNDS.MEMBERS.ERROR_VALIDATION'
      );
    } else {
      this.errorModalService.showError(
        'INVESTMENT_FUNDS.MEMBERS.ERROR_UNKNOWN'
      );
    }
  }

  get memberTypeDisplay(): string {
    return this.member.memberType === BoardMemberType._1
      ? 'INVESTMENT_FUNDS.MEMBERS.INDEPENDENT'
      : 'INVESTMENT_FUNDS.MEMBERS.DEPENDENT';
  }

  get isIndependentMember(): boolean {
    return this.member.memberType === BoardMemberType._1;
  }
}
