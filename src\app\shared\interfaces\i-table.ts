// i-table-column.ts

import { ColumnTypeEnum } from "@core/enums/column-type";

export interface StatusObject {
  label: string;
  color: string;
}

export interface ITableColumn {
  columnDef: string;
  header: string;
  columnType: ColumnTypeEnum;
  cell: (element: any) => any;
  class?: string;
  isSortingBy?: boolean;
  displayMode?:ActionDisplayMode;
}
export interface TableActionEvent {
  action: string;
  row: any;
}
export interface SwitchToggleEvent {
  row: any;
  newValue: boolean;
}
export interface TextLinkClickEvent {
  row: any;
  columnDef: string;
}
export enum ActionDisplayMode {
  Flex = 'flex',
  Dropdown = 'dropdown',
}
