import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';


// Core imports
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// Feature components
import { DocumentListComponent } from './components/document-list/document-list.component';
import { DocumentUploadComponent, DocumentUploadDialogData } from './components/document-upload/document-upload.component';

// Services and API
import { TokenService } from '../auth/services/token.service';
import {
  DocumentServiceProxy,
  DocumentCategoryDto,
  GetDocumentCategoriesQuery
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatCardModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    DocumentListComponent,
    DocumentUploadComponent,
    PageHeaderComponent
  ],
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss']
})
export class DocumentsComponent implements OnInit {
    @ViewChild(DocumentListComponent) list!: DocumentListComponent;

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [];
  breadcrumbSizeEnum = SizeEnum;

  // Component state
  isLoading = false;
  currentFundId: number | null = null;
  selectedTabIndex = 0;

  // Document categories (loaded from API)
  documentCategories: DocumentCategoryDto[] = [];

  // Track which tabs have been loaded to implement lazy loading
  loadedTabs = new Set<number>();

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // Permission properties
  canAddDocument = false;

  // Dialog state management
  private isDialogOpen = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private translateService: TranslateService,
    private documentProxy: DocumentServiceProxy,
    private errorModalService: ErrorModalService,
    public tokenService: TokenService,
    private dialog: MatDialog,
    private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit(): void {
    this.loadRouteParams();
    this.initializeBreadcrumb();
    this.loadDocumentCategories();
  }

  private initializeBreadcrumb(): void {
     
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);

    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`,
      },
      { label:'DOCUMENTS.TITLE', url: '', disabled: true },

   ];
   this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  private loadRouteParams(): void {
    this.route.queryParams.subscribe(params => {
      if (params['fundId']) {
        this.currentFundId = +params['fundId'];
        this.updateBreadcrumbWithFund();
        this.initializePermissions();

      }
    });
  }

  private loadDocumentCategories(): void {
    this.isLoading = true;

    // Use NSwag-generated proxy with proper typing
    const query = new GetDocumentCategoriesQuery();

    this.documentProxy.categories(query).subscribe({
      next: (response) => {
        // Response is properly typed as DocumentCategoryDtoListBaseResponse
        if (response?.data && Array.isArray(response.data)) {
          this.documentCategories = response.data;
        } else {
          this.documentCategories = [];
        }
        this.isLoading = false;

        // If no categories are returned, show appropriate message
        if (this.documentCategories.length === 0) {
          console.warn('No document categories returned from API');
        } else {
          // Mark the first tab as loaded by default
          this.loadedTabs.add(0);
        }
      },
      error: (error: any) => {
        console.error('Error loading document categories:', error);
        this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.LOAD_CATEGORIES_FAILED'));
        this.documentCategories = [];
        this.isLoading = false;
      }
    });
  }

  private updateBreadcrumbWithFund(): void {
    debugger;
    if (this.currentFundId) {
      this.breadcrumbItems = [
        {
          label: 'COMMON.HOME',
          url: '/admin/dashboard'
        },
        {
          label: 'INVESTMENT_FUNDS.TITLE',
          url: '/admin/investment-funds'
        },
        {

            label: history.state.fundName || 'BREADCRUMB.FUND_DETAILS',
           url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`,

        },
        {
          label: 'DOCUMENTS.TITLE',
          url: `/admin/documents/fund/${this.currentFundId}`,
          disabled: true
        }
      ];
      this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems)
    }
  }

  onTabChange(index: number): void {
    this.selectedTabIndex = index;
    // Mark this tab as loaded for lazy loading
    if (!this.loadedTabs.has(index)) {
      this.loadedTabs.add(index);
    }
  }

  onUploadDocument(event?: Event | void): void {
    // Prevent event bubbling if event is provided and is an Event object
    if (event && typeof event === 'object' && 'stopPropagation' in event) {
      event.stopPropagation();
      event.preventDefault();
    }

    // Prevent multiple dialog instances
    if (this.isDialogOpen) {
      console.log('Upload dialog is already open, ignoring duplicate request');
      return;
    }

    // Check if there are already open dialogs
    const openDialogs = this.dialog.openDialogs;
    if (openDialogs.length > 0) {
      console.log('Another dialog is already open, ignoring upload request');
      return;
    }

    console.log('Opening upload document dialog');
    this.isDialogOpen = true;

    const dialogData: DocumentUploadDialogData = {
      fundId: this.currentFundId || 0,
      selectedCategory: this.getCurrentCategory(),
      documentCategories: this.documentCategories
    };

    const dialogRef = this.dialog.open(DocumentUploadComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true,
    });

    // Add timeout-based reset as additional safeguard
    const timeoutId = setTimeout(() => {
      if (this.isDialogOpen) {
        console.warn('Dialog state reset due to timeout - possible dialog opening issue');
        this.isDialogOpen = false;
      }
    }, 5000); // Reset after 5 seconds if dialog doesn't close properly

    dialogRef.afterClosed().subscribe((result) => {
      console.log('Upload dialog closed with result:', result);
      clearTimeout(timeoutId); // Clear the timeout since dialog closed properly
      this.isDialogOpen = false; // Reset dialog state

      if (result) {
        this.refreshCurrentTab();
      }
    });
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url) {
      this.router.navigate([item.url]);
    }
  }

  getCurrentCategory(): any {
    return this.documentCategories[this.selectedTabIndex];
  }

  private refreshCurrentTab(): void {
    this.list.loadDocuments();
  }

  onSearch(search:any)
  {
    this.list.loadDocuments(search);
  }

  /**
   * Initialize permission-based access control
   */
  private initializePermissions(): void {


    this.documentProxy.fundPermission(this.currentFundId || undefined).subscribe({
      next: (response) => {
        if (response.successed && response.data) {
          this.canAddDocument = response.data.canAdd && this.tokenService.hasPermission('Document.Create');
        } else {
          this.canAddDocument = false;
        }
      },
      error: (error: any) => {
          this.canAddDocument = false;
      }
    });
  }
}
