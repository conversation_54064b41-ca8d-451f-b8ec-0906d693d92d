import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { TokenService } from 'src/app/features/auth/services/token.service';

@Injectable({ providedIn: 'root' })
export class CreateAssessmentGuard implements CanActivate {
  constructor(
    private tokenService: TokenService,
    private router: Router,
  ) {}

  canActivate(
  ): boolean {
    // Check if user is authenticated first
    if (!this.tokenService.isLoggedIn()) {
      this.router.navigate(['/auth/login']);
      return false;
    }

    if (this.tokenService.hasPermission('Assessment.Create')) {
      return true;
    }else{
      this.router.navigate(['/admin/unauthorized'])
      return false;
    }
  }
}
