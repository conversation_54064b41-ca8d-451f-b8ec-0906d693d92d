import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ViewChild } from '@angular/core';
import { NgModel } from '@angular/forms';
import Swal from 'sweetalert2';
import { ValidationMessagesComponent } from "../../../shared/components/validation/validation-messages.component";


@Component({
  selector: 'app-category-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule,
    ValidationMessagesComponent
],
  templateUrl: './category-dialog.component.html',
  styleUrls: ['./category-dialog.component.scss'],
})
export class CategoryDialogComponent {
  englishName: string = '' ;
  arabicName: string = '';
  displayOrder : number = 0;
  initialArabicName: string = '';
initialEnglishName: string = '';
initialdisplayOrder: string = '';
  @ViewChild('arabicNameRef') arabicNameRef!: NgModel;
  @ViewChild('englishNameRef') englishNameRef!: NgModel;
  constructor(
    public dialogRef: MatDialogRef<CategoryDialogComponent>,
    private translateService: TranslateService,@Inject(MAT_DIALOG_DATA) public data: any
  ) {

  }

  ngOnInit(): void {
    if (this.data.isEdit) {
      this.englishName = this.data.englishName;
      this.arabicName = this.data.arabicName;
this.displayOrder = this.data.displayOrder;
      this.initialEnglishName = this.data.englishName;
      this.initialArabicName = this.data.arabicName;
      this.initialdisplayOrder = this.data.displayOrder;
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }



  onSubmit(): void {
    const isArabicNameValid = this.arabicNameRef?.valid;
    const isEnglishNameValid = this.englishNameRef?.valid;

    const arabicInput = this.arabicName?.trim().toLowerCase();
    const englishInput = this.englishName?.trim().toLowerCase();

    const isArabicDuplicate = this.data.documentCategoryNames.filteredData.some((documentCategory:any) => {
      const sameArabic = documentCategory.nameAr.trim().toLowerCase() === arabicInput;
      const isSameId = this.data?.isEdit && documentCategory.id === this.data.id;
      return sameArabic && !isSameId;
    });

    const isEnglishDuplicate = this.data.documentCategoryNames.filteredData.some((documentCategory:any) => {
      const sameEnglish = documentCategory.nameEn.trim().toLowerCase() === englishInput;
      const isSameId = this.data?.isEdit && documentCategory.id === this.data.id;
      return sameEnglish && !isSameId;
    });

    if (isArabicDuplicate || isEnglishDuplicate) {
      if (isArabicDuplicate) {
        this.arabicNameRef.control.setErrors({ duplicate: true });
        this.arabicNameRef.control.markAsTouched();
      }
      if (isEnglishDuplicate) {
        this.englishNameRef.control.setErrors({ duplicate: true });
        this.englishNameRef.control.markAsTouched();
      }
      return;
    }

    if (isArabicNameValid && isEnglishNameValid) {
      this.dialogRef.close({
        englishName: this.englishName,
        arabicName: this.arabicName,
        displayOrder: this.displayOrder,
      });
    } else {
      this.arabicNameRef.control.markAsTouched();
      this.englishNameRef.control.markAsTouched();
    }
  }
  isChanged(): boolean {
    return this.englishName?.trim() !== this.initialEnglishName?.trim() ||
           this.arabicName?.trim() !== this.initialArabicName?.trim();
  }
markAllFieldsTouched(event: Event): void {
  const target = event.target as HTMLElement;
  const isInputClick =
    target.tagName === 'INPUT' || target.closest('input');

  if (!isInputClick) {
    this.arabicNameRef.control.markAsTouched();
    this.englishNameRef.control.markAsTouched();
  }
}


}
