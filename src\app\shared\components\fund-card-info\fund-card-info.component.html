<div class="fund-card-info w-100"
     [class.disabled]="disabled"
     (click)="!disabled && navigate()"
     >
  <div class="card-header">
    <div class="notification-container"  (click)="!disabled && filterNotificationHandle()">
      <img src="assets/images/notification.png" />
      <span class="notification-badge" *ngIf="notificationCount > 0">
        {{ notificationCount }}
      </span>
    </div>

    <div class="icon-container d-flex justify-content-center align-items-center">
      <div *ngIf="icon == 'resolutionsCount'">
        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56"
          viewBox="0 0 56 56" fill="none">
          <use href="assets/svgs/sprite.svg#resolutionsCount"></use>
        </svg>
      </div>

      <div *ngIf="icon == 'evaluationsNotificationCount'">
        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56"
          viewBox="0 0 56 56" fill="none">
          <use href="assets/svgs/sprite.svg#evaluationsNotificationCount"></use>
        </svg>
      </div>
      <div *ngIf="icon == 'documentsNotificationCount'">
        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56"
          viewBox="0 0 56 56" fill="none">
          <use href="assets/svgs/sprite.svg#documentsNotificationCount"></use>
        </svg>
      </div>
      <div *ngIf="icon == 'meetingsNotificationCount'">
        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56"
          viewBox="0 0 56 56" fill="none">
          <use href="assets/svgs/sprite.svg#meetingsNotificationCount"></use>
        </svg>
      </div>
      <div *ngIf="icon == 'membersNotificationCount'">
        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56"
          viewBox="0 0 56 56" fill="none">
          <use href="assets/svgs/sprite.svg#membersNotificationCount"></use>
        </svg>
      </div>
      <h3 class="title mx-2">{{ title |translate }}</h3>
    </div>
  </div>

  <div class="card-content">
    <img src="assets/images/arrow-goto.png" class="rotate-icon"
      (click)="!disabled && navigate()" />
    <div class="count m-lg-auto">{{ fundCount }}</div>
  </div>
</div>
