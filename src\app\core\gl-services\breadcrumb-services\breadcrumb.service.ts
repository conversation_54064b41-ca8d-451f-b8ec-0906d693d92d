import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { BehaviorSubject, filter } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class BreadcrumbService {
  private breadcrumbSource = new BehaviorSubject<any[]>([]);
  breadcrumb$ = this.breadcrumbSource.asObservable();

  constructor(private router: Router) {
    // Clear breadcrumbs automatically on every route change
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.clearBreadcrumb();
      });
  }

  setBreadcrumbData(data: any[]) {
    this.breadcrumbSource.next(data);
  }

  clearBreadcrumb() {
    this.breadcrumbSource.next([]);
  }
}
