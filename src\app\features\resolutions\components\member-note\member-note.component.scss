.member-note-wrapper {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1rem;

  .member-note-header {
    color: #6b7280;
    font-size: 0.875rem;
    display: flex;

    .img-container {
     // width: 40px;
      padding: 0;
    }
.member-container {
  margin: 0 4px;
  width: 100%;
  padding: 0;
    .member-note-user-info {
      display: flex;
      gap: 0.5rem;

      .user-name {
        color: #00205A;


        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }

      .user-role,
      .user-time {
        color: #828282;
        font-size: 10px;
        font-weight: 400;
        line-height: 16px;
        margin-bottom: 8px;
      }

    }
  }
  }

  .member-note-body {
    margin-top: 0.5rem;
    background-color: #fff;
    border-radius: 6px;
    padding: 0.75rem 0;
    font-size: 1rem;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #333;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;
  }
}
