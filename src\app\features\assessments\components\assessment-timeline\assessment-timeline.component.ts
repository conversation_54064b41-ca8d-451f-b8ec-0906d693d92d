import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AssessmentStatus } from '@core/api/api.generated';
import { DateHijriConverterPipe } from "../../../../shared/pipes/dateHijriConverter/dateHijriConverter.pipe";
import { TranslateModule, TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'app-assessment-timeline',
  standalone: true,
  imports: [CommonModule, DateHijriConverterPipe, TranslateModule],
  templateUrl: './assessment-timeline.component.html',
  styleUrl: './assessment-timeline.component.scss'
})
export class AssessmentTimelineComponent {
  assessmentStatus = AssessmentStatus;
  @Input() steps: any;

  constructor(
    private translateService: TranslateService
  ) {}

  ngOnInit() {
    if (this.steps && Array.isArray(this.steps)) {
      this.steps = this.steps.map((step: any) => ({
        ...step,
        formattedChangeDate: step.formattedChangeDate || '',
        // Convert DateTime object to JavaScript Date for regular date pipes
        createdAt: this.convertToJSDate(step.createdAt),
        // Keep original DateTime for Hijri converter pipe
        originalCreatedAt: step.createdAt,
        hijriDate: step.hijriDate || '',
        displayedAction: step.actionDisplayName || this.mapActionName(step.action),
        displayedUserRole: step.displayedUserRole || this.translateService.instant('ASSESSMENTS.TIMELINE.ROLES.FUND_MANAGER'),
        fullName: step.fullName || this.translateService.instant('ASSESSMENTS.TIMELINE.UNKNOWN_USER'),
        displayedStatus: step.statusDisplayName || this.mapStatusText(step.status),
        iconClass: step.iconClass || this.mapIcon(step.status),
        color: step.color || this.mapColor(step.status),
        rejectionReason: step.rejectionReason || ''
      }));
    }
  }

  mapActionName(action: number): string {
    // Map assessment action enum values to translation keys
    switch (action) {
      case 1: // Created
        return this.translateService.instant('ASSESSMENTS.TIMELINE.ACTIONS.ASSESSMENT_CREATED');
      case 2: // Updated
        return this.translateService.instant('ASSESSMENTS.TIMELINE.ACTIONS.ASSESSMENT_UPDATED');
      case 3: // Submitted for Approval
        return this.translateService.instant('ASSESSMENTS.TIMELINE.ACTIONS.SUBMITTED_FOR_APPROVAL');
      case 4: // Approved
        return this.translateService.instant('ASSESSMENTS.TIMELINE.ACTIONS.ASSESSMENT_APPROVED');
      case 5: // Rejected
        return this.translateService.instant('ASSESSMENTS.TIMELINE.ACTIONS.ASSESSMENT_REJECTED');
      case 6: // Distributed
        return this.translateService.instant('ASSESSMENTS.TIMELINE.ACTIONS.ASSESSMENT_DISTRIBUTED');
      case 8: // Completed
        return this.translateService.instant('ASSESSMENTS.TIMELINE.ACTIONS.ASSESSMENT_COMPLETED');
      default:
        return this.translateService.instant('ASSESSMENTS.TIMELINE.ACTIONS.ASSESSMENT_UPDATE');
    }
  }

  mapStatusText(statusId: number): string {
    switch (statusId) {
      case this.assessmentStatus._1: // Draft
        return this.translateService.instant('ASSESSMENTS.TIMELINE.STATUSES.DRAFT');
      case this.assessmentStatus._2: // Waiting for Approval
        return this.translateService.instant('ASSESSMENTS.TIMELINE.STATUSES.WAITING_FOR_APPROVAL');
      case this.assessmentStatus._3: // Approved
        return this.translateService.instant('ASSESSMENTS.TIMELINE.STATUSES.APPROVED');
      case this.assessmentStatus._4: // Rejected
        return this.translateService.instant('ASSESSMENTS.TIMELINE.STATUSES.REJECTED');
      case this.assessmentStatus._5: // Distributed/Active
        return this.translateService.instant('ASSESSMENTS.TIMELINE.STATUSES.ACTIVE');
      case this.assessmentStatus._6: // Completed
        return this.translateService.instant('ASSESSMENTS.TIMELINE.STATUSES.COMPLETED');
      default:
        return this.translateService.instant('ASSESSMENTS.TIMELINE.STATUSES.UNKNOWN');
    }
  }

  mapIcon(statusId: number): string {
    switch (statusId) {
      case this.assessmentStatus._1: // Draft
        return 'draft';
      case this.assessmentStatus._2: // Waiting for Approval
        return 'pending';
      case this.assessmentStatus._3: // Approved
        return 'confirmed';
      case this.assessmentStatus._4: // Rejected
        return 'rejected';
      case this.assessmentStatus._5: // Distributed/Active
        return 'voting-inProgress';
      case this.assessmentStatus._6: // Completed
        return 'approved';
      default:
        return 'draft';
    }
  }

  mapColor(statusId: number): string {
    switch (statusId) {
      case this.assessmentStatus._1: // Draft
        return '#7555ac';
      case this.assessmentStatus._2: // Waiting for Approval
        return '#856404';
      case this.assessmentStatus._3: // Approved
        return '#27ae60';
      case this.assessmentStatus._4: // Rejected
        return '#e74c3c';
      case this.assessmentStatus._5: // Distributed/Active
      return '#0e700e';
      case this.assessmentStatus._6: // Completed
      return '#2f80ed';
      default:
        return '#7555ac';
    }
  }

  getStatusClass(status: number): string {
    switch (status) {
      case this.assessmentStatus._1: // Draft
        return 'draft';
      case this.assessmentStatus._2: // Waiting for Approval
        return 'pending';
      case this.assessmentStatus._3: // Approved
        return 'confirmed';
      case this.assessmentStatus._4: // Rejected
        return 'rejected';
      case this.assessmentStatus._5: // Distributed/Active
        return 'voting-inProgress';
      case this.assessmentStatus._6: // Completed
        return 'approved';
      default:
        return 'draft';
    }
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === 'en';
  }

  getHijriDate(_gregorian: string): string {
    // Static fallback
    return '21 ذو القعدة 1446';
  }



  private convertToJSDate(dateTime: any): Date {
    if (!dateTime) {
      return new Date();
    }

    // If it's already a JavaScript Date, return it
    if (dateTime instanceof Date) {
      return dateTime;
    }

    // If it's a DateTime object with toJSDate method (Luxon DateTime)
    if (dateTime && typeof dateTime.toJSDate === 'function') {
      return dateTime.toJSDate();
    }

    // If it's a DateTime object with toDate method
    if (dateTime && typeof dateTime.toDate === 'function') {
      return dateTime.toDate();
    }

    // If it's an ISO string
    if (typeof dateTime === 'string') {
      return new Date(dateTime);
    }

    // If it's a timestamp
    if (typeof dateTime === 'number') {
      return new Date(dateTime);
    }

    // Fallback: try to convert to string and parse
    try {
      return new Date(dateTime.toString());
    } catch (error) {
      console.warn('Could not convert DateTime to JavaScript Date:', dateTime, error);
      return new Date();
    }
  }
}
