<div class="voting-container" (click)="navigateToProfile()">
  <div class="voting-header">
    <div class="user-info">
      <img [src]="userImage" alt="User Profile" class="profile-image">
      <div>
  <p class="welcome-text dark-color bold-700  mb-0">
       {{"DASHBOARD.WELCOME" | translate}}
    </p>
    <p class="user-name">  {{fullName}}
    </p>
      </div>

    </div>
    <!-- <div  *ngIf="!isAdministrator"> -->
    <!-- <hr>
    <p class="mb-0 text-center bold-700 font-size-xs dark-blue">
      {{"DASHBOARD.THE_VOTE" | translate}}
      </p> -->

 <!-- <div>

    <ng-select
    class="fund-select"
      id="fundSelect"
      [clearable]="false"
      [(ngModel)]="selectedFundId"
      (change)="onFundChanged($event)"
      [disabled]="isLoading ">
      <ng-option *ngFor="let fund of defaultFunds" [value]="fund.fundId">
        {{fund.fundName}}
      </ng-option>
    </ng-select>
  </div> -->



   <!-- </div> -->
  </div>


  <!-- <div class="voting-content" *ngIf="!isAdministrator">
    <p class="bold-700 font-size-sm light-dark mb-0">
      <span >
        {{"DASHBOARD.HAVE" | translate}} {{remainingVotes}} {{"DASHBOARD.ITEMS_TO_VOTE" | translate}}
      </span>
    </p>
    <button class="btn primary-btn  m-auto vote-button" (click)="goToResolution()" [disabled]="remainingVotes === 0 || isLoading">
      {{"DASHBOARD.VOTE" | translate}}
    </button>
  </div> -->
</div>
