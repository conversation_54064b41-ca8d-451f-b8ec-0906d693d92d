<div class="voting-container">
  <div class="voting-header">
    <div class="user-info">
      <img [src]="userImage" alt="User Profile" class="profile-image">
      <p class="user-name dark-color bold-700 mx-2 mb-0">
       {{"DASHBOARD.WELCOME" | translate}}
    <span class="mx-2">  {{fullName}}
    </span>
    </p>
    </div>
    <div  *ngIf="!isAdministrator">
    <hr>
    <p class="mb-0 text-center bold-700 font-size-xs dark-blue">
      {{"DASHBOARD.THE_VOTE" | translate}}
      </p>

 <div>

    <ng-select
    class="fund-select"
      id="fundSelect"
      [(ngModel)]="selectedFundId"
      (change)="onFundChanged($event)"
      [disabled]="isLoading ">
      <ng-option *ngFor="let fund of defaultFunds" [value]="fund.fundId">
        {{fund.fundName}}
      </ng-option>
    </ng-select>
  </div>



   </div>
  </div>


  <div class="voting-content" *ngIf="!isAdministrator">
    <!-- Vote Count Display -->
    <p class="bold-700 font-size-sm light-dark mb-0">
      <span >
        {{"DASHBOARD.HAVE" | translate}} {{remainingVotes}} {{"DASHBOARD.ITEMS_TO_VOTE" | translate}}
      </span>
    </p>
    <button class="btn primary-btn  m-auto vote-button" (click)="goToResolution()" [disabled]="remainingVotes === 0 || isLoading">
      {{"DASHBOARD.VOTE" | translate}}
    </button>
  </div>
</div>
