# 🏛️ Jadwa Investment Web Application

> **Modern Angular-based Investment Fund Management System**

[![Angular](https://img.shields.io/badge/Angular-18+-red.svg)](https://angular.io/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.4+-blue.svg)](https://www.typescriptlang.org/)
[![Bootstrap](https://img.shields.io/badge/Bootstrap-5.x-purple.svg)](https://getbootstrap.com/)
[![License](https://img.shields.io/badge/License-Proprietary-yellow.svg)]()

## 🌟 Overview

The Jadwa Investment Web Application is a comprehensive, modern Angular-based investment fund management system designed for scalability, security, and performance. Built with Angular 18+, TypeScript, and a robust architecture supporting Arabic (RTL) and English (LTR) localization.

### ✨ Key Features

- 🏗️ **Modular Architecture**: Clean, scalable component-based design
- 🔐 **Secure Authentication**: JWT-based authentication with role-based access control
- 🌍 **Multi-language Support**: Arabic (RTL) and English (LTR) with ngx-translate
- 📱 **Responsive Design**: Mobile-first approach with Bootstrap 5
- 🗳️ **Voting System**: Complete voting management with real-time updates
- 📊 **Assessment Module**: Comprehensive assessment creation and management
- 📁 **File Management**: MinIO integration for secure file handling
- 🔔 **Notification System**: Real-time notifications with Firebase integration

## 🚀 Recent Updates (Latest Release)

### 🆕 New Features
- **Assessment Module**: Complete assessment functionality with creation, distribution, and tracking
- **Enhanced Voting System**: Improved voting interface with comments and results
- **MinIO Integration**: Advanced file management and storage capabilities
- **User Management**: Enhanced user administration and role management

### 🐛 Bug Fixes
- Fixed 50+ JIRA tasks including critical UI and functionality issues
- Improved responsive design and mobile experience
- Enhanced localization and accessibility features
- Resolved authentication and authorization issues

## 🛠️ Technology Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **Framework** | Angular | 18+ | Core application framework |
| **Language** | TypeScript | 5.4+ | Type-safe development |
| **Styling** | SCSS + Bootstrap | 5.x | Responsive design |
| **State Management** | RxJS | Latest | Reactive programming |
| **Internationalization** | ngx-translate | Latest | Multi-language support |
| **Authentication** | JWT | - | Secure authentication |
| **File Storage** | MinIO | Latest | File management system |
| **Notifications** | Firebase | Latest | Real-time notifications |

## 🔧 SSL Support

This application supports SSL/HTTPS for secure connections:

1. **Certificate Setup**: Place SSL certificates in the `ssl/` directory:
   - Main certificate file as `cert.crt`
   - Private key file as `cert.key`

2. **Development**: Generate self-signed certificates using OpenSSL (see `ssl/README.md`)

3. **Port Configuration**:
   - Production: Port 443
   - Development/Staging: Port 4443

For detailed SSL setup instructions, see `ssl/README.md`.

## 🚀 Quick Start

### 📋 Prerequisites

- **Node.js**: 18+ (LTS recommended)
- **Angular CLI**: 18+
- **Git**: Latest version
- **Docker**: Latest (for containerized deployment)

### 🔧 Installation

1. **Clone the repository**:
   ```bash
   git clone https://gitlab.com/amiragad/jadwa.web.git
   cd jadwa.web
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Environment setup**:
   ```bash
   # Copy environment template
   cp src/environments/environment.example.ts src/environments/environment.ts
   # Configure your environment variables
   ```

4. **Start development server**:
   ```bash
   npm run start
   # Or with SSL
   npm run serve:ssl
   ```

### 🌍 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run start` | Start development server |
| `npm run build:prod` | Build for production |
| `npm run build:test` | Build for test environment |
| `npm run test` | Run unit tests |
| `npm run test:e2e` | Run E2E tests with Playwright |
| `npm run test:e2e:ui` | Run E2E tests with UI mode |
| `npm run lint` | Lint code |
| `npm run nswag` | Generate API clients |

## 🏗️ Architecture

### 📁 Project Structure
```
src/
├── app/
│   ├── core/                 # Core services and guards
│   ├── shared/               # Shared components and utilities
│   ├── features/             # Feature modules
│   │   ├── assessment/       # Assessment module
│   │   ├── voting/           # Voting system
│   │   ├── fund-management/  # Fund management
│   │   └── user-management/  # User administration
│   └── layouts/              # Application layouts
├── assets/                   # Static assets
├── environments/             # Environment configurations
└── styles/                   # Global styles
```

### 🔧 Key Modules

- **Assessment Module**: Complete assessment lifecycle management
- **Voting System**: Democratic decision-making tools
- **Fund Management**: Investment fund administration
- **User Management**: User and role administration
- **Document Management**: File handling with MinIO
- **Notification System**: Real-time user notifications

## 🌐 Environments

| Environment | Purpose | API Endpoint | Features |
|-------------|---------|--------------|----------|
| **Local** | Development | `localhost:7010` | Hot reload, debugging |
| **Test** | QA Testing | `test-api.jadwa.com` | Staging data |
| **Production** | Live System | `api.jadwa.com` | Optimized builds |

## 🧪 Testing

### Unit Testing
```bash
# Run unit tests
npm run test

# Run tests with coverage
npm run test:coverage
```

### E2E Testing
```bash
# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run E2E tests in headed mode
npm run test:e2e:headed
```

### Testing Guidelines
- Write unit tests for all new components and services
- Ensure E2E tests cover critical user journeys
- Test both Arabic and English interfaces
- Validate responsive design on multiple devices

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t jadwa-web .

# Run with Docker Compose
docker-compose up -d
```

### Environment-Specific Builds
```bash
# Production build
npm run build:prod

# Test environment build
npm run build:test
```

### CI/CD Pipeline
The project uses GitLab CI/CD for automated testing and deployment:
- Automated testing on every commit
- Deployment to test environment on merge to Test branch
- Production deployment on merge to main branch

## 📚 Documentation

- **[Architecture Overview](./docs/architecture.md)**: Complete system architecture
- **[API Documentation](./docs/api/)**: API endpoints and integration guides
- **[Component Guide](./docs/components/)**: Frontend component documentation
- **[Testing Guide](./docs/testing/)**: Testing strategies and guidelines
- **[Deployment Guide](./docs/deployment/)**: Deployment instructions and configurations

## 🤝 Contributing

### Development Workflow
1. Create feature branch from `Development`
2. Implement changes with tests
3. Update documentation
4. Create merge request
5. Code review and approval
6. Merge to `Development`

### Code Standards
- Follow Angular style guide
- Use TypeScript strict mode
- Write comprehensive tests
- Update documentation
- Follow semantic commit messages

## 📞 Support

### Development Team
- **Frontend Team**: Angular, TypeScript, UI/UX
- **Backend Team**: API integration and services
- **QA Team**: Testing and quality assurance
- **DevOps Team**: Deployment and infrastructure

### Resources
- **Issue Tracker**: GitLab Issues
- **Documentation**: `/docs` directory
- **API Documentation**: Swagger/OpenAPI
- **Architecture Diagrams**: Available in docs

## 🗺️ Roadmap

### Upcoming Features
- Enhanced reporting and analytics
- Mobile application development
- Advanced notification system
- Integration with external systems

### Technical Improvements
- Performance optimizations
- Enhanced security measures
- Improved accessibility
- Advanced testing automation

## 📄 License

This project is proprietary software owned by Jadwa Investment. All rights reserved.

## 🏆 Acknowledgments

Special thanks to all team members who contributed to this project:
- Development Team
- QA Team
- UI/UX Designers
- DevOps Engineers
- Product Management

---

**Project Status**: ✅ Active Development
**Latest Version**: Development Branch
**Last Updated**: 2025-01-07

For more information, please refer to the [project documentation](./docs/) or contact the development team.
