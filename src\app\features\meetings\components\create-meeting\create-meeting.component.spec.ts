import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule } from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';

import { CreateMeetingComponent } from './create-meeting.component';
import { MeetingServiceProxy } from '@core/api/api.generated';

describe('CreateMeetingComponent', () => {
  let component: CreateMeetingComponent;
  let fixture: ComponentFixture<CreateMeetingComponent>;
  let mockMeetingServiceProxy: jasmine.SpyObj<MeetingServiceProxy>;

  beforeEach(async () => {
    const meetingServiceSpy = jasmine.createSpyObj('MeetingServiceProxy', ['addMeeting']);

    await TestBed.configureTestingModule({
      imports: [
        CreateMeetingComponent,
        RouterTestingModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        MatDialogModule,
        ReactiveFormsModule
      ],
      providers: [
        { provide: MeetingServiceProxy, useValue: meetingServiceSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CreateMeetingComponent);
    component = fixture.componentInstance;
    mockMeetingServiceProxy = TestBed.inject(MeetingServiceProxy) as jasmine.SpyObj<MeetingServiceProxy>;

    // Mock the API response
    const mockResponse = {
      statusCode: 200,
      successed: true,
      message: 'Success',
      data: 'meeting-id-123',
      errors: []
    } as any;
    mockMeetingServiceProxy.addMeeting.and.returnValue(of(mockResponse));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.formGroup).toBeDefined();
    expect(component.formGroup.get('locationType')?.value).toBe(1); // MeetingRoom
  });

  it('should validate required fields', () => {
    component.onSubmit();

    expect(component.isValidationFire).toBe(true);
    expect(component.hasError).toBe(true);
    expect(component.errorMessage).toBe('MEETING.VALIDATION_ERROR');
  });

  it('should call API when form is valid', () => {
    // Set up valid form data
    component.fundId = 1;
    component.agendaItems = [{ subject: 'Test Agenda Item' } as any];
    component.formGroup.patchValue({
      subject: 'Test Meeting',
      meetingTypeId: 1,
      meetingDate: new Date(Date.now() + 86400000), // Tomorrow
      startTime: '09:00',
      endTime: '10:00',
      locationType: 1,
      meetingRoomDetails: 'Conference Room A'
    });

    component.onSubmit();

    expect(mockMeetingServiceProxy.addMeeting).toHaveBeenCalled();
  });

  it('should validate future date requirement', () => {
    component.fundId = 1;
    component.agendaItems = [{ subject: 'Test Agenda Item' } as any];
    component.formGroup.patchValue({
      subject: 'Test Meeting',
      meetingTypeId: 1,
      meetingDate: new Date(Date.now() - 86400000), // Yesterday
      startTime: '09:00',
      endTime: '10:00',
      locationType: 1,
      meetingRoomDetails: 'Conference Room A'
    });

    component.onSubmit();

    expect(component.hasError).toBe(true);
    expect(component.errorMessage).toBe('MSG-MEET-CREATE-002');
  });

  it('should validate end time after start time', () => {
    component.fundId = 1;
    component.agendaItems = [{ subject: 'Test Agenda Item' } as any];
    component.formGroup.patchValue({
      subject: 'Test Meeting',
      meetingTypeId: 1,
      meetingDate: new Date(Date.now() + 86400000), // Tomorrow
      startTime: '10:00',
      endTime: '09:00', // End before start
      locationType: 1,
      meetingRoomDetails: 'Conference Room A'
    });

    component.onSubmit();

    expect(component.hasError).toBe(true);
    expect(component.errorMessage).toBe('MSG-MEET-CREATE-003');
  });

  it('should require at least one agenda item', () => {
    component.fundId = 1;
    component.agendaItems = []; // No agenda items
    component.formGroup.patchValue({
      subject: 'Test Meeting',
      meetingTypeId: 1,
      meetingDate: new Date(Date.now() + 86400000), // Tomorrow
      startTime: '09:00',
      endTime: '10:00',
      locationType: 1,
      meetingRoomDetails: 'Conference Room A'
    });

    component.onSubmit();

    expect(component.hasError).toBe(true);
    expect(component.errorMessage).toBe('MSG-MEET-CREATE-005');
  });
});
