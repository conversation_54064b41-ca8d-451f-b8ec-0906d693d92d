// Vote Result Gauge Chart Styles
// Advanced gauge chart with breakdown visualization

.vote-result-gauge-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  // Loading State
  .chart-loading {
    .spinner-border {
      width: 2rem;
      height: 2rem;
    }
  }

  // Empty State
  .chart-empty {
    i {
      opacity: 0.5;
    }

    h6, p {
      margin: 0;
    }
  }

  // Chart Container
  .chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    // Chart Header
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 8px;
      margin-bottom: 16px;

      .chart-title {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
      }

      .chart-total {
        display: flex;
        align-items: center;
        gap: 8px;

        .total-label {
          font-size: 0.85rem;
          color: #6c757d;
        }

        .total-value {
          font-size: 1.1rem;
          font-weight: 700;
          color: #2c3e50;
          background: #f8f9fa;
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
    }

    // Gauge Wrapper
    .gauge-wrapper {
      display: flex;
      flex-direction: column;
      gap: 20px;

      // Primary Gauge
      .primary-gauge {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 250px;

        // NGX Charts Overrides
        ::ng-deep {
          .ngx-charts {
            .advanced-pie-chart {
              .pie-series {
                .pie-arc {
                  stroke: #ffffff;
                  stroke-width: 3px;
                  transition: all 0.3s ease;

                  &:hover {
                    opacity: 0.8;
                    transform: scale(1.02);
                  }
                }
              }
            }

            .pie-label {
              font-size: 0.8rem;
              font-weight: 600;
              fill: #ffffff;
              text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
          }

          .tooltip-anchor {
            .ngx-tooltip {
              .tooltip-content {
                background: rgba(0, 0, 0, 0.9);
                color: white;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 0.85rem;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
              }
            }
          }
        }

        // Center Text Overlay
        .gauge-center-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          pointer-events: none;
          z-index: 10;

          .center-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #28a745;
            line-height: 1;
            margin-bottom: 4px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          .center-label {
            font-size: 0.8rem;
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }

      // Vote Breakdown
      .vote-breakdown {
        .breakdown-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 12px;

          .breakdown-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            border-left: 4px solid transparent;
            transition: all 0.2s ease;

            &:hover {
              background: #e9ecef;
              transform: translateX(2px);
            }

            &.approved {
              border-left-color: #28a745;
            }

            &.rejected {
              border-left-color: #dc3545;
            }

            &.abstained {
              border-left-color: #6c757d;
            }

            &.in-progress {
              border-left-color: #ffc107;
            }

            .item-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;

              .item-color {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                flex-shrink: 0;
              }

              .item-label {
                font-size: 0.85rem;
                font-weight: 500;
                color: #2c3e50;
              }
            }

            .item-content {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .item-percentage {
                font-size: 1.2rem;
                font-weight: 700;
                color: #2c3e50;
              }

              .item-count {
                font-size: 0.8rem;
                color: #6c757d;
              }
            }

            .item-progress {
              .progress {
                height: 4px;
                background-color: #e9ecef;
                border-radius: 2px;
                overflow: hidden;

                .progress-bar {
                  transition: width 0.6s ease;
                  border-radius: 2px;
                }
              }
            }
          }
        }
      }
    }

    // Chart Summary
    .chart-summary {
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid #e9ecef;

      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 16px;

        .summary-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          background: #f8f9fa;
          border-radius: 8px;
          transition: all 0.2s ease;

          &:hover {
            background: #e9ecef;
            transform: translateY(-1px);
          }

          .summary-icon {
            font-size: 1.5rem;
            flex-shrink: 0;
          }

          .summary-content {
            flex: 1;
            min-width: 0;

            .summary-value {
              font-size: 1.1rem;
              font-weight: 700;
              color: #2c3e50;
              line-height: 1;
            }

            .summary-label {
              font-size: 0.75rem;
              color: #6c757d;
              margin-top: 2px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .vote-result-gauge-chart {
    .chart-container {
      .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .chart-total {
          align-self: flex-end;
        }
      }

      .gauge-wrapper {
        .primary-gauge {
          min-height: 200px;

          .gauge-center-text {
            .center-value {
              font-size: 2rem;
            }

            .center-label {
              font-size: 0.7rem;
            }
          }
        }

        .vote-breakdown {
          .breakdown-grid {
            gap: 8px;

            .breakdown-item {
              padding: 10px;

              .item-content {
                .item-percentage {
                  font-size: 1rem;
                }
              }
            }
          }
        }
      }

      .chart-summary {
        .summary-grid {
          grid-template-columns: 1fr;
          gap: 8px;

          .summary-item {
            padding: 10px;

            .summary-content {
              .summary-value {
                font-size: 1rem;
              }

              .summary-label {
                font-size: 0.7rem;
              }
            }
          }
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .vote-result-gauge-chart {
    .chart-container {
      .chart-header {
        .chart-total {
          flex-direction: row-reverse;
        }
      }

      .gauge-wrapper {
        .vote-breakdown {
          .breakdown-grid {
            .breakdown-item {
              border-left: none;
              border-right: 4px solid transparent;

              &:hover {
                transform: translateX(-2px);
              }

              &.approved {
                border-right-color: #28a745;
              }

              &.rejected {
                border-right-color: #dc3545;
              }

              &.abstained {
                border-right-color: #6c757d;
              }

              &.in-progress {
                border-right-color: #ffc107;
              }

              .item-header {
                flex-direction: row-reverse;
              }
            }
          }
        }
      }

      .chart-summary {
        .summary-grid {
          .summary-item {
            flex-direction: row-reverse;
          }
        }
      }
    }
  }
}

// Print Styles
@media print {
  .vote-result-gauge-chart {
    .chart-container {
      .gauge-wrapper {
        .primary-gauge {
          ::ng-deep {
            .ngx-charts {
              .advanced-pie-chart {
                .pie-series {
                  .pie-arc {
                    stroke-width: 1px;
                  }
                }
              }
            }
          }
        }
      }

      .chart-summary {
        page-break-inside: avoid;
      }
    }
  }
}
