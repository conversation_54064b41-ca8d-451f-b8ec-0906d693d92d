@import "../../../../../assets/scss/variables";

.create-assessment-page {
  padding: 20px;
  min-height: 100vh;

  .breadcrumb-section {
    margin-bottom: 20px;
  }

  .page-header-section {
    margin-bottom: 30px;
  }

  .main-content {
    .create-form-container {
      background: #F8FAFC;
      border-radius: 16px;
      border: 0.5px solid #DCE0E3;
      padding: 16px;

      .form-section {
        margin-bottom: 40px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 20px;
          margin-top: 20px;
          padding-bottom: 10px;

          .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;

            // RTL support
            html[dir="rtl"] & {
              flex-direction: row-reverse;
            }
          }

          h6 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #00205a;
          }

          .items-num{
            display: inline-flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 14px;
            background: rgba(38, 86, 135, 0.12);
            color: #000;
            font-size: 16px;
            font-weight: 400;
            line-height: 18px;
          }
        }

        // Form builder header styling
        .header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 10px;
        }

      }
    }
  }

  // Assessment Questions Styles - Following resolution items pattern
  .items-container {
    margin-bottom: 20px;

    .assessment-question-card {
      padding: 16px 10px;
      border-radius: 8px;
      background-color: white;
      margin-bottom: 16px;

      .item-header {
        .item-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .item-number {
            font-size: 20px;
            font-weight: 500;
            color: #00205a;

            .question-type-badge {
              display: inline-block;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: 600;
              letter-spacing: 0.5px;
              background: #e3f2fd;
              color: #1976d2;
              margin-inline-start: 10px;
            }

          }

          .item-actions {
            display: flex;
            gap: 8px;

            .btn {
              padding: 8px;
              &:hover{
                background: unset;
                border: unset;
                color: unset;
              }
            }
          }
        }
      }

      .item-body {
        .item-description {
          font-size: 16px;
          color: $light-dark;
          margin-bottom: 8px;
          line-height: 1.4;

          &:lang(ar){
            &::after {
              content: "؟";
            }
          }
          &:lang(en){
            &::after {
              content: "?";
            }
          }
        }

        .question-type-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .question-options {
            color: $light-dark;
            width: 100%;
            .option-number {
              background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
              color: #374151;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: 600;
              border: 2px solid #E5E7EB;
            }

            .options-header{
              margin-bottom: 16px;
              .options-title {
                font-size: 14px;
                font-weight: 600;
                color: $navy-blue;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 8px;
  
                .options-icon {
                  color: #6B7280;
                  font-size: 13px;
                }

                .stats-item {
                  display: flex;
                  align-items: center;
                  gap: 6px;
                  padding: 6px 12px;
                  background: #F3F4F6;
                  border-radius: 16px;
                  font-size: 12px;
                  color: #6B7280;
                }
              }
            }

            .options-list {
              .option-item {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                padding: 12px 0;
                border-bottom: 1px solid #F3F4F6;
                transition: all 0.2s ease;

                &:last-child {
                  border-bottom: none;
                }

                .option-indicator {
                  .option-number {
                    background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
                    color: #374151;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    font-weight: 600;
                    flex-shrink: 0;
                    border: 2px solid #E5E7EB;
                  }
                }

                .option-content {
                  flex: 1;

                  .option-text {
                    font-size: 14px;
                    color: #374151;
                    line-height: 1.5;
                    font-weight: 400;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

.add-item-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  // border: 2px solid #007bff;
  background: white;
  color: $navy-blue;
  border-radius: 8px;
  font-weight: 400;
  transition: all 0.2s ease;

  // &:hover {
  //   background: #007bff;
  //   color: white;
  //   transform: translateY(-1px);
  //   box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  // }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

  .assessment-questions{
    max-height: 500px;
    overflow-y: auto;
  }

  // Form Actions
  .form-actions {
        margin-top: 40px;
        padding-top: 30px;
        border-top: 2px solid #e0e0e0;

        .actions-container {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 15px;
          flex-wrap: wrap;

          // .btn {
          //   padding: 12px 24px;
          //   font-weight: 500;
          //   border-radius: 8px;
          //   display: flex;
          //   align-items: center;
          //   gap: 8px;
          //   min-width: 120px;
          //   justify-content: center;

          //   &.cancel-btn {
          //     background: #6c757d;
          //     border-color: #6c757d;
          //     color: white;

          //     &:hover {
          //       background: #5a6268;
          //       border-color: #545b62;
          //     }
          //   }

          //   &.save-draft-btn {
          //     border-color: #007bff;
          //     color: #007bff;

          //     &:hover {
          //       background: #007bff;
          //       color: white;
          //     }
          //   }

          //   &.submit-btn {
          //     background: #007bff;
          //     border-color: #007bff;

          //     &:hover {
          //       background: #0056b3;
          //       border-color: #004085;
          //     }
          //   }

          //   &:disabled {
          //     opacity: 0.6;
          //     cursor: not-allowed;
          //   }
          // }
        }
      }
}

// Responsive Design
@media (max-width: 768px) {
  .create-assessment-page {
    padding: 15px;

    .main-content {
      .create-form-container {
        padding: 20px;

        .form-actions {
          .actions-container {
            flex-direction: column;

            app-custom-button {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
