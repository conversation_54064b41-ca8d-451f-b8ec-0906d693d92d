<div class="document-list-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>{{ 'DOCUMENTS.LOADING' | translate }}</p>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && documents.length === 0" class="empty-state flex-column">
    <img src="assets/images/nodata.png" width="350">
    <!-- <h3 class="mt-3">{{ 'DOCUMENTS.NO_DOCUMENTS' | translate }}</h3> -->
    <p  class="text-center mt-3 header fs-20">{{ 'DOCUMENTS.NO_DOCUMENTS_MESSAGE' | translate }}</p>
  </div>

  <!-- Document Table -->
  <div *ngIf="!isLoading && documents.length > 0" class="documents-table">
    <app-table
      [columns]="tableColumns"
      [displayedColumns]="displayedColumns"
      [dataSource]="tableDataSource"
      [totalItems]="totalCount"
      [pageSize]="pageSize"
      (onClickAction)="onTableAction($event)"
      [paginationType]="paginationType"
      (textLinkClick)="onTextLinkClick($event)"
      (pageChange)="onPageChange($event)">
    </app-table>
  </div>
</div>
