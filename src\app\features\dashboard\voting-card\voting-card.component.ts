import { Component, Input, OnInit, OnDestroy, OnChanges, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { TokenService } from '../../auth/services/token.service';
import { GetUserAcceptedVotesServiceProxy, DefaultFundInfoDto } from '@core/api/api.generated';
import { Subject, takeUntil } from 'rxjs';
import { Router } from '@angular/router';
import { NgSelectModule } from "@ng-select/ng-select";
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-voting-card',
  standalone: true,
  imports: [CommonModule, TranslateModule,FormsModule, NgSelectModule],
  templateUrl: './voting-card.component.html',
  styleUrl: './voting-card.component.scss'
})
export class VotingCardComponent implements OnInit, OnDestroy,OnChanges {
  fullName: any;
  userImage: any;
  @Input() isAdministrator: boolean = false;
  @Input() defaultFunds: DefaultFundInfoDto[] = [];

  selectedFundId: number | null = null;
  remainingVotes: number = 0;
  isLoading: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(
    private TokenService: TokenService,
    private getUserAcceptedVotesService: GetUserAcceptedVotesServiceProxy,
        private router: Router
  ) {
    this.fullName = this.TokenService.getFullName();
    this.userImage = this.getPersonalPhotoUrl();
  }
  ngOnChanges(){
      // Set default selected fund if available
      if (this.defaultFunds && this.defaultFunds.length > 0) {
        this.selectedFundId = this.defaultFunds[0].fundId || null;
        this.loadVoteCount();
      }
    }

 getPersonalPhotoUrl(): string {
    let fullUrl: string = "";
    const photoUrl = this.TokenService.getPersonalPhotoUrl();
    //  if (photoUrl?.startsWith('http')) {
      fullUrl = photoUrl || 'assets/images/avatar-user.png';
  //  }
    return fullUrl ;
  }

ngOnInit() {
    // Set default selected fund if available
    if (this.defaultFunds && this.defaultFunds.length > 0) {
      this.selectedFundId = this.defaultFunds[0].fundId || null;
      this.loadVoteCount();
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onFundChanged(event: any): void {
      this.selectedFundId = event;
      this.loadVoteCount();
  }

  private loadVoteCount(): void {
    if (!this.selectedFundId) {
      this.remainingVotes = 0;
      return;
    }

    this.isLoading = true;
    this.getUserAcceptedVotesService.userAcceptedVotes(this.selectedFundId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.remainingVotes = response.data;
          } else {
            this.remainingVotes = 0;
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.remainingVotes = 0;
          console.error('Error loading vote count:', error);
        }
      });
  }
  goToResolution() {
    localStorage.setItem('fundName', this.defaultFunds.find(f => f.fundId === Number(this.selectedFundId))?.fundName || '');

    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.selectedFundId }
    });
  }

  navigateToProfile(){
   this.router.navigate(['/admin/user-management/my-profile']);
  }
}
