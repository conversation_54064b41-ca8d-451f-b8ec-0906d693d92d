import { CommonModule } from '@angular/common';
import { Component, ViewChild, viewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { SizeEnum } from '@shared/enum/size-enum';
import { MeetingsListComponent } from './components/meetings-list/meetings-list.component';
import { ProposalListComponent } from './components/Proposal-List/proposal-list.component';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-meetings',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatCardModule,
    TranslateModule,
    PageHeaderComponent,
    BreadcrumbComponent,
    MeetingsListComponent,
    ProposalListComponent
  ],
  templateUrl: './meetings.component.html',
  styleUrl: './meetings.component.scss'
})
export class MeetingsComponent {
  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [ 
  ];
  breadcrumbSizeEnum = SizeEnum;
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;
  isLoading: any;
  documentCategories:any = [];
  selectedTabIndex: any;
  totalCount: any;
  fundId: number=0;
 
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private translate: TranslateService,
    private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe((queryParams) => {
      this.selectedTabIndex = +queryParams['selectedTab'] || 0;
      this.fundId = +queryParams['fundId'] || 0;
    });
    this.updateBreadcrumb();
    this.documentCategories = [
      {name:  'INVESTMENT_FUNDS.MEETING.Voting_TITLE', component: 'proposal'},
      {name: 'INVESTMENT_FUNDS.MEETING.Meeting_TITLE', component: 'meetings'},
    ]
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }
  
  onTabChange(arg0: number) {
    throw new Error('Method not implemented.');
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  handleData($event: any) {
    console.log("totalcount",$event)
    this.totalCount = $event
  }
  private updateBreadcrumb(): void {
    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      { label: 'INVESTMENT_FUNDS.MEETING.MEETINGSTITLE', url: '', disabled: true },
   ];
   this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
   }
}
