import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { TranslateModule } from '@ngx-translate/core';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";

export interface CancelMeetingDialogData {
  meetingSubject: string;
  title: string;
  message: string;
}

@Component({
  selector: 'app-cancel-meeting-dialog',
  standalone: true,
  imports: [CommonModule, TranslateModule, CustomButtonComponent],
  templateUrl: './cancel-meeting-dialog.component.html',
  styleUrl: './cancel-meeting-dialog.component.scss'
})
export class CancelMeetingDialogComponent {
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  constructor(
    public dialogRef: MatDialogRef<CancelMeetingDialogComponent>,
    private cdr: ChangeDetectorRef,
    @Inject(MAT_DIALOG_DATA) public data: CancelMeetingDialogData,
  ) {}

  onConfirm(): void {
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
    this.cdr.detectChanges();
  }
}
