import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule } from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { DateTime } from 'luxon';

import { EditMeetingComponent } from './edit-meeting.component';
import { MeetingServiceProxy, MeetingSessionResponseDto } from '@core/api/api.generated';

describe('EditMeetingComponent', () => {
  let component: EditMeetingComponent;
  let fixture: ComponentFixture<EditMeetingComponent>;
  let mockMeetingServiceProxy: jasmine.SpyObj<MeetingServiceProxy>;

  beforeEach(async () => {
    const meetingServiceSpy = jasmine.createSpyObj('MeetingServiceProxy', ['meetingSession', 'editMeeting']);

    await TestBed.configureTestingModule({
      imports: [
        EditMeetingComponent,
        RouterTestingModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        MatDialogModule,
        ReactiveFormsModule
      ],
      providers: [
        { provide: MeetingServiceProxy, useValue: meetingServiceSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(EditMeetingComponent);
    component = fixture.componentInstance;
    mockMeetingServiceProxy = TestBed.inject(MeetingServiceProxy) as jasmine.SpyObj<MeetingServiceProxy>;

    // Mock the API responses
    const mockMeetingData: SingleMeetingResponse = {
      id: 1,
      fundId: 1,
      subject: 'Test Meeting',
      description: 'Test Description',
      meetingTypeId: 1,
      meetingDate: DateTime.now(),
      startTime: '09:00',
      endTime: '10:00',
      locationType: MeetingLocationType._1,
      meetingRoomDetails: 'Conference Room A',
      onlineMeetingLink: null,
      meetingStatusId: 2,
      fundName: 'Test Fund',
      meetingTypeName: 'Board Meeting',
      meetingStatusName: 'Scheduled',
      agendaItems: [],
      attendees: [],
      attachmentIds: []
    } as any;

    const mockGetResponse = {
      statusCode: 200,
      successed: true,
      message: 'Success',
      data: mockMeetingData,
      errors: []
    } as any;

    const mockUpdateResponse = {
      statusCode: 200,
      successed: true,
      message: 'Success',
      data: 'meeting-updated',
      errors: []
    } as any;

    mockMeetingServiceProxy.meetingGetById.and.returnValue(of(mockGetResponse));
    mockMeetingServiceProxy.editMeeting.and.returnValue(of(mockUpdateResponse));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.formGroup).toBeDefined();
    expect(component.formGroup.get('locationType')?.value).toBe(1); // MeetingRoom
  });

  it('should load meeting data on init', () => {
    component.meetingId = 1;
    component.loadMeetingData();

    expect(mockMeetingServiceProxy.meetingGetById).toHaveBeenCalledWith(1);
  });

  it('should validate required fields', () => {
    component.onSubmit();

    expect(component.isValidationFire).toBe(true);
    expect(component.hasError).toBe(true);
    expect(component.errorMessage).toBe('MEETING.VALIDATION_ERROR');
  });

  it('should call API when form is valid', () => {
    // Set up valid form data
    component.meetingId = 1;
    component.fundId = 1;
    component.formGroup.patchValue({
      subject: 'Updated Meeting',
      meetingTypeId: 1,
      meetingDate: new Date(),
      startTime: '09:00',
      endTime: '10:00',
      locationType: 1,
      meetingRoomDetails: 'Conference Room A'
    });

    component.onSubmit();

    expect(mockMeetingServiceProxy.editMeeting).toHaveBeenCalled();
  });
});
