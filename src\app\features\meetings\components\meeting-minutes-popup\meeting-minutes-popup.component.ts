import { Component, Inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { 
  MeetingServiceProxy, 
  MeetingMinutesDtoBaseResponse,
  MeetingMinutesDto
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';

export interface MeetingMinutesDialogData {
  meetingId: number;
  meetingSubject: string;
}

@Component({
  selector: 'app-meeting-minutes-popup',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    TranslateModule
  ],
  templateUrl: './meeting-minutes-popup.component.html',
  styleUrls: ['./meeting-minutes-popup.component.scss']
})
export class MeetingMinutesPopupComponent implements OnDestroy {
  private destroy$ = new Subject<void>();
  
  // UI State
  isLoading = false;
  hasError = false;
  errorMessage = '';
  
  // Meeting minutes data
  meetingMinutes: MeetingMinutesDto | null = null;

  constructor(
    public dialogRef: MatDialogRef<MeetingMinutesPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MeetingMinutesDialogData,
    private meetingServiceProxy: MeetingServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService
  ) {
    this.loadMeetingMinutes();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load meeting minutes from API
   */
  loadMeetingMinutes(): void {
    if (!this.data.meetingId) {
      this.hasError = true;
      this.errorMessage = 'INVESTMENT_FUNDS.MEETING.INVALID_MEETING_ID';
      return;
    }

    this.isLoading = true;
    this.hasError = false;
    this.errorMessage = '';

    this.meetingServiceProxy.meetingMinutesGet(this.data.meetingId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: MeetingMinutesDtoBaseResponse) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.meetingMinutes = response.data;
          } else {
            this.hasError = true;
            this.errorMessage = response.message || 'INVESTMENT_FUNDS.MEETING.MINUTES_LOAD_ERROR';
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.hasError = true;
          this.errorMessage = 'INVESTMENT_FUNDS.MEETING.MINUTES_NETWORK_ERROR';
          console.error('Error loading meeting minutes:', error);
        }
      });
  }

  /**
   * Retry loading meeting minutes
   */
  retryLoadMeetingMinutes(): void {
    this.loadMeetingMinutes();
  }

  /**
   * Close the dialog
   */
  onClose(): void {
    this.dialogRef.close();
  }
 
}
