# 🏗️ Dashboard Component Architecture

## 📋 Overview

This document outlines the component architecture, design patterns, and implementation details of the Dashboard feature in the Jadwa Investment Web Application.

## 🎯 Architecture Principles

### 🔧 Design Patterns
- **Standalone Components**: Angular 18+ standalone component architecture
- **Reactive Programming**: RxJS observables for data flow management
- **Service Layer**: Separation of concerns with dedicated services
- **Dependency Injection**: Angular DI for service management
- **Component Composition**: Reusable components with clear interfaces

### 📦 Module Structure
```
DashboardFeature (Standalone)
├── DashboardComponent (Main)
├── BoxInfoCardComponent (Reusable)
├── VotingCardComponent (Specific)
├── DashboardService (Data)
├── DashboardUtilsService (Utilities)
└── Dashboard Models (Types)
```

## 🧩 Component Hierarchy

### 🎛️ Main Dashboard Component
**File**: `dashboard.component.ts`

**Responsibilities**:
- Orchestrate dashboard data loading
- Manage component state (loading, error, data)
- Handle user interactions (refresh, navigation)
- Coordinate child components

**Key Properties**:
```typescript
export class DashboardComponent implements OnInit, OnDestroy {
  // State Management
  loading = false;
  errorMessage = '';
  
  // Data Properties
  dashboardData: DashboardViewModel | null = null;
  kpiCards: KPICardConfig[] = [];
  recentNotifications: NotificationViewModel[] = [];
  recentActivities: ActivityViewModel[] = [];
  
  // Configuration
  breadcrumbs: IBreadcrumbItem[] = [];
  
  // Cleanup
  private destroy$ = new Subject<void>();
}
```

**Lifecycle Methods**:
- `ngOnInit()`: Initialize component and load dashboard data
- `ngOnDestroy()`: Clean up subscriptions and resources

**Public Methods**:
- `onRefresh()`: Manually refresh dashboard data
- `onBreadcrumbClick()`: Handle breadcrumb navigation
- `trackByKPITitle()`: TrackBy function for KPI cards
- `trackByActivityId()`: TrackBy function for activities
- `trackByNotificationId()`: TrackBy function for notifications

### 📊 Box Info Card Component
**File**: `box-info-card.component.ts`

**Purpose**: Reusable KPI display component with consistent styling and behavior.

**Input Properties**:
```typescript
@Input() title: string = '';                    // Translation key for title
@Input() value: number | string = 0;            // Main metric value
@Input() icon: string = 'fas fa-info-circle';   // FontAwesome icon class
@Input() color: CardColor = 'primary';          // Color theme
@Input() subtitle: string = '';                 // Optional subtitle
@Input() trend: TrendType = 'neutral';          // Trend indicator
@Input() trendValue: string = '';               // Trend value display
```

**Computed Properties**:
```typescript
get cardColorClass(): string {
  return `card-${this.color}`;
}

get iconColorClass(): string {
  return `text-${this.color}`;
}

get trendIcon(): string {
  switch (this.trend) {
    case 'up': return 'fas fa-arrow-up text-success';
    case 'down': return 'fas fa-arrow-down text-danger';
    default: return 'fas fa-minus text-muted';
  }
}
```

**Features**:
- Responsive design with mobile optimization
- Accessibility attributes (ARIA labels, keyboard navigation)
- Hover and focus states
- RTL/LTR support
- Color theming system

### 🗳️ Voting Card Component
**File**: `voting-card.component.ts`

**Purpose**: Display voting-related information for board members.

**Features**:
- Shows pending votes count
- User profile integration
- Vote status indicators
- Quick access to voting interface

## 🔄 Data Flow Architecture

### 📊 Data Flow Diagram
```mermaid
graph TD
    A[Dashboard Component] --> B[Dashboard Service]
    B --> C[Generated API Client]
    C --> D[Backend API]
    
    B --> E[Dashboard Utils Service]
    E --> F[Enhanced View Models]
    F --> A
    
    A --> G[Box Info Card Components]
    A --> H[Voting Card Component]
    A --> I[Shared Components]
    
    J[User Interactions] --> A
    A --> K[Navigation/Refresh]
```

### 🔄 Service Layer Pattern

#### 📡 Dashboard Service
**Responsibilities**:
- API communication and data fetching
- Error handling and user feedback
- Request caching and optimization
- Date/time conversion utilities

**Key Methods**:
```typescript
class DashboardService {
  getAutoDashboard(fromDate?, toDate?, limit?): Observable<BaseDashboardResponseBaseResponse>
  getFundManagerDashboard(...params): Observable<FundManagerDashboardResponseBaseResponse>
  getDashboardKPIs(...params): Observable<DashboardKPIsDtoBaseResponse>
  getRecentNotifications(...params): Observable<NotificationSummaryDtoListBaseResponse>
  getRecentActivities(...params): Observable<ActivitySummaryDtoListBaseResponse>
}
```

#### 🛠️ Dashboard Utils Service
**Responsibilities**:
- Data transformation and enhancement
- UI-specific data processing
- Formatting and display utilities
- Business logic calculations

**Key Methods**:
```typescript
class DashboardUtilsService {
  convertKPIsToCards(kpis: DashboardKPIsDto): KPICardConfig[]
  enhanceActivities(activities: ActivitySummaryDto[]): ActivityViewModel[]
  enhanceNotifications(notifications: NotificationSummaryDto[]): NotificationViewModel[]
  createDashboardViewModel(data: BaseDashboardResponse): DashboardViewModel
  formatNumber(value: number): string
  getDashboardGreeting(): string
}
```

## 🎨 Styling Architecture

### 🎯 SCSS Structure
```scss
.dashboard-page {
  // Main container styles
  
  .welcome-section {
    // Welcome card styles
  }
  
  .kpis-section {
    // KPI cards grid layout
  }
  
  .activity-item, .notification-item {
    // List item styles
  }
  
  // Responsive breakpoints
  @media (max-width: 768px) { }
  
  // RTL support
  html[dir="rtl"] & { }
  
  // Dark theme support
  :root.dark-theme & { }
}
```

### 🎨 Design System Integration
- **Bootstrap Grid**: Responsive layout system
- **Angular Material**: UI components and theming
- **FontAwesome**: Icon system
- **Custom Variables**: Brand colors and spacing

## 🔧 State Management

### 📊 Component State Pattern
```typescript
interface DashboardState {
  // Loading States
  isLoading: boolean;
  isRefreshing: boolean;
  
  // Error States
  error?: string;
  
  // Data States
  dashboardData?: DashboardViewModel;
  kpiCards: KPICardConfig[];
  activities: ActivityViewModel[];
  notifications: NotificationViewModel[];
  
  // UI States
  lastRefresh?: Date;
  filters: DashboardFilters;
}
```

### 🔄 Reactive State Updates
```typescript
// Observable-based state management
private loadDashboardData(): void {
  this.loading = true;
  this.errorMessage = '';
  
  this.dashboardService.getAutoDashboard()
    .pipe(
      takeUntil(this.destroy$),
      finalize(() => this.loading = false)
    )
    .subscribe({
      next: (response) => this.handleSuccess(response),
      error: (error) => this.handleError(error)
    });
}
```

## 🧪 Testing Architecture

### 🔬 Testing Strategy
- **Unit Tests**: Individual component and service testing
- **Integration Tests**: Component interaction testing
- **Accessibility Tests**: ARIA and keyboard navigation
- **Visual Tests**: Responsive design and styling

### 🎭 Mock Strategy
```typescript
// Service mocking
const mockDashboardService = jasmine.createSpyObj('DashboardService', [
  'getAutoDashboard'
]);

// Data mocking
const mockDashboardData: BaseDashboardResponse = {
  userName: 'Test User',
  userRole: 'FundManager',
  // ... other properties
};
```

### 📊 Test Coverage Areas
- Component initialization and lifecycle
- Data loading and error handling
- User interactions and navigation
- Accessibility compliance
- Responsive behavior

## ♿ Accessibility Architecture

### 🎯 WCAG 2.1 AA Compliance
- **Semantic HTML**: Proper heading hierarchy and landmarks
- **ARIA Attributes**: Labels, roles, and descriptions
- **Keyboard Navigation**: Tab order and focus management
- **Screen Reader Support**: Meaningful content structure
- **Color Contrast**: Accessible color combinations

### 🔍 Accessibility Implementation
```typescript
// Component template with accessibility
<div class="dashboard-page" role="main" aria-label="Dashboard">
  <div class="card" role="region" aria-labelledby="welcome-heading">
    <h4 id="welcome-heading">{{ 'DASHBOARD.WELCOME' | translate }}</h4>
  </div>
  
  <ul class="list-unstyled" role="list" aria-label="Recent Activities">
    <li role="listitem" *ngFor="let activity of activities">
      <time [attr.datetime]="activity.occurredAt">
        {{ activity.displayTime }}
      </time>
    </li>
  </ul>
</div>
```

## 🌍 Internationalization Architecture

### 🔤 Translation Integration
```typescript
// Component with i18n
export class DashboardComponent {
  breadcrumbs: IBreadcrumbItem[] = [
    {
      label: 'sidebar.dashboard',  // Translation key
      url: '/admin/dashboard',
      disabled: true
    }
  ];
}
```

### 🌐 RTL Support Implementation
```scss
// RTL-aware styling
.dashboard-page {
  .activity-icon {
    margin-right: 1rem;
    
    html[dir="rtl"] & {
      margin-right: 0;
      margin-left: 1rem;
    }
  }
}
```

## 🚀 Performance Architecture

### ⚡ Optimization Strategies
- **OnPush Change Detection**: Reduced change detection cycles
- **TrackBy Functions**: Efficient list rendering
- **Lazy Loading**: Route-based code splitting
- **API Caching**: ShareReplay for repeated requests
- **Bundle Optimization**: Tree shaking and dead code elimination

### 📊 Performance Monitoring
```typescript
// Performance tracking
private trackPerformance(operation: string, startTime: number): void {
  const duration = performance.now() - startTime;
  console.log(`Dashboard ${operation} took ${duration}ms`);
}
```

## 🔮 Future Architecture Enhancements

### 📋 Planned Improvements
- **Micro-Frontend Architecture**: Modular dashboard widgets
- **State Management Library**: NgRx or Akita integration
- **Component Library**: Shared UI component system
- **Progressive Web App**: Offline capabilities
- **Real-Time Updates**: WebSocket integration

### 🎯 Scalability Considerations
- **Widget System**: Pluggable dashboard components
- **Configuration Management**: Dynamic dashboard layouts
- **Multi-Tenancy**: Role-based component visibility
- **Performance Monitoring**: Real-time performance metrics
