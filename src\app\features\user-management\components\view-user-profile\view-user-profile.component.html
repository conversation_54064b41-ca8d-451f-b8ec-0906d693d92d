<!-- Main Content -->
<div class="view-user-profile-page">
  <!-- Breadcrumb -->
  <!-- <app-breadcrumb [breadcrumbs]="breadcrumbItems" [size]="breadcrumbSizeEnum.Medium" divider=">"></app-breadcrumb> -->

  <div class="d-flex align-items-center justify-content-between mt-4">
    <!-- Page Header -->
    <app-page-header [title]="'USER_PROFILE.VIEW_TITLE' | translate">
    </app-page-header>

    <div class="actions d-flex align-items-center gap-3">

      <ng-container *ngIf="canShowAdminActions()">


        <!-- Reset Password Button -->
        <app-custom-button *ngIf="canResetPassword()" [buttonType]="ButtonTypeEnum.OutLine"
          [btnName]="'USER_MANAGEMENT.ACTIONS.RESET_PASSWORD' | translate"
          [disabled]="isResettingPassword"
          [loading]="isResettingPassword"
          (click)="onResetPassword()">
        </app-custom-button>

        <!-- Resend Message Button -->
        <app-custom-button *ngIf="canResendMessage()" [buttonType]="ButtonTypeEnum.Secondary"
          [btnName]="'USER_MANAGEMENT.ACTIONS.RESEND_MESSAGE' | translate"
          [disabled]="isResendingMessage"
          [loading]="isResendingMessage"
          (click)="onResendMessage()">
        </app-custom-button>

        <!-- Activate/Deactivate Toggle Button -->
        <app-custom-button
          [buttonType]="currentUserData?.data?.isActive ? ButtonTypeEnum.Danger : ButtonTypeEnum.Primary"
          [btnName]="(currentUserData?.data?.isActive ? 'USER_MANAGEMENT.ACTIONS.DEACTIVATE' : 'USER_MANAGEMENT.ACTIONS.ACTIVATE') | translate"
          [disabled]="isLoading"
          [loading]="isLoading"
          (click)="onToggleUserStatus()">
        </app-custom-button>
      </ng-container>
      <!-- Show Update Profile button only if viewing current user's profile -->
      <app-custom-button *ngIf="isCurrentUser()" [buttonType]="ButtonTypeEnum.Primary"
        [btnName]="'USER_PROFILE.UPDATE_PROFILE' | translate" (click)="onUpdateProfile()">
      </app-custom-button>
      <!-- Edit User Button -->
      <app-custom-button *ngIf="canShowAdminActions()" [buttonType]="ButtonTypeEnum.Primary"
        [btnName]="'USER_MANAGEMENT.ACTIONS.EDIT' | translate" (click)="onEditUser()">
      </app-custom-button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading && !currentUserData" class="loading-container text-center">
    <div class="spinner-border text-primary text-grey" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="mt-2">{{ 'USER_PROFILE.LOADING_PROFILE' | translate }}</p>
  </div>

  <!-- Profile Container -->
  <div *ngIf="!isLoading || currentUserData" class="profile-container mt-4">
    <!-- Profile Photo Section -->
    <div class="d-flex justify-content-between align-items-center mb-5 border-bottom-gradient pb-5 ">
      <div class="d-flex align-items-center gap-4 justify-content-center">
        <div class="profile-photo-section text-center">
          <div class="photo-container">
            <div class="profile-photo-circle">
              <img [src]="getUserPhotoUrl()" [alt]="'USER_PROFILE.PERSONAL_PHOTO' | translate" class="profile-photo"
                (error)="onImageError($event)">
            </div>
          </div>
        </div>
        <div class="d-flex flex-column">
          <div class="d-flex align-items-baseline justify-content-center gap-2">
            <div class="text-primary text-grey fs-5 fw-bold mb-3">{{ currentUserData?.data?.fullName || '-' }}</div>
            <div class="field-value mt-3">
              <span class="status-badge" [class]="currentUserData?.data?.isActive ? 'status-green' : 'status-orange'">
                <span class="dot"></span>
                {{ (currentUserData?.data?.isActive ? 'USER_MANAGEMENT.STATUS.ACTIVE' :
                'USER_MANAGEMENT.STATUS.INACTIVE') |
                translate }}
              </span>
            </div>
          </div>
          <div class="d-flex gap-2 flex-wrap">
            <span *ngFor="let role of currentUserData?.data?.roles" class="status-badge status-info role-badge">
              {{ role?.name }}
            </span>
            <!-- <div class="field-value">
              {{ (currentUserData?.data?.roles && currentUserData?.data?.roles.length > 0) ? currentUserData?.data?.roles.join(', ') : '-' }}
            </div> -->
          </div>
          <div>
            <a *ngIf="isCurrentUser()" (click)="onToggleChangePassword()"
              class="change-pass-btn d-flex text-primary text-grey mt-1">{{'USER_PROFILE.CHANGE_PASSWORD' |
              translate}}</a>
          </div>

        </div>
      </div>

    </div>
    <!-- <div *ngIf="showChangePassword" class="mt-4 mb-4">
        <app-change-password
          [userId]="currentUserData?.data?.id"
          (passwordChanged)="onPasswordChanged()"
          (cancelled)="onChangePasswordCancelled()">
        </app-change-password>
      </div> -->

    <!-- User Details -->
    <div class="user-details-section">
      <div class="row">
        <!-- Basic Information Section -->
        <!-- <div class="col-md-6 mb-3">
          <label class="form-label">{{ 'USER_PROFILE.NAME' | translate }}</label>
          <div class="field-value">{{ currentUserData?.data?.fullName || '-' }}</div>
        </div> -->
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.EMAIL' | translate }}</label>
          <div class="field-value">{{ currentUserData?.data?.email || '-' }}</div>
        </div>

        <!-- Contact Information -->
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.MOBILE' | translate }}</label>
          <div class="field-value">{{ currentUserData?.data?.userName || '-' }}</div>
        </div>
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.COUNTRY_CODE' | translate }}</label>
          <div class="field-value">+966</div>
        </div>
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_MANAGEMENT.COLUMNS.LAST_UPDATE_DATE' | translate }}</label>
          <div class="field-value">{{ getLastUpdateDate() }}</div>
      <div class="time-container d-flex align-items-center">
        <ng-container *ngIf="currentLang === 'ar'; else enTime">
          <p class="period">{{ getLastUpdateTime().period }}</p>
          <p class="mx-1">{{ getLastUpdateTime().time }}</p>
        </ng-container>

        <ng-template #enTime>
          <p class="mx-1">{{ getLastUpdateTime().time }}</p>
          <p class="period">{{ getLastUpdateTime().period }}</p>
        </ng-template>
      </div>

        </div>

        <!-- Additional Information -->
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.IBAN' | translate }}</label>
          <div class="field-value">{{ currentUserData?.data?.iban || '-' }}</div>
        </div>
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.NATIONALITY' | translate }}</label>
          <div class="field-value">{{ currentUserData?.data?.nationality || '-' }}</div>
        </div>

        <!-- Document Information -->
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.PASSPORT_NO' | translate }}</label>
          <div class="field-value">{{ currentUserData?.data?.passportNo || '-' }}</div>
        </div>
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.CV' | translate }}</label>
          <div class="field-value">
            <!-- Show CV download if file exists -->
            <div *ngIf="currentUserData?.data?.cvFile" class="attachment-card mb-2">
              <div class="download-icon" (click)="downloadFile()">
                <img src="assets/icons/download.png" alt="download" />
              </div>
              <div class="file-info" (click)="downloadFile()">
                <span class="file-name">
                  {{ currentUserData?.data?.cvFile?.fileName || 'CV' }}
                </span>
              </div>
            </div>
            <!-- Show message when no CV is available -->
            <span *ngIf="!currentUserData?.data?.cvFile">-</span>
          </div>
        </div>

        <!-- Registration Status -->
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.REGISTRATION_STATUS' | translate }}</label>
          <div class="field-value">
            <span class="status-badge"
              [class]="currentUserData?.data?.registrationIsCompleted ? 'status-green' : 'status-orange'">
              <span class="dot"></span>
              {{ getRegistrationStatus() | translate }}
            </span>
          </div>
        </div>

        <!-- Registration Message Status (for admin view only) -->
        <div class="col-md-3 mb-3">
          <label class="form-label  text-grey">{{ 'USER_PROFILE.REGISTRATION_MESSAGE' | translate }}</label>
          <div class="field-value">
            <span class="status-badge"
              [class]="currentUserData?.data?.registrationMessageIsSent ? 'status-green' : 'status-orange'">
              <span class="dot"></span>
              {{ (currentUserData?.data?.registrationMessageIsSent ? 'USER_MANAGEMENT.MESSAGE.SENT' :
              'USER_MANAGEMENT.MESSAGE.NOT_SENT') | translate }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons for Super Admin -->
    <div class="action-buttons-section mt-5" *ngIf="canShowAdminActions()">
      <div class="row">
        <div class="col-12">
          <div class="d-flex gap-3 flex-wrap justify-content-end">

            <app-custom-button [buttonType]="ButtonTypeEnum.Secondary" [btnName]="'COMMON.BACK' | translate"
              (click)="onBack()">
            </app-custom-button>

          </div>
        </div>
      </div>
    </div>

  </div>

</div>
