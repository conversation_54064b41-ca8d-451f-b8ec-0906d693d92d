import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { TranslateModule } from '@ngx-translate/core';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";
@Component({
  selector: 'app-edit-resolution-popup',
  standalone: true,
  imports: [CommonModule,
    TranslateModule, CustomButtonComponent],
  templateUrl: './edit-resolution-popup.component.html',
  styleUrl: './edit-resolution-popup.component.scss'
})
export class EditResolutionPopupComponent {
 buttonEnum = ButtonTypeEnum;
    IconEnum = IconEnum;
  constructor(
    public dialogRef: MatDialogRef<EditResolutionPopupComponent>,
    public router:Router,
      private cdr: ChangeDetectorRef,
          @Inject(MAT_DIALOG_DATA) public data: any,

  ) {

  }
  onSubmit(){
    this.dialogRef.close(true);
    //  this.router.navigate(['/admin/investment-funds/resolutions'], {
    //     queryParams: { fundId: this.data },
    //   });
  }

  onClose(): void {
    this.dialogRef.close(false);
      this.cdr.detectChanges();
  }
}

