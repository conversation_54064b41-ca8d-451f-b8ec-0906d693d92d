// Dashboard Analytics Card Styles
// Pixel-perfect implementation matching the design attachments

.dashboard-analytics-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  // Card Header with Main Metric
  .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 20px;
    text-align: center;
    position: relative;

    .main-metric {
      .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        line-height: 1;
        margin-bottom: 8px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }

      .metric-title {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }

  // Card Body with Sub Metrics
  .card-body {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .sub-metrics-grid {
      display: grid;
      gap: 12px;
      flex: 1;

      // Different layouts based on number of items
      &:has(.sub-metric-item:nth-child(2):last-child) {
        grid-template-columns: 1fr 1fr;
      }

      &:has(.sub-metric-item:nth-child(3)) {
        grid-template-columns: 1fr;
      }
    }

    .sub-metric-item {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 12px;
      border-left: 4px solid transparent;
      transition: all 0.2s ease;

      &:hover {
        background: #e9ecef;
        transform: translateX(2px);
      }

      // Color variants
      &--primary {
        border-left-color: #0d6efd;
        .metric-icon { color: #0d6efd; }
      }

      &--success {
        border-left-color: #198754;
        .metric-icon { color: #198754; }
      }

      &--warning {
        border-left-color: #ffc107;
        .metric-icon { color: #ffc107; }
      }

      &--danger {
        border-left-color: #dc3545;
        .metric-icon { color: #dc3545; }
      }

      &--secondary {
        border-left-color: #6c757d;
        .metric-icon { color: #6c757d; }
      }

      &--info {
        border-left-color: #0dcaf0;
        .metric-icon { color: #0dcaf0; }
      }

      .metric-content {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .metric-icon {
          font-size: 1.2rem;
          margin-right: 10px;
          width: 24px;
          text-align: center;
        }

        .metric-details {
          flex: 1;

          .metric-value {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1;
            margin-bottom: 2px;
          }

          .metric-title {
            font-size: 0.75rem;
            color: #6c757d;
            font-weight: 500;
          }
        }
      }

      .metric-progress {
        .progress {
          height: 4px;
          background-color: #e9ecef;
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: 4px;

          .progress-bar {
            transition: width 0.6s ease;
            border-radius: 2px;
          }
        }

        .percentage-text {
          font-size: 0.7rem;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }
  }

  // Card Footer with Chart
  .card-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 16px;

    .chart-container {
      text-align: center;

      .chart-placeholder {
        padding: 20px;
        color: #6c757d;

        i {
          font-size: 2rem;
          margin-bottom: 8px;
          display: block;
        }

        small {
          font-size: 0.8rem;
        }
      }
    }
  }
}

// Specific card type styles
.dashboard-analytics-card--fund {
  .card-header {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    
    .main-metric .metric-value {
      color: #1976d2;
    }
  }
}

.dashboard-analytics-card--resolution-status {
  .card-header {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    
    .main-metric .metric-value {
      color: #f57c00;
    }
  }
}

.dashboard-analytics-card--resolution-votes {
  .card-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    
    .main-metric .metric-value {
      color: #388e3c;
    }
  }
}

.dashboard-analytics-card--assessment {
  .card-header {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    
    .main-metric .metric-value {
      color: #7b1fa2;
    }
  }
}

.dashboard-analytics-card--document {
  .card-header {
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    
    .main-metric .metric-value {
      color: #c2185b;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-analytics-card {
    .card-header {
      padding: 16px;

      .main-metric .metric-value {
        font-size: 2rem;
      }
    }

    .card-body {
      padding: 12px;

      .sub-metrics-grid {
        grid-template-columns: 1fr !important;
        gap: 8px;
      }

      .sub-metric-item {
        padding: 10px;

        .metric-content .metric-details .metric-value {
          font-size: 1.2rem;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .dashboard-analytics-card {
    .sub-metric-item {
      border-left: none;
      border-right: 4px solid transparent;

      &:hover {
        transform: translateX(-2px);
      }

      &--primary { border-right-color: #0d6efd; }
      &--success { border-right-color: #198754; }
      &--warning { border-right-color: #ffc107; }
      &--danger { border-right-color: #dc3545; }
      &--secondary { border-right-color: #6c757d; }
      &--info { border-right-color: #0dcaf0; }

      .metric-content .metric-icon {
        margin-right: 0;
        margin-left: 10px;
      }
    }
  }
}
