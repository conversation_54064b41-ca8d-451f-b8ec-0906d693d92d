import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { of, throwError } from 'rxjs';

import { ChangePasswordComponent } from './change-password.component';
import { AuthenticationServiceProxy } from '@core/api/api.generated';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import { ErrorModalService } from '@core/services/error-modal.service';
import { AuthService } from '../../services/auth-service/auth.service';
import { TokenService } from '../../services/token.service';
import { UserManagementService } from '@shared/services/users/user-management.service';

describe('ChangePasswordComponent', () => {
  let component: ChangePasswordComponent;
  let fixture: ComponentFixture<ChangePasswordComponent>;
  let mockUserManagementService: jasmine.SpyObj<UserManagementService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockErrorModalService: jasmine.SpyObj<ErrorModalService>;
  let mockTranslateService: jasmine.SpyObj<TranslateService>;

  beforeEach(async () => {
    const userManagementServiceSpy = jasmine.createSpyObj('UserManagementService', ['setNewPasswordForUser']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['logout']);
    const errorModalServiceSpy = jasmine.createSpyObj('ErrorModalService', ['showSuccess']);
    const translateServiceSpy = jasmine.createSpyObj('TranslateService', ['instant']);
    const languageServiceSpy = jasmine.createSpyObj('LanguageService', ['switchLang'], {
      currentLang$: of('en'),
      currentLanguageEvent: of('en')
    });

    await TestBed.configureTestingModule({
      imports: [
        ChangePasswordComponent,
        ReactiveFormsModule,
        TranslateModule.forRoot()
      ],
      providers: [
        FormBuilder,
        { provide: UserManagementService, useValue: userManagementServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: ErrorModalService, useValue: errorModalServiceSpy },
        { provide: TranslateService, useValue: translateServiceSpy },
        { provide: LanguageService, useValue: languageServiceSpy },
        { provide: AuthenticationServiceProxy, useValue: {} },
        { provide: TokenService, useValue: {} }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ChangePasswordComponent);
    component = fixture.componentInstance;

    mockUserManagementService = TestBed.inject(UserManagementService) as jasmine.SpyObj<UserManagementService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockErrorModalService = TestBed.inject(ErrorModalService) as jasmine.SpyObj<ErrorModalService>;
    mockTranslateService = TestBed.inject(TranslateService) as jasmine.SpyObj<TranslateService>;

    mockTranslateService.instant.and.returnValue('Translated text');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Double Submission Prevention', () => {
    beforeEach(() => {
      // Set up valid form data
      component.formGroup.patchValue({
        newPassword: 'ValidPassword123!',
        confirmPassword: 'ValidPassword123!'
      });

      // Mock localStorage
      spyOn(localStorage, 'getItem').and.returnValue('123');
    });

    it('should prevent double submission when already loading', () => {
      // Arrange
      component.isLoading = true;

      // Act
      component.onSubmit();
      component.onSubmit(); // Try to submit again

      // Assert
      expect(mockUserManagementService.setNewPasswordForUser).not.toHaveBeenCalled();
    });

    it('should set loading state during API call', () => {
      // Arrange
      mockUserManagementService.setNewPasswordForUser.and.returnValue(of({}));

      // Act
      component.onSubmit();

      // Assert
      expect(component.isLoading).toBe(true);
      expect(mockUserManagementService.setNewPasswordForUser).toHaveBeenCalledTimes(1);
    });

    it('should reset loading state on successful API response', () => {
      // Arrange
      mockUserManagementService.setNewPasswordForUser.and.returnValue(of({}));

      // Act
      component.onSubmit();

      // Assert
      expect(component.isLoading).toBe(false);
      expect(mockErrorModalService.showSuccess).toHaveBeenCalled();
      expect(mockAuthService.logout).toHaveBeenCalled();
    });

    it('should reset loading state on API error', () => {
      // Arrange
      mockUserManagementService.setNewPasswordForUser.and.returnValue(throwError(() => 'API Error'));
      spyOn(console, 'error');

      // Act
      component.onSubmit();

      // Assert
      expect(component.isLoading).toBe(false);
      expect(console.error).toHaveBeenCalledWith('Error changing password', 'API Error');
    });

    it('should not call API if form is invalid', () => {
      // Arrange
      component.formGroup.patchValue({
        newPassword: '',
        confirmPassword: ''
      });

      // Act
      component.onSubmit();

      // Assert
      expect(component.isLoading).toBe(false);
      expect(mockUserManagementService.setNewPasswordForUser).not.toHaveBeenCalled();
    });

    it('should not call API if passwords do not match', () => {
      // Arrange
      component.formGroup.patchValue({
        newPassword: 'Password123!',
        confirmPassword: 'DifferentPassword123!'
      });

      // Act
      component.onSubmit();

      // Assert
      expect(component.isLoading).toBe(false);
      expect(mockUserManagementService.setNewPasswordForUser).not.toHaveBeenCalled();
      expect(component.formGroup.get('confirmPassword')?.errors).toEqual({ passwordMismatch: true });
    });
  });
});
