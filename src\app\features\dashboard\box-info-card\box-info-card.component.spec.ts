import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';
import { By } from '@angular/platform-browser';

import { BoxInfoCardComponent } from './box-info-card.component';

describe('BoxInfoCardComponent', () => {
  let component: BoxInfoCardComponent;
  let fixture: ComponentFixture<BoxInfoCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        BoxInfoCardComponent,
        TranslateModule.forRoot()
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BoxInfoCardComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.title).toBe('');
    expect(component.value).toBe(0);
    expect(component.icon).toBe('fas fa-info-circle');
    expect(component.color).toBe('primary');
    expect(component.subtitle).toBe('');
    expect(component.trend).toBe('neutral');
    expect(component.trendValue).toBe('');
  });

  it('should display title and value', () => {
    component.title = 'Test Title';
    component.value = 42;
    fixture.detectChanges();

    const titleElement = fixture.debugElement.query(By.css('.card-title'));
    const valueElement = fixture.debugElement.query(By.css('.card-value'));

    expect(titleElement.nativeElement.textContent.trim()).toBe('Test Title');
    expect(valueElement.nativeElement.textContent.trim()).toBe('42');
  });

  it('should display icon with correct classes', () => {
    component.icon = 'fas fa-test';
    component.color = 'success';
    fixture.detectChanges();

    const iconElement = fixture.debugElement.query(By.css('.icon-section i'));

    expect(iconElement.nativeElement.classList).toContain('fas');
    expect(iconElement.nativeElement.classList).toContain('fa-test');
    expect(iconElement.nativeElement.classList).toContain('text-success');
  });

  it('should display subtitle when provided', () => {
    component.subtitle = 'Test Subtitle';
    fixture.detectChanges();

    const subtitleElement = fixture.debugElement.query(By.css('small'));
    expect(subtitleElement.nativeElement.textContent.trim()).toBe('Test Subtitle');
  });

  it('should not display subtitle when empty', () => {
    component.subtitle = '';
    fixture.detectChanges();

    const subtitleElement = fixture.debugElement.query(By.css('small'));
    expect(subtitleElement).toBeFalsy();
  });

  it('should display trend section when trend is not neutral and trendValue is provided', () => {
    component.trend = 'up';
    component.trendValue = '+5%';
    fixture.detectChanges();

    const trendSection = fixture.debugElement.query(By.css('.trend-section'));
    expect(trendSection).toBeTruthy();
  });

  it('should not display trend section when trend is neutral', () => {
    component.trend = 'neutral';
    component.trendValue = '+5%';
    fixture.detectChanges();

    const trendSection = fixture.debugElement.query(By.css('.trend-section'));
    expect(trendSection).toBeFalsy();
  });

  it('should not display trend section when trendValue is empty', () => {
    component.trend = 'up';
    component.trendValue = '';
    fixture.detectChanges();

    const trendSection = fixture.debugElement.query(By.css('.trend-section'));
    expect(trendSection).toBeFalsy();
  });

  describe('computed properties', () => {
    it('should return correct card color class', () => {
      component.color = 'warning';
      expect(component.cardColorClass).toBe('card-warning');
    });

    it('should return correct icon color class', () => {
      component.color = 'danger';
      expect(component.iconColorClass).toBe('text-danger');
    });

    it('should return correct trend icon for up trend', () => {
      component.trend = 'up';
      expect(component.trendIcon).toBe('fas fa-arrow-up text-success');
    });

    it('should return correct trend icon for down trend', () => {
      component.trend = 'down';
      expect(component.trendIcon).toBe('fas fa-arrow-down text-danger');
    });

    it('should return correct trend icon for neutral trend', () => {
      component.trend = 'neutral';
      expect(component.trendIcon).toBe('fas fa-minus text-muted');
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA attributes', () => {
      component.title = 'Test KPI';
      component.value = 100;
      fixture.detectChanges();

      const cardElement = fixture.debugElement.query(By.css('.box-info-card'));

      expect(cardElement.nativeElement.getAttribute('role')).toBe('region');
      expect(cardElement.nativeElement.getAttribute('aria-label')).toBe('Test KPI: 100');
      expect(cardElement.nativeElement.getAttribute('tabindex')).toBe('0');
    });

    it('should have proper icon section ARIA attributes', () => {
      component.title = 'Test KPI';
      fixture.detectChanges();

      const iconSection = fixture.debugElement.query(By.css('.icon-section'));

      expect(iconSection.nativeElement.getAttribute('role')).toBe('img');
      expect(iconSection.nativeElement.getAttribute('aria-label')).toBe('Test KPI');
    });

    it('should have proper trend section ARIA attributes', () => {
      component.trend = 'up';
      component.trendValue = '+10%';
      fixture.detectChanges();

      const trendSection = fixture.debugElement.query(By.css('.trend-section'));

      expect(trendSection.nativeElement.getAttribute('role')).toBe('img');
      expect(trendSection.nativeElement.getAttribute('aria-label')).toBe('Trend: up +10%');
    });
  });

  describe('styling', () => {
    it('should apply correct color variant class', () => {
      component.color = 'info';
      fixture.detectChanges();

      const cardElement = fixture.debugElement.query(By.css('.box-info-card'));
      expect(cardElement.nativeElement.classList).toContain('card-info');
    });

    it('should be focusable', () => {
      fixture.detectChanges();

      const cardElement = fixture.debugElement.query(By.css('.box-info-card'));
      expect(cardElement.nativeElement.tabIndex).toBe(0);
    });
  });
});
