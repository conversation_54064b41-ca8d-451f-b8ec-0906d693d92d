@import "../../../../../assets/scss/variables";

.breadcrumb-container {
  margin-bottom: 1rem;
}

.header-container {
  display: flex;

  .rotate-icon{
    cursor: pointer;
  }

  .title-container {
    .title {
      color: $navy-blue;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      // margin-bottom: 17px;
    }
    .sub-title {
      color: #4f4f4f;

      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 33px;
      span {
        color: $navy-blue;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        margin-right: 8px;

        &.referral-code-link {
          cursor: pointer;
          text-decoration: underline;
          transition: color 0.2s ease;

          &:hover {
            color: darken($navy-blue, 10%);
            text-decoration: underline;
          }

          &:active {
            color: darken($navy-blue, 20%);
          }
        }
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;

  .loading-text {
    margin-top: 1rem;
    color: var(--text-secondary);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  .error-icon {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: 1rem;
  }

  .error-message {
    color: var(--error-color);
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
}

.resolution-details-container,
.attachment-section,
.members-section {
  // padding: 1rem;
  // max-width: 1200px;
  // margin: 0 auto;

  background-color: $card-background;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .info-item {
    margin-bottom: 15px;
    .info-label {
      color: $text-grey;
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
    }
    .description-text {
      max-width: 100%; // or set a fixed width like 250px
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }
    .info-value {
      color: $navy-blue;
      font-size: 16px;
      font-weight: 500;
      line-height: 30px;
    }
    .status {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 24px;
      border-radius: 20px;
      padding: 10px;

      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
      width: fit-content;

      &.draft {
        background: rgba(117, 85, 172, 0.1);
        color: #7555ac;
      }
      &.pending {
        background: #fff3cd;
        color: #856404;
      }
      &.completing-data {
        background: rgba(157, 112, 9, 0.27);
        color: #9d7009;
      }
      &.waiting-for-confirmation {
        background: rgba(226, 180, 138, 0.34);
        color: #d16440;
      }
      &.confirmed {
        background: rgba(97, 253, 97, 0.14);

        color: #27ae60;
      }

      &.rejected {
        color: $text-grey;
        background: #eaeef1;
      }

      &.voting-inProgress {
        background: rgba(47, 128, 237, 0.1);
        color: #2f80ed;
      }

      &.approved {
        background: #f1faf1;
        color: #0e700e;
      }

      &.not-approved {
        background: rgba(197, 15, 31, 0.1);

        color: #c50f1f;
      }

      &.cancelled {
        background: var(--Color---Grey-5, #e0e0e0);

        color: #4f4f4f;
      }
    }
  }
}
.attachment-section {
  padding: 18px;
  height: fit-content;

  .title {
    color: $dark-blue;
    font-size: 16px;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.32px;
    .attachment-number {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: $navy-blue;
      font-size: 14px;
      font-weight: 400;
      margin-right: 10px;
      padding: 2px 8px;
    }
  }

  .add-attachment-button{
    background-color: white;
    border: 1px solid $navy-blue;
    border-radius: 6px;
    color: $navy-blue;
    font-weight: 500;
  }
}

.details-header {
  margin-bottom: 2rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .page-title {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 600;
  }

  .header-actions {
    display: flex;
    gap: 0.5rem;

    .expand-button {
      background-color: transparent;
      border: 1px solid transparent;
    }
  }
}

.status-section {
  margin-bottom: 2rem;

  mat-chip {
    font-weight: 500;

    &.status-1 {
      // Draft
      background-color: #e3f2fd;
      color: #1976d2;
    }

    &.status-2 {
      // Pending
      background-color: #fff3e0;
      color: #f57c00;
    }

    &.status-3 {
      // Approved/Confirmed
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    &.status-4 {
      // Rejected/Cancelled
      background-color: #ffebee;
      color: #c62828;
    }

    &.status-5 {
      // Completing data
      background-color: #f3e5f5;
      color: #7b1fa2;
    }

    &.status-6 {
      // Waiting for confirmation
      background-color: #e0f2f1;
      color: #00695c;
    }
  }
}

.card-container {
  &.expanded {
    max-height: 500px;
  }

  .info-card,
  .file-card,
  .attachments-card,
  .items-card,
  .history-card,
  .rejection-card {
    margin-bottom: 2rem;

    mat-card-header {
      margin-bottom: 1rem;

      mat-card-title {
        color: var(--primary-color);
        font-size: 1.25rem;
        font-weight: 600;
      }
    }
  }
}
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    &.full-width {
      grid-column: 1 / -1;
    }

    .info-label {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.9rem;
    }

    .info-value {
      color: var(--text-secondary);
      font-size: 1rem;
      word-break: break-word;
    }
  }
}

.file-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.attachments-list {
  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    .attachment-info {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      .attachment-name {
        font-weight: 500;
        color: var(--text-primary);
      }

      .attachment-size {
        font-size: 0.875rem;
        color: var(--text-secondary);
      }
    }

    .attachment-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
}

.attachment-counter {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: normal;
}

.items-list {
  .item-card {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }

    .item-header {
      margin-bottom: 1rem;

      .item-title {
        margin: 0;
        color: var(--primary-color);
        font-size: 1.1rem;
        font-weight: 600;
      }
    }

    .item-content {
      .item-description {
        margin-bottom: 1rem;
        color: var(--text-secondary);
        line-height: 1.6;
      }

      .conflict-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background-color: #fff3e0;
        border-radius: 6px;
        border-left: 4px solid #ff9800;

        .conflict-icon {
          color: #ff9800;
          font-size: 1.25rem;
        }

        .conflict-text {
          color: #e65100;
          font-weight: 500;
        }

        .view-members-btn {
          margin-left: auto;
          color: var(--primary-color);
        }
      }
    }
  }
}

.history-list {
  .history-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    .history-info {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr;
      gap: 1rem;
      align-items: center;

      .action-name {
        font-weight: 600;
        color: var(--text-primary);
      }

      .user-role {
        color: var(--primary-color);
        font-size: 0.9rem;
      }

      .user-name {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }

      .date-time {
        color: var(--text-secondary);
        font-size: 0.875rem;
      }
    }
  }
}

.rejection-card {
  border-left: 4px solid var(--error-color);

  .rejection-content {
    .rejection-text {
      color: var(--text-secondary);
      line-height: 1.6;
      font-style: italic;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding: 2rem 0;
  border-top: 1px solid var(--border-color);
}

// Responsive design
@media (max-width: 768px) {
  .resolution-details-container {
    padding: 0.5rem;
  }

  .details-header .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .file-actions {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

// RTL support
[dir="rtl"] {
  .details-header .header-content {
    text-align: right;
  }

  .info-item {
    text-align: right;
  }

  .conflict-info {
    border-left: none;
    border-right: 4px solid #ff9800;
  }

  .notes-container {
    .note-item {
      .attendee-note-card {
        .attendee-info {
          .attendee-details {
            .attendee-header {
              .attendee-name,
              .note-timestamp {
                text-align: right;
              }
            }

            .attendee-role {
              text-align: right;
            }

            .note-content {
              text-align: right;
            }
          }
        }

        .note-actions {
          margin-left: 0;
          margin-right: 16px;
        }
      }
    }
  }

  // Replies Section RTL Support
  .replies-section {
    margin-left: 0;
    margin-right: 64px;

    .reply-item {
      .reply-line {
        left: auto;
        right: -32px;

        &::before {
          left: auto;
          right: -32px;
        }
      }

      .attendee-reply-card {
        .attendee-info {
          .attendee-details {
            .attendee-header {
              .attendee-name,
              .note-timestamp {
                text-align: right;
              }
            }

            .attendee-role {
              text-align: right;
            }

            .note-content {
              text-align: right;
            }
          }
        }

        .note-actions {
          margin-left: 0;
          margin-right: 12px;
        }
      }
    }
  }

  // Main Reply Section RTL Support
  .reply-section {
    .main-reply-btn {
      svg {
        transform: scaleX(-1);
      }
    }
  }
}

.resolution-details-container {
  background-color: $card-background;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:last-of-type{
    margin-bottom: 30px;
  }
}

.header {
  margin: 0 0 42px;
  font-size: 24px;
  font-weight: 700;
  line-height: 20px;
}

.resolution-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  .header-actions {
    display: flex;
    gap: 12px;

    button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #333;
      }
      .fa-pencil {
        color: #eaa300;
      }
      .expand {
        color: $navy-blue;
      }
      i {
        font-size: 16px;
      }
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 700;
    margin: 0;
    line-height: 22px;
    display: flex;
    align-items: center;
    span {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: var(--Color---Black-1, #000);

      font-size: 16px;
      font-weight: 400;
      line-height: 18px; /* 112.5% */
      display: flex;
      padding: 8px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      margin-right: 10px;
      margin-left: 10px;
    }
  }
}

hr {
  color: $border-hr-color;
  border: 1px solid;
  margin: 0;
}

.resolution-details-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: fit-content;
  }

  .item-container {
    border-radius: 8px;
    background: #fff;
    padding: 16px;
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    .top-section {
      padding: 0 18px;

      .title {
        color: $navy-blue;
        font-size: 20px;
        font-weight: 500;
        line-height: 32px;
      }
      .conflict-btn {
        color: #b68107;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        border-radius: 8px;
        border: 1px solid #ffc800;
        background: #fffaeb;
        padding: 7px 13px;
      }
    }
    .sub-title {
      color: #333;
      font-size: 14px;
      font-weight: 400;
      line-height: 21px;
      margin-top: 12px;
      padding: 0 18px;
    }
  }
}
.info-item {
  .info-label {
    color: $text-grey;
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
  }
  .info-value {
    color: $navy-blue;
    font-size: 16px;
    font-weight: 500;
    line-height: 30px;
  }
  .status {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 24px;
    border-radius: 20px;
    padding: 10px;

    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
    &.draft {
      background: rgba(117, 85, 172, 0.1);
      color: #7555ac;
    }
    &.pending {
      background: #fff3cd;
      color: #856404;
    }
    &.completing-data {
      background: rgba(157, 112, 9, 0.27);
      color: #9d7009;
    }
    &.waiting-for-confirmation {
      background: rgba(226, 180, 138, 0.34);
      color: #d16440;
    }
    &.confirmed {
      background: rgba(97, 253, 97, 0.14);

      color: #27ae60;
    }

    &.rejected {
      color: $text-grey;
      background: #eaeef1;
    }

    &.voting-inProgress {
      background: rgba(47, 128, 237, 0.1);
      color: #2f80ed;
    }

    &.approved {
      background: #f1faf1;
      color: #0e700e;
    }

    &.not-approved {
      background: rgba(197, 15, 31, 0.1);

      color: #c50f1f;
    }

    &.cancelled {
      background: var(--Color---Grey-5, #e0e0e0);

      color: #4f4f4f;
    }
  }
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 16px 0;

  .detail-item {
    display: flex;
    flex-direction: column;
    label {
      display: block;
      font-size: 14px;
      color: $text-grey;
      font-weight: 700;
      margin-bottom: 8px;
    }

    button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #333;
      }
      .fa-pencil {
        color: #eaa300;
      }
      .expand {
        color: $navy-blue;
      }
      i {
        font-size: 16px;
      }
    }

    .value {
      font-size: 16px;
      color: $navy-blue;
      font-weight: 500;
    }

    .date-value {
      display: flex;
      flex-direction: row;
      gap: 6px;

      .gregorian {
        font-size: 16px;
        color: $navy-blue;
        font-weight: 500;
      }

      .hijri {
        font-size: 12px;
        color: $text-grey;
      }
    }

    .status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      height: 24px;
      border-radius: 20px;
      padding: 10px;

      .circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }

      &.active {
        color: #28a745;

        span {
          color: #28a745;
        }
      }

      span {
        font-size: 12px;
      }
    }
    .status-new span {
      background-color: gray;
    }

    .status-under-construction {
      background-color: #e5eefb;
      color: #2f80ed;

      span {
        background-color: #2f80ed;
      }
    }
    .status-waiting {
      color: #ff5f3d;

      background-color: #fdf1eb;
      span {
        background-color: #ff5f3d;
      }
    }
    .status-active {
      color: #27ae60;

      background-color: #f1faf1;
      span {
        background-color: #27ae60;
      }
    }
    .status-exited {
      color: $text-grey;

      background-color: #e0e0e0;
      span {
        background-color: $text-grey;
      }
    }
    .custom-btn {
      color: $navy-blue;
      border: 1px solid #00205a;
      background-color: #fff;
      border-radius: 8px;

      cursor: default;
      padding: 0 31.426px;
      height: 28px;
      font-weight: 400;
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .details-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .details-grid {
    grid-template-columns: 1fr;
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 400;
  line-height: 16px;

  .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0px;
    margin-inline-end: 6px;
  }

  // &.status-green {
  //   background-color: #f1faf1;
  //   color: #27ae60;

  //   .dot {
  //     background-color: #27ae60;
  //   }
  // }

  // &.status-blue {
  //   background-color: #e5eefb;
  //   color: #2f80ed;

  //   .dot {
  //     background-color: #2f80ed;
  //   }
  // }

  // &.status-orange {
  //   background-color: #fdf1eb;
  //   color: #ff5f3d;

  //   .dot {
  //     background-color: #ff5f3d;
  //   }
  // }

  // &.status-red {
  //   background-color: #FFEBED;
  //   color: #C50F1F;

  //   .dot {
  //     background-color: #C50F1F;
  //   }
  // }

  // &.status-grey {
  //   background-color: #e0e0e0;
  //   color: #828282;

  //   .dot {
  //     background-color: #828282;
  //   }
  // }

  &.draft {
    background: rgba(117, 85, 172, 0.1);
    color: #7555ac;
    .dot {
      background-color: #7555ac;
    }
  }
  &.pending {
    background: #fff3cd;
    color: #856404;
    .dot {
      background-color: #856404;
    }
  }
  &.completing-data {
    background: rgba(157, 112, 9, 0.27);
    color: #9d7009;
    .dot {
      background-color: #9d7009;
    }
  }
  &.waiting-for-confirmation {
    background: rgba(226, 180, 138, 0.34);
    color: #d16440;
    .dot {
      background-color: #d16440;
    }
  }
  &.confirmed {
    background: rgba(97, 253, 97, 0.14);
    color: #27ae60;
    .dot {
      background-color: #27ae60;
    }
  }

  &.rejected {
    color: $text-grey;
    background: #eaeef1;
    .dot {
      background-color: $text-grey
    }
  }

  &.voting-inProgress {
    background: rgba(47, 128, 237, 0.1);
    color: #2f80ed;
    .dot {
      background-color: #2f80ed;
    }
  }

  &.approved {
    background: #f1faf1;
    color: #0e700e;
    .dot {
      background-color: #0e700e;
    }
  }

  &.not-approved {
    background: rgba(197, 15, 31, 0.1);
    color: #c50f1f;
    .dot {
      background-color: #c50f1f;
    }
  }

  &.cancelled {
    background: var(--Color---Grey-5, #e0e0e0);
    color: #4f4f4f;
    .dot {
      background-color: #4f4f4f;
    }
  }
}


.attendees-summary {
  display: flex;
  // flex-wrap: wrap;
  gap: 8px;
  justify-content: space-between;
  margin-top: 16px;

  .summary-card {
    // display: flex;
    // flex-direction: column;
    padding: 12px;
    padding-top: 8px;
    border-radius: 12px;
    width: 33%;
    // flex-grow: 1;
    // pointer-events: none;

    .summary-count {
      font-size: 18px;
      font-weight: 500;
    }

    .summary-label {
      font-size: 12px;
      font-weight: 500;
      line-height: 20px;
      margin-bottom: 5px;
    }

    .view-summary{
      cursor: pointer;
      position: relative;
      top: 4px;
    }
  }
  .approved-summary {
    background: #27AE602E;
    color: #27AE60;
  }

  .rejected-summary {
    background: #C50F1F2E;
    color: #C50F1F;
  }

  .not-voted-summary {
    background: #EAA3002E;
    color: #CC910B;
  }
}
// Responsive design
@media (max-width: 768px) {
  .item-header{
        width: 100%;
    justify-content: space-between;
  }
  .top-section{
    .conflict-btn{
      width: 100%
    }
  }
  // .members-section {
  //   .voting-summary {
  //     gap: 12px;

  //     .summary-card {
  //       flex-direction: row;
  //       justify-content: space-between;
  //       padding: 12px 16px;
  //       text-align: center;

  //       .summary-count {
  //         margin-bottom: 0;
  //       }
  //     }
  //   }
  // }
  .summary-card{
    position: relative;
    .view-summary{
      position: absolute !important;
      top: 0px !important;
      right: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      opacity: 0;
    }
  }
}

// Attendees List Styles
.attendees-list{
  .attendee-card {
    background: $card-background;
    border: 1px solid $border-color;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: box-shadow 0.2s ease;
    margin-bottom: 12px;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .attendee-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .attendee-avatar {
        .avatar-image {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .attendee-details {
        .attendee-name {
          color: $navy-blue;
          font-size: 16px;
          font-weight: 600;
          line-height: 20px;
          margin: 0 0 4px 0;
        }

        .attendee-role {
          color: $text-grey;
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
          margin: 0;
        }
      }
    }

    .attendance-selection {
      .attendance-dropdown {
        min-width: 140px;
        border: 1px solid $border-color;
        border-radius: 8px;
        padding: 10px 14px;
        font-size: 13px;
        font-weight: 500;
        background-color: #f8f9fa;
        color: $text-grey;
        transition: all 0.2s ease;
        cursor: pointer;
        &:lang(ar) {
          text-align: left;
        }

        &:focus {
          outline: none;
          border-color: $navy-blue;
          box-shadow: 0 0 0 2px rgba(0, 32, 90, 0.1);
        }

        // Status-specific styling
        &.attending {
          background-color: #f1faf1;
          color: #27ae60;
          border-color: #27ae60;
        }

        &.away {
          background-color: #ffebed;
          color: #c50f1f;
          border-color: #c50f1f;
        }

        &.unregistered {
          background-color: #fdf1eb;
          color: #ff5f3d;
          border-color: #ff5f3d;
        }

        option {
          background-color: white;
          color: $text-grey;
        }
      }
    }

    // Responsive design for mobile
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .attendee-info {
        width: 100%;
      }

      .attendance-selection {
        width: 100%;

        .attendance-dropdown {
          width: 100%;
          min-width: unset;
        }
      }
    }
  }
}

// Notes Container Styles
.note-item{
  background: $white;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;

  .attendee-card {
      margin-bottom: 12px;
      .attendee-info {
        display: flex;
        align-items: start;
        gap: 12px;
        width: 80%;

        .attendee-avatar {
          .avatar-image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
          }
        }

        .attendee-details {
          .attendee-name {
            color: $navy-blue;
            font-size: 16px;
            font-weight: 600;
            line-height: 20px;
            margin: 0 0 4px 0;
          }

          .attendee-role {
            color: $text-grey;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            margin: 0;
          }
        }
      }

      // Responsive design for mobile
      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .attendee-info {
          width: 100%;
        }
      }
    }

    .note-content{
      margin-inline-start: 50px;
      margin-top: 20px;
    }
    .note-actions{
      margin-inline-start: 50px;
      margin-bottom: 20px;
      .reply-btn{
        background: none;
        border: none;
        color: #2F80ED;
        text-decoration: underline;
        cursor: pointer;

        &:hover {
          color: #0056b3;
        }
      }

      // Reply Form Styles
      .reply-form {
        width: 100%;
        margin-top: 12px;

        .reply-textarea {
          width: 100%;
          border: 1px solid #e5e5e5;
          border-radius: 8px;
          padding: 12px;
          font-size: 14px;
          font-family: inherit;
          resize: vertical;
          min-height: 80px;
          // text-align: right;
          // direction: rtl;

          &:focus {
            outline: none;
          }

          &::placeholder {
            color: #999;
            // text-align: right;
          }
        }

        .reply-form-actions {
          display: flex;
          gap: 8px;
          margin-top: 8px;
          justify-content: flex-end;

          .btn {
            padding: 8px 30px
          }
        }
      }
    }

  .replies-section{
    .reply-item{
      padding-inline-start: 50px;
    }
    .note-header{
      margin-top: 40px;

      .attendee-avatar {
        .avatar-image {
          width: 25px;
          height: 25px;
          border-radius: 50%;
          object-fit: cover;
        }
      }
      .attendee-details {
        .attendee-name {
          font-size: 14px;
        }
        .attendee-role {
          font-size: 10px;
        }
      }
      .note-timestamp{
        font-size: 12px;
      }
    }
    .note-content{
      font-size: 14px;
      margin-inline-start: 35px;
    }
  }

  .line{
    &::before{
      content: '';
      position: absolute;
      top: 50px;
      inset-inline-start: 20px;
      width: 1px;
      height: 85%;
      background-color: #e5e5e5;
    }
  }

  .note-timestamp{
    color: $light-dark;
  }
}

  ::ng-deep{
  .swal2-popup{
    border-radius: 8px;
    padding-inline: 24px;
    .swal2-html-container{
      color: $dark;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      padding: 0px;
      margin-top: 10px;
    }
    .swal2-textarea{
      margin-inline: 0px;
      border-color: $grey;
      height: 100px;
      resize: none;
      overflow-y: auto;
    }
    .swal2-input-label{
      justify-content: flex-start;
      width: 100%;
      border-radius: 10px;
      // font-size: 16px;
      font-weight: 700;
      color: $navy-blue;
    }

    .swal2-confirm{
      background-color: $navy-blue;
      border: 1px solid $navy-blue;
      color: $white;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 700;
    }

    .swal2-cancel{
      background-color: white;
      border: 1px solid $navy-blue;
      color: $navy-blue;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 700;
    }
  }
}




// Countdown Timer Styling
.countdown-timer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: inline-block;
  min-width: 80px;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  // Warning state when time is running low (less than 5 minutes)
  &.warning {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
    animation: pulse 2s infinite;
  }

  // Critical state when time is very low (less than 1 minute)
  &.critical {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    animation: pulse 1s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

::ng-deep {
  .st-editor-container {
    .st-button[title="add image"],
    .st-button[title="create link"],
    .st-button[title="unlink"] {
      display: none;
    }
  }
}
