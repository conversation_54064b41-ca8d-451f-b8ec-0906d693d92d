<div class="create-meeting-page">
  <!-- <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div> -->
  <div class="d-flex justify-content-between">
    <div
      class="header-container w-100 d-flex align-items-center justify-content-between mb-3">
      <div class="d-flex align-items-baseline">
        <span class="rotate-icon mx-2" (click)="goBack()">
          <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22"
            viewBox="0 0 22 22" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M11.0572 0.75H10.9428C8.75212 0.749987 7.03144 0.749976 5.68802 0.930594C4.31137 1.11568 3.21911 1.50272 2.36091 2.36091C1.50271 3.21911 1.11568 4.31137 0.930593 5.68802C0.749975 7.03144 0.749987 8.75212 0.75 10.9428V11.0572C0.749987 13.2479 0.749975 14.9686 0.930593 16.312C1.11568 17.6886 1.50271 18.7809 2.36091 19.6391C3.21911 20.4973 4.31137 20.8843 5.68802 21.0694C7.03144 21.25 8.75212 21.25 10.9428 21.25H11.0572C13.2479 21.25 14.9686 21.25 16.312 21.0694C17.6886 20.8843 18.7809 20.4973 19.6391 19.6391C20.4973 18.7809 20.8843 17.6886 21.0694 16.312C21.25 14.9686 21.25 13.2479 21.25 11.0572V10.9428C21.25 8.75212 21.25 7.03144 21.0694 5.68802C20.8843 4.31137 20.4973 3.21911 19.6391 2.36091C18.7809 1.50272 17.6886 1.11568 16.312 0.930594C14.9686 0.749976 13.2479 0.749987 11.0572 0.75ZM16.1121 2.41722C17.3224 2.57994 18.0454 2.88853 18.5784 3.42157C19.1115 3.95462 19.4201 4.67757 19.5828 5.8879C19.7484 7.11979 19.75 8.73963 19.75 11C19.75 13.2604 19.7484 14.8802 19.5828 16.1121C19.4201 17.3224 19.1115 18.0454 18.5784 18.5784C18.0454 19.1115 17.3224 19.4201 16.1121 19.5828C14.8802 19.7484 13.2604 19.75 11 19.75C8.73963 19.75 7.11979 19.7484 5.88789 19.5828C4.67757 19.4201 3.95462 19.1115 3.42157 18.5784C2.88853 18.0454 2.57994 17.3224 2.41722 16.1121C2.25159 14.8802 2.25 13.2604 2.25 11C2.25 8.73963 2.25159 7.11979 2.41722 5.8879C2.57994 4.67757 2.88853 3.95462 3.42157 3.42157C3.95462 2.88853 4.67757 2.57994 5.88789 2.41722C7.11979 2.25159 8.73963 2.25 11 2.25C13.2604 2.25 14.8802 2.25159 16.1121 2.41722Z"
              fill="#00205A" />
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M11.9622 7.97726C11.6735 8.27428 11.6802 8.74911 11.9773 9.03781C12.1388 9.19487 12.396 9.3971 12.6407 9.58933C12.6596 9.60416 12.6786 9.61906 12.6976 9.63405C12.9434 9.82696 13.2061 10.0333 13.4548 10.2439C13.4572 10.246 13.4595 10.248 13.4619 10.25L7 10.25C6.58579 10.25 6.25 10.5858 6.25 11C6.25 11.4142 6.58579 11.75 7 11.75L13.4619 11.75C13.4595 11.752 13.4572 11.754 13.4548 11.7561C13.2061 11.9667 12.9434 12.173 12.6976 12.3659C12.6786 12.3809 12.6596 12.3958 12.6407 12.4107C12.396 12.6029 12.1388 12.8051 11.9773 12.9622C11.6802 13.2509 11.6735 13.7257 11.9622 14.0227C12.2509 14.3198 12.7257 14.3265 13.0227 14.0378C13.114 13.9491 13.2958 13.8035 13.5672 13.5903C13.5869 13.5748 13.6069 13.5592 13.6272 13.5432C13.8693 13.3532 14.1534 13.1302 14.4245 12.9005C14.715 12.6543 15.0168 12.3787 15.2515 12.1032C15.369 11.9652 15.485 11.8096 15.5746 11.6422C15.661 11.4807 15.75 11.2583 15.75 11C15.75 10.7417 15.661 10.5193 15.5746 10.3578C15.485 10.1904 15.369 10.0348 15.2515 9.89679C15.0168 9.62131 14.715 9.34574 14.4245 9.09954C14.1534 8.8698 13.8693 8.64683 13.6272 8.45676C13.6069 8.44084 13.5869 8.42515 13.5672 8.40971C13.2958 8.19651 13.114 8.05089 13.0227 7.96219C12.7257 7.67349 12.2509 7.68023 11.9622 7.97726Z"
              fill="#00205A" />
          </svg>
        </span>
        <div class="title-container">
          <p class="title">
            {{'INVESTMENT_FUNDS.MEETING.VOTE_MEETING_TITLE' | translate
            }}
          </p>
          <p class="sub-title">
            {{'INVESTMENT_FUNDS.MEETING.VOTE_MEETING_DESC' | translate
            }}
          </p>
        </div>
      </div>

    </div>

  </div>
  <div class="row">
    <div class="col-lg-8">
      <!-- Meeting Details Section -->
      <div class="voting-container mb-4" *ngIf="proposalData">
        <div class="voting-header">
          <p class="section-title navy-color">
            {{'INVESTMENT_FUNDS.MEETING.MEETING_DETAILS' | translate}}
          </p>
          <p class="sub-title">
            {{ proposalData.subject }}
          </p>
          <p class="info" *ngIf="proposalData.description">
            {{ proposalData.description }}
          </p>
        </div>
      </div>

      <!-- Loading State -->
      <div class="text-center py-5" *ngIf="isLoading">
        <mat-spinner diameter="50"></mat-spinner>
        <p class="mt-3">{{'INVESTMENT_FUNDS.MEETING.LOADING' | translate}}</p>
      </div>

      <!-- Voting Form -->
      <div class="suggestion-voting mb-3" *ngIf="!isLoading && !hasError && timeSlots.length > 0">
        <h2 class="section-title">
          {{'INVESTMENT_FUNDS.MEETING.PROPOSED_DATES' | translate}}
        </h2>

        <form [formGroup]="voteForm" (ngSubmit)="confirmVote()">
          <mat-radio-group formControlName="selectedTimeSlot" class="voting-options">
            <div class="vote-option d-flex align-items-baseline"
                 [class.disabled]="proposalData?.currentUserVoted"
                 *ngFor="let timeSlot of timeSlots; trackBy: trackByTimeSlotId">
              <mat-radio-button [value]="timeSlot.id" class="me-3">
              </mat-radio-button>

              <div class="option-details w-100 d-flex justify-content-between align-items-center">
                <div>
                  <p class="mb-0">
                    <!-- {{ timeSlot.proposedDate | dateHijriConverter }} -->
                     <span class="gregorian">{{timeSlot.proposedDate | date:'d/M/y' }}</span>-
                    <span class="hijri">{{timeSlot.proposedDate | dateHijriConverter}}</span>
                  </p>
                  <p>{{ formatTime(timeSlot.proposedTime) }}</p>
                </div>
                <div>
                  <span class="votes">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.3503 17.623 3.8507 18.1676 4.55231C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89317 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z"
                            stroke="#181D27" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    {{ timeSlot.voteCount || 0 }}
                    {{'INVESTMENT_FUNDS.MEETING.VOTES' | translate}}
                  </span>
                </div>
              </div>
            </div>
          </mat-radio-group>


          <!-- Submit Button - Only show if user hasn't voted yet -->
          <app-custom-button
            *ngIf="!proposalData?.currentUserVoted"
            [btnName]="'INVESTMENT_FUNDS.MEETING.CONFIRM_VOTE' | translate"
            (click)="confirmVote()"
            class="submit-vote mx-2 mt-3"
            [buttonType]="buttonEnum.Primary"
            [iconName]="IconEnum.verify"
            [disabled]="isLoading || isSubmitting"
            [loading]="isSubmitting">
          </app-custom-button>
        </form>
      </div>

      <!-- No Time Slots Available -->
      <div class="alert alert-info" *ngIf="!isLoading && !hasError && timeSlots.length === 0">
        <p class="mb-0">{{'INVESTMENT_FUNDS.MEETING.NO_TIME_SLOTS_AVAILABLE' | translate}}</p>
      </div>

    </div>
    <div class="col-md-4">
      <div class="voting-container" *ngIf="proposalData && !isLoading">
        <div class="voting-header">
          <p class="section-title navy-color">
            {{'INVESTMENT_FUNDS.MEETING.VOTE_RESULTS' | translate}}
          </p>

          <!-- Voting Summary -->
          <div class="result-card mb-3" *ngIf="proposalData.votingSummary">
            <p class="result">{{ proposalData.votingSummary.participationDisplay }}</p>
            <p class="result-text">
              {{'INVESTMENT_FUNDS.MEETING.MEMBER_VOTED' | translate}}
            </p>
            <!-- <p class="participation-rate">
              {{ proposalData.votingSummary.participationRatePercent }}%
              {{'INVESTMENT_FUNDS.MEETING.PARTICIPATION_RATE' | translate}}
            </p> -->
          </div>

          <!-- Voting Results by Time Slot -->
          <div class="voting-data mb-3" *ngFor="let result of votingResults; trackBy: trackByTimeSlotId">
            <div class="voting-header">
              <h1 class="voting-date">{{ formatDate(result.proposedDate) }}</h1>
              <!-- <p class="voting-time">{{ formatTime(result.proposedTime) }}</p> -->
              <div class="vote-count">
                {{ result.voteCount || 0 }}
                {{'INVESTMENT_FUNDS.MEETING.VOTES' | translate}}
              </div>
            </div>

            <!-- Voters List -->
            <div class="voters-list" *ngIf="result.voterNames && result.voterNames.length > 0">
              <div class="voter-item" *ngFor="let voterName of result.voterNames">
                <span class="voter-name">{{ voterName?.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
