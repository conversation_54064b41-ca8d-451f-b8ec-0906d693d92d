.box-info-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
  transition: all 0.15s ease-in-out;

  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }

  &:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }

  .card-body {
    padding: 1.5rem;
  }

  .icon-section {
    i {
      font-size: 2.5rem;
      opacity: 0.8;
    }
  }

  .content-section {
    .card-title {
      font-size: 0.875rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .card-value {
      font-size: 2rem;
      line-height: 1.2;
    }
  }

  .trend-section {
    i {
      font-size: 0.875rem;
    }

    small {
      font-weight: 500;
    }
  }

  // Color variants
  &.card-primary {
    border-left: 4px solid #007bff;

    .icon-section i {
      color: #007bff;
    }
  }

  &.card-success {
    border-left: 4px solid #28a745;

    .icon-section i {
      color: #28a745;
    }
  }

  &.card-info {
    border-left: 4px solid #17a2b8;

    .icon-section i {
      color: #17a2b8;
    }
  }

  &.card-warning {
    border-left: 4px solid #ffc107;

    .icon-section i {
      color: #ffc107;
    }
  }

  &.card-danger {
    border-left: 4px solid #dc3545;

    .icon-section i {
      color: #dc3545;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .card-body {
      padding: 1rem;
    }

    .icon-section {
      i {
        font-size: 2rem;
      }
    }

    .content-section {
      .card-value {
        font-size: 1.5rem;
      }
    }
  }

  // RTL Support
  html[dir="rtl"] & {
    .icon-section {
      margin-right: 0;
      margin-left: 1rem;
    }

    &.card-primary,
    &.card-success,
    &.card-info,
    &.card-warning,
    &.card-danger {
      border-left: none;
      border-right: 4px solid;
    }

    .trend-section {
      .ms-1 {
        margin-left: 0 !important;
        margin-right: 0.25rem !important;
      }
    }
  }

  // Dark theme support
  :root.dark-theme & {
    background-color: #2d3748;
    color: #e2e8f0;

    .card-title {
      color: #a0aec0 !important;
    }

    .text-muted {
      color: #a0aec0 !important;
    }
  }
}
