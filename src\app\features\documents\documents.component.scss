.documents-container {
    .page-header {
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a365d;
            margin: 0;
        }

        .page-description {
            color: #718096;
            margin: 8px 0 0 0;
            font-size: 14px;
        }
    }

    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;
    }

    .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;
        text-align: center;

        .empty-state-content {
            h3 {
                color: #666;
                margin-bottom: 16px;
            }

            p {
                color: #999;
                font-size: 14px;
            }
        }
    }

    .documents-content {
        .documents-tabs {
            ::ng-deep {
                .mat-mdc-tab-labels {
                    border-bottom: 1px solid #ddd;
                    margin-bottom: 12px;

                    .mat-mdc-tab.mdc-tab {
                        color: #939393;
                        font-weight: 500;
                        font-size: 10px;
                        height: 40px;
                        padding: 0;
                        margin-inline: 15px;
                        min-width: fit-content;
                    }

                    .mat-mdc-tab .mdc-tab-indicator__content--underline {
                        border-bottom: 4px solid #00205A !important;
                        border-radius: 16px;
                        border-top-style: none !important;
                    }

                    .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
                        color: #00205A !important;
                        font-weight: 600 !important;
                    }

                    .mat-mdc-tab .mdc-tab__text-label {
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 24px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                    }

                    .mat-mdc-tab.mdc-tab--active {
                        color: #00205A !important;
                    }

                    .mat-mdc-tab .mdc-tab__ripple {
                        display: none;
                    }

                    .mat-mdc-tab .mdc-tab__content {
                        position: relative;
                        pointer-events: pointer;
                        line-height: 24px;
                        height: 24px;
                        width: 100%;
                        text-align: center;
                        justify-content: center;
                    }
                }
            }
        }

        .tab-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            gap: 16px;

            p {
                color: #718096;
                margin: 0;
                font-size: 14px;
            }
        }
    }
}

// RTL Support
[dir="rtl"] {
    .documents-container {
        .page-header {
            .d-flex {
                flex-direction: row-reverse;
            }
        }
    }
}