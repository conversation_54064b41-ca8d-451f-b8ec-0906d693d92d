@import "../../../../../assets/scss/variables";


.edit-resolution-page {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;

  .breadcrumb-section {
    margin-bottom: 20px;
  }

  .page-header-section {
    margin-bottom: 30px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    .spinner-border {
      width: 3rem;
      height: 3rem;
      margin-bottom: 20px;
    }

    .loading-text {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }

  .main-content {
    .resolution-info-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 30px;
      overflow: hidden;

      .card-header {
        background: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .card-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #00205a;
        }

        .resolution-status {
          .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;

            &.status-1 { // Draft
              background: #fff3cd;
              color: #856404;
            }

            &.status-2 { // Pending
              background: #d1ecf1;
              color: #0c5460;
            }

            &.status-3 { // Approved
              background: #d4edda;
              color: #155724;
            }

            &.status-4 { // Waiting for confirmation
              background: #ffeaa7;
              color: #b8860b;
            }

            &.status-5 { // Confirmed
              background: #d4edda;
              color: #155724;
            }

            &.status-6 { // Not approved
              background: #f8d7da;
              color: #721c24;
            }

            &.status-7 { // Rejected
              background: #f8d7da;
              color: #721c24;
            }

            &.status-8 { // Voting in progress
              background: #e2e3e5;
              color: #383d41;
            }

            &.status-9 { // Completing data
              background: #d1ecf1;
              color: #0c5460;
            }
          }
        }
      }

      .card-body {
        padding: 20px;

        .row {
          margin: 0;

          .col-md-4 {
            margin-bottom: 15px;
            font-size: 14px;

            strong {
              color: #00205a;
            }

            span {
              color: #666;
            }
          }
        }
      }
    }

    .edit-form-container {
      background: #F8FAFC;
      border-radius: 16px;
      border: 0.5px solid #DCE0E3;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 16px;

      .form-section {
        margin-bottom: 40px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 20px;
          margin-top: 20px;  
          padding-bottom: 10px;
          // border-bottom: 2px solid #e0e0e0;

          h6 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #00205a;

          }
          .items-num{
            display: inline-flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 14px;
            background: rgba(38, 86, 135, 0.12);
            color: #000;
            font-size: 16px;
            font-weight: 400;
            line-height: 18px;
          }
        }

        // Status display styling removed - now handled by form-builder component

        // Form builder header styling
        .header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 10px;
        }
        
        .hr-first{
          margin: 24px 0 15px;
          border: 1px solid #b6b6b6;
        }
        .hr-last{
          border: 1px solid #b6b6b6;
          margin: 32px 0 16px;
        }

        .section-divider {
          border: none;
          height: 1px;
          background: linear-gradient(to right, #e0e0e0, transparent);
          margin: 20px 0;
        }

        

        .empty-items {
          text-align: center;
          padding: 60px 20px;
          background: #fafbfc;
          border: 2px dashed #e0e0e0;
          border-radius: 12px;
          margin-bottom: 20px;

          .empty-icon {
            margin-bottom: 20px;

            i {
              font-size: 64px;
              color: #d0d7de;
            }
          }

          .empty-message {
            font-size: 16px;
            color: #656d76;
            margin-bottom: 24px;
            font-weight: 500;
          }

          .add-first-item-btn {
            background: #007bff;
            border-color: #007bff;
            color: white;
            padding: 12px 24px;
            font-weight: 600;
            border-radius: 8px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;

            &:hover {
              background: #0056b3;
              border-color: #004085;
              transform: translateY(-1px);
            }
          }
        }

        // Attachments Styles
        .section-header {
          .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;

            .attachments-info {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 14px;

              .attachments-count {
                color: #666;

                strong {
                  color: #007bff;
                }
              }

              .remaining-count {
                color: #28a745;
                font-size: 12px;
              }
            }
          }
        }

        .attachments-container {
          margin-bottom: 20px;

          .attachment-card {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            margin-bottom: 12px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              transform: translateY(-1px);
            }

            .attachment-icon {
              flex-shrink: 0;
              width: 48px;
              height: 48px;
              background: #fff5f5;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #dc3545;
              font-size: 20px;
            }

            .attachment-info {
              flex: 1;

              .attachment-name {
                font-size: 15px;
                font-weight: 600;
                color: #00205a;
                margin-bottom: 6px;
                word-break: break-word;
                line-height: 1.4;
              }

              .attachment-meta {
                display: flex;
                align-items: center;
                gap: 16px;
                font-size: 13px;
                color: #666;

                .file-size {
                  font-weight: 500;
                  color: #007bff;
                }

                .upload-date {
                  color: #999;
                }
              }
            }

            .attachment-actions {
              display: flex;
              gap: 8px;

              .btn {
                padding: 6px 10px;
                font-size: 12px;
                border-radius: 6px;
                transition: all 0.2s ease;

                &.download-btn {
                  border-color: #007bff;
                  color: #007bff;

                  &:hover {
                    background: #007bff;
                    color: white;
                  }
                }

                &.delete-btn {
                  border-color: #dc3545;
                  color: #dc3545;

                  &:hover {
                    background: #dc3545;
                    color: white;
                  }
                }
              }
            }
          }
        }

        .empty-attachments {
          text-align: center;
          padding: 50px 20px;
          background: #fafbfc;
          border: 2px dashed #e0e0e0;
          border-radius: 12px;
          margin-bottom: 20px;

          .empty-icon {
            margin-bottom: 16px;

            i {
              font-size: 56px;
              color: #d0d7de;
            }
          }

          .empty-message {
            font-size: 15px;
            color: #656d76;
            margin: 0;
            font-weight: 500;
          }
        }

        .upload-section {
          text-align: center;
          padding: 20px;
          border: 2px dashed #007bff;
          border-radius: 8px;
          background: #f8f9ff;

          .upload-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border-color: #007bff;
            color: #007bff;
            background: white;
            border-radius: 6px;
            transition: all 0.2s ease;

            &:hover {
              background: #007bff;
              color: white;
            }
          }

          .upload-info {
            margin-top: 10px;

            small {
              font-size: 11px;
              color: #666;
            }
          }
        }

        .max-reached-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 16px;
          background: #fff3cd;
          color: #856404;
          border-radius: 6px;
          font-size: 14px;

          i {
            font-size: 16px;
          }
        }
      }

      .form-actions {
        margin-top: 40px;
        padding-top: 30px;
        border-top: 2px solid #e0e0e0;

        .actions-container {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 15px;
          flex-wrap: wrap;

          // .btn {
          //   padding: 12px 24px;
          //   font-weight: 500;
          //   border-radius: 8px;
          //   display: flex;
          //   align-items: center;
          //   gap: 8px;
          //   min-width: 120px;
          //   justify-content: center;

          //   &.cancel-btn {
          //     background: #6c757d;
          //     border-color: #6c757d;
          //     color: white;

          //     &:hover {
          //       background: #5a6268;
          //       border-color: #545b62;
          //     }
          //   }

          //   &.save-draft-btn {
          //     border-color: #007bff;
          //     color: #007bff;

          //     &:hover {
          //       background: #007bff;
          //       color: white;
          //     }
          //   }

          //   &.submit-btn {
          //     background: #007bff;
          //     border-color: #007bff;

          //     &:hover {
          //       background: #0056b3;
          //       border-color: #004085;
          //     }
          //   }

          //   &:disabled {
          //     opacity: 0.6;
          //     cursor: not-allowed;
          //   }
          // }
        }
      }
    }
  }

  // Resolution Items Styles
        .items-container {
          margin-bottom: 20px;

          .resolution-item-card {
            padding: 16px 10px;
            border-radius: 8px;
            background-color: white;
            margin-bottom: 16px;
            // background: white;
            // border: 1px solid #e0e0e0;
            // border-radius: 12px;
            // margin-bottom: 16px;
            // overflow: hidden;
            // transition: all 0.2s ease;
            // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            // &:hover {
            //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            //   transform: translateY(-1px);
            // }

            .item-header {
              // background: #f8f9fa;
              // padding: 16px 20px;
              // border-bottom: 1px solid #e0e0e0;

              .item-info {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .item-number {
                  font-size: 20px;
                  font-weight: 500;
                  line-height: 32px;
                  color: #00205a;
                  // background: #e3f2fd;
                  // padding: 4px 12px;
                  // border-radius: 16px;
                }

                .item-actions {
                  display: flex;
                  gap: 8px;

                  .btn {
                    padding: 8px;
                    // font-size: 12px;
                    // border-radius: 6px;
                    &:hover{
                      background: unset;
                      border: unset;
                      color: unset;
                    }
                    &.view-conflict-btn {
                      border-radius: 8px;
                      border: 1px solid #FFC800;
                      background: #FFFAEB;
                      color: #B68107;
                      font-size: 14px;
                      font-style: normal;
                      font-weight: 400;
                      line-height: 18px;
                    }
                  }
                }
              }

              .item-conflict {
                display: flex;
                align-items: center;
                justify-content: center;

                .conflict-indicator {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  color: #856404;
                  font-size: 13px;
                  font-weight: 500;
                  margin-bottom: 8px;

                  i {
                    color: #ffc107;
                  }
                }
              }
            }

            .item-body {
              margin-top: 15px;

              .item-title {
                font-size: 16px;
                font-weight: 600;
                color: #00205a;
                margin-bottom: 8px;
              }

              .item-description {
                    color: #333;
                    line-height: 21px;
                    font-size: 14px;
                    font-weight: 400;
              }
            }
          }
        }

  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;

    .error-message {
      text-align: center;
      padding: 40px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-width: 500px;

      i {
        font-size: 48px;
        color: #dc3545;
        margin-bottom: 20px;
      }

      h5 {
        color: #00205a;
        margin-bottom: 15px;
      }

      p {
        color: #666;
        margin-bottom: 30px;
        line-height: 1.5;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .edit-resolution-page {
    padding: 15px;

    .main-content {
      .resolution-info-card {
        .card-header {
          flex-direction: column;
          gap: 15px;
          text-align: center;
        }

        .card-body {
          .row {
            .col-md-4 {
              margin-bottom: 20px;
            }
          }
        }
      }

      .edit-form-container {
        padding: 20px;

        .form-actions {
          .actions-container {
            flex-direction: column;

            .btn {
              width: 100%;
            }
          }
        }
      }
    }
  }
}

.add-item-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  // border: 2px solid #007bff;
  background: white;
  color: $navy-blue;
  border-radius: 8px;
  font-weight: 400;
  transition: all 0.2s ease;

  // &:hover {
  //   background: #007bff;
  //   color: white;
  //   transform: translateY(-1px);
  //   box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  // }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.resolution-items{
  max-height: 500px;
  overflow-y: auto;
}

  ::ng-deep{
  .swal2-popup{
    border-radius: 8px;
    padding-inline: 24px;
    .swal2-title{
      color: $navy-blue;
      font-size: 20px;
      font-weight: 400;
      line-height: 32px;
      padding: 0px;
      margin: 0px; 
    }
    .swal2-html-container{
      color: $dark;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px; 
      padding: 0px;
      margin-top: 10px;
    }
    .swal2-textarea{
      margin-inline: 0px;
      border-color: $grey;
      height: 100px;
      resize: none;
      overflow-y: auto;
    }
  }
}
