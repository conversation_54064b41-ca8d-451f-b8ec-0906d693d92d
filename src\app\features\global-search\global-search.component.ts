import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { GeneralServiceProxy, GeneralSearchResultDto, GeneralSearchResultDtoPaginatedResult } from '@core/api/api.generated';
import { Subject, takeUntil, finalize } from 'rxjs';
import { FilePreviewService } from '@core/services/file-preview.service';


@Component({
  selector: 'app-global-search',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    PageHeaderComponent
  ],
  templateUrl: './global-search.component.html',
  styleUrl: './global-search.component.scss'
})
export class GlobalSearchComponent implements OnInit, OnDestroy {
  searchTerm: string | undefined;
  searchResults: GeneralSearchResultDto[] = [];
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  totalPages = 0;
  pageSizeOptions = [10, 25, 50, 100];

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private translateService: TranslateService,
    private generalService: GeneralServiceProxy,
    private router: Router,
    private filePreviewService : FilePreviewService
  ) { }

  ngOnInit() {
    this.route.queryParams.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      this.searchTerm = params['searchTrim'] || '';
      if (this.searchTerm) {
        this.performSearch();
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  public performSearch(): void {
    if (!this.searchTerm) return;

    this.isLoading = true;
    this.hasError = false;
    this.errorMessage = '';

    this.generalService.generalSearch(
      this.currentPage,
      this.pageSize,
      this.searchTerm,
      'Id desc'
    ).pipe(
      takeUntil(this.destroy$),
      finalize(() => this.isLoading = false)
    ).subscribe({
      next: (response: GeneralSearchResultDtoPaginatedResult) => {
        if (response.successed) {
          this.searchResults = response.data || [];
          this.totalCount = response.totalCount;
          this.totalPages = response.totalPages;
          this.currentPage = response.currentPage;
        } else {
          this.handleError(response.message || 'GLOBAL_SEARCH.ERROR_OCCURRED');
        }
      },
      error: (error) => {
        console.error('Search error:', error);
        this.handleError('GLOBAL_SEARCH.ERROR_OCCURRED');
      }
    });
  }

  private handleError(message: string): void {
    this.hasError = true;
    this.errorMessage = message;
    this.searchResults = [];
    this.totalCount = 0;
    this.totalPages = 0;
  }


  // Pagination methods
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.performSearch();
    }
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const halfRange = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfRange);
    let endPage = Math.min(this.totalPages, this.currentPage + halfRange);

    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxPagesToShow) {
      if (startPage === 1) {
        endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);
      } else {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.currentPage = 1; // Reset to first page when changing page size
    this.performSearch();
  }

  onResultClick(result: GeneralSearchResultDto): void {
    const category = result?.category?.toLowerCase();
    let CuurentFundNameValue = localStorage.getItem("fundName") ?? '';
    localStorage.setItem("fundName", result?.fundName ?? CuurentFundNameValue);
    switch (category) {
      case 'fund':
         this.router.navigate(['admin/investment-funds/fund-details'], {
            queryParams: { id: result.fundId }
          });
        break;
      case 'resolution':
        debugger
        if(result?.hasVote > 0)
        {
          this.router.navigate(['/admin/investment-funds/resolutions/view-voting-result', result.id], {
            queryParams: { fundId: result.fundId },
          });
        }
        else{
          this.router.navigate(['/admin/investment-funds/resolutions/details', result.id], {
            queryParams: { fundId: result.fundId },
          });
        }
        break;
      case 'assessment':
          this.router.navigate(['/admin/investment-funds/assessments/details', result.id]);
        break;
      case 'document':
          this.downloadFile(result.attachmentId);
        break;
      case 'meeting':
          this.router.navigate(['/admin/investment-funds/meetings/details'], {
            queryParams: {id : result.id , fundId: result.fundId }
          });
        break;
      default:
        if (result.fundId) {
          this.router.navigate(['/admin/dashboard']);
        }
    }
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === LanguageEnum.en;
  }

  onPreviousPage(): void {
    if (this.canGoPrevious()) {
      this.onPageChange(this.currentPage - 1);
    }
  }

  onNextPage(): void {
    if (this.canGoNext()) {
      this.onPageChange(this.currentPage + 1);
    }
  }


  downloadFile(attachmentId: any): void {
    // if (!this.canDownload())
    //   return;

    if (!attachmentId) {
      return;
    }

    this.filePreviewService.previewFile({id : attachmentId}, () => {
    });
  }
}
