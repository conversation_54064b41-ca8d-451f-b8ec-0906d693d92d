import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';

@Component({
  selector: 'app-global-search',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    PageHeaderComponent
  ],
  templateUrl: './global-search.component.html',
  styleUrl: './global-search.component.scss'
})
export class GlobalSearchComponent {
  searchTerm: string | undefined;
    // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalCount = 100;
  totalPages = 0;
  pageSizeOptions = [10, 25, 50, 100];

  constructor(
    private route: ActivatedRoute,
    private translateService: TranslateService
  ) { }

  ngOnInit() { 
    this.route.queryParams.subscribe(params => {
      this.searchTerm = params['searchTrim'] || '';
    });

    console.log('Search term:', this.searchTerm);
  }

  // Pagination methods
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      // this.reloadCurrentData();
    }
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const halfRange = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfRange);
    let endPage = Math.min(this.totalPages, this.currentPage + halfRange);

    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxPagesToShow) {
      if (startPage === 1) {
        endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);
      } else {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.currentPage = 1; // Reset to first page when changing page size
    // this.reloadCurrentData();
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === LanguageEnum.en;
  }

  onPreviousPage(): void {
    if (this.canGoPrevious()) {
      this.onPageChange(this.currentPage - 1);
    }
  }

  onNextPage(): void {
    if (this.canGoNext()) {
      this.onPageChange(this.currentPage + 1);
    }
  }
}
