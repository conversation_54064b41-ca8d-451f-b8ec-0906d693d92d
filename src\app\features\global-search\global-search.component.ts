import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-global-search',
  standalone: true,
  imports: [],
  templateUrl: './global-search.component.html',
  styleUrl: './global-search.component.scss'
})
export class GlobalSearchComponent {
  searchTerm: string | undefined;
  constructor(
    private route: ActivatedRoute,
    private breadcrumbService: BreadcrumbService
  ) { }

  ngOnInit() { 
    this.route.queryParams.subscribe(params => {
      this.searchTerm = params['searchTrim'] || '';
    });

    console.log('Search term:', this.searchTerm);
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === LanguageEnum.en;
  }
}
