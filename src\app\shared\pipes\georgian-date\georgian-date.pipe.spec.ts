import { TestBed } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';
import { DateTime } from 'luxon';
import { GeorgianDatePipe } from './georgian-date.pipe';
import { LanguageService } from '@core/gl-services/language-services/language.service';

describe('GeorgianDatePipe', () => {
  let pipe: GeorgianDatePipe;
  let mockLanguageService: jasmine.SpyObj<LanguageService>;

  beforeEach(() => {
    const languageServiceSpy = jasmine.createSpyObj('LanguageService', [], {
      currentLang: 'en',
      currentLang$: { subscribe: jasmine.createSpy('subscribe') }
    });

    TestBed.configureTestingModule({
      imports: [TranslateModule.forRoot()],
      providers: [
        { provide: LanguageService, useValue: languageServiceSpy }
      ]
    });

    mockLanguageService = TestBed.inject(LanguageService) as jasmine.SpyObj<LanguageService>;
    pipe = new GeorgianDatePipe(mockLanguageService);
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should format date string in English long format', () => {
    const dateString = '2024-01-15T10:30:00Z';
    const result = pipe.transform(dateString, 'long');
    expect(result).toBe('January 15, 2024');
  });

  it('should format date string in English short format', () => {
    const dateString = '2024-01-15T10:30:00Z';
    const result = pipe.transform(dateString, 'short');
    expect(result).toBe('Jan 15, 2024');
  });

  it('should format DateTime object', () => {
    const dateTime = DateTime.fromISO('2024-01-15T10:30:00Z');
    const result = pipe.transform(dateTime, 'long');
    expect(result).toBe('January 15, 2024');
  });

  it('should format Date object', () => {
    const date = new Date('2024-01-15T10:30:00Z');
    const result = pipe.transform(date, 'long');
    expect(result).toBe('January 15, 2024');
  });

  it('should return empty string for null input', () => {
    const result = pipe.transform(null);
    expect(result).toBe('');
  });

  it('should return empty string for undefined input', () => {
    const result = pipe.transform(undefined);
    expect(result).toBe('');
  });

  it('should return empty string for invalid date string', () => {
    const result = pipe.transform('invalid-date');
    expect(result).toBe('');
  });

  it('should handle Arabic language formatting', () => {
    // Simulate Arabic language
    Object.defineProperty(mockLanguageService, 'currentLang', {
      get: () => 'ar'
    });
    
    pipe = new GeorgianDatePipe(mockLanguageService);
    const dateString = '2024-01-15T10:30:00Z';
    const result = pipe.transform(dateString, 'long');
    expect(result).toBe('15 يناير 2024');
  });
});
