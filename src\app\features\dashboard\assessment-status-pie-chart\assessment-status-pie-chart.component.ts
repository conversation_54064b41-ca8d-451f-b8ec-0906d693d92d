import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { NgxChartsModule, Color, ScaleType } from '@swimlane/ngx-charts';

// API Types
import { AssessmentStatusBreakdownDto } from '@core/api/api.generated';

export interface PieChartData {
  name: string;
  value: number;
  extra?: {
    code: string;
    color: string;
    count: number;
  };
}

@Component({
  selector: 'app-assessment-status-pie-chart',
  standalone: true,
  imports: [CommonModule, TranslateModule,  NgxChartsModule],
  templateUrl: './assessment-status-pie-chart.component.html',
  styleUrl: './assessment-status-pie-chart.component.scss'
})
export class AssessmentStatusPieChartComponent implements OnChanges {
  @Input() assessmentStatusData: AssessmentStatusBreakdownDto[] = [];
  @Input() totalAssessments: number = 0;
  @Input() loading: boolean = false;

  // Chart configuration
  chartData: PieChartData[] = [];
  // Chart options for pie chart
  view: [number, number] = [170, 170];
  colorScheme: Color = {
    name: 'assessment-status',
    selectable: true,
    group: ScaleType.Ordinal,
    domain: ['#7555ac','#17a2b8','#0e700e','#C50F1F','#2f80ed','#27ae60']
  };

  // Pie chart specific options
  showLegend: boolean = true;
  showLabels: boolean = true;
  doughnut: boolean = false;
  explodeSlices: boolean = false;
  animations: boolean = true;
  gradient: boolean = false;
  tooltipDisabled: boolean = false;

  // Assessment status color mapping
  private statusColors: { [key: string]: string } = {
    '1':' #7555ac',
    '2':' #17a2b8',
    '3':' #0e700e',
    '4':' #C50F1F',
    '5': '#2f80ed',
    '6': '#27ae60',
  };





  ngOnChanges(changes: SimpleChanges): void {
    if (changes['assessmentStatusData'] || changes['totalAssessments']) {
      this.updateChartData();
    }
  }

  /**
   * Transform API data to chart format
   */
  private updateChartData(): void {
    if (!this.assessmentStatusData || this.assessmentStatusData.length === 0 || this.totalAssessments === 0) {
      this.chartData = [];
      return;
    }

    this.chartData = this.assessmentStatusData.map(item => {
      const statusKey = item.statusId.toString() ||   '0';
      const color = this.statusColors[statusKey] || '#6c757d';
      const count = item.count || 0;

      return {
        name: item.status || 'Unknown',
        value: count,
        extra: {
          code: statusKey,
          color: color,
          count: count
        }
      };
    });
 this.colorScheme = {
      name: 'vote-results',
      selectable: true,
      group: ScaleType.Ordinal,
      domain: this.chartData.map(item => item.extra?.color || '#6c757d')
    };
  }



  /**
   * Check if chart has data
   */
  get hasData(): boolean {
    return this.chartData.length > 0 && this.totalAssessments > 0;
  }




  /**
   * TrackBy function for chart data
   */
  trackByName(_index: number, item: PieChartData): string {
    return item.name;
  }


get centerPercentage(): number {
  const total = this.chartData.reduce((sum, item) => sum + item.value, 0);
  const first = this.chartData[0]?.value || 0;
  return total ? Math.round((first / total) * 100) : 0;
}


}
