import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-box-info-card',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './box-info-card.component.html',
  styleUrl: './box-info-card.component.scss'
})
export class BoxInfoCardComponent {
  @Input() title: string = '';
  @Input() value: number | string = 0;
  @Input() icon: string = 'fas fa-info-circle';
  @Input() color: 'primary' | 'success' | 'info' | 'warning' | 'danger' = 'primary';
  @Input() subtitle: string = '';
  @Input() trend: 'up' | 'down' | 'neutral' = 'neutral';
  @Input() trendValue: string = '';

  get cardColorClass(): string {
    return `card-${this.color}`;
  }

  get iconColorClass(): string {
    return `text-${this.color}`;
  }

  get trendIcon(): string {
    switch (this.trend) {
      case 'up':
        return 'fas fa-arrow-up text-success';
      case 'down':
        return 'fas fa-arrow-down text-danger';
      default:
        return 'fas fa-minus text-muted';
    }
  }
}
