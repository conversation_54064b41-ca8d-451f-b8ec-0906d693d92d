# Edit Assessment Component Integration Guide

## Overview

The `edit-assessment` component has been created following JadwaUI's established patterns and is ready for integration with the backend API. This guide outlines the steps needed to complete the integration once the backend API endpoints are available.

## Component Structure

```
src/app/features/assessments/components/edit-assessment/
├── edit-assessment.component.ts
├── edit-assessment.component.html
└── edit-assessment.component.scss
```

## Current Implementation Status

### ✅ Completed Features

1. **Component Structure**: Complete TypeScript, HTML, and SCSS files
2. **Route Configuration**: Edit route added to assessments routing (`/edit/:id`)
3. **Form Pre-population**: Loads and populates existing assessment data
4. **Breadcrumb Navigation**: Follows established patterns with proper navigation
5. **Form Validation**: Same validation rules as create-assessment
6. **Question Management**: Full CRUD operations for assessment questions
7. **File Upload**: Attachment handling for attachment-type assessments
8. **Loading States**: Proper loading indicators and error handling
9. **Translation Support**: Complete Arabic and English translations
10. **RTL Support**: Full right-to-left layout support

### 🔄 Pending API Integration

The following API methods need to be implemented in the backend and regenerated with NSwag:

## Required Backend API Endpoints

### 1. Get Assessment by ID
```typescript
// Method signature (will be auto-generated by NSwag)
getById(id: number): Observable<AssessmentDetailsDtoBaseResponse>
```

**Endpoint**: `GET /api/Assessment/{id}`
**Purpose**: Retrieve detailed assessment data for editing
**Response**: `AssessmentDetailsDtoBaseResponse` containing full assessment details including questions

### 2. Update Assessment
```typescript
// Method signature (will be auto-generated by NSwag)
update(command: UpdateAssessmentCommand): Observable<AssessmentDetailsDtoBaseResponse>
```

**Endpoint**: `PUT /api/Assessment/Update`
**Purpose**: Update existing assessment data
**Request Body**: `UpdateAssessmentCommand`

### 3. UpdateAssessmentCommand Interface
```typescript
export interface UpdateAssessmentCommand {
  id: number;                                    // Assessment ID to update
  fundId: number;                               // Fund ID
  title: string;                                // Assessment title
  description?: string;                         // Optional description
  type: AssessmentType;                         // Questionnaire or Attachment
  dueDate?: DateTime;                           // Optional due date
  instructions?: string;                        // Optional instructions
  allowAnonymousResponses: boolean;             // Allow anonymous responses
  allowResponseEditing: boolean;                // Allow response editing
  questions?: CreateAssessmentQuestionDto[];    // Questions for questionnaire type
  attachmentId?: number;                        // Attachment ID for attachment type
  saveAsDraft: boolean;                         // Save as draft flag
}
```

## Integration Steps

### Step 1: Backend API Implementation
1. Implement `GetAssessmentById` endpoint in the backend
2. Implement `UpdateAssessment` endpoint in the backend
3. Ensure proper validation and error handling
4. Test endpoints with Swagger/Postman

### Step 2: NSwag Regeneration
1. Run `nswag run` to regenerate API clients
2. Verify new methods are available in `AssessmentServiceProxy`
3. Confirm `UpdateAssessmentCommand` interface is generated

### Step 3: Component Integration
1. Replace placeholder API calls in `edit-assessment.component.ts`
2. Update the `loadAssessmentData()` method:

```typescript
// Replace the temporary implementation with:
private loadAssessmentData(): void {
  this.isLoading = true;
  
  this.assessmentServiceProxy.getById(this.currentAssessmentId)
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.successed) {
          this.currentAssessment = response.data;
          this.populateForm(response.data);
        } else {
          this.errorModalService.showError(
            response.message || this.translateService.instant('ASSESSMENTS.LOAD_ERROR')
          );
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorModalService.showError('ASSESSMENTS.LOAD_ERROR');
      }
    });
}
```

3. Update the `callUpdateApi()` method:

```typescript
// Replace the placeholder implementation with:
private callUpdateApi(isDraft: boolean = false): void {
  const command = new UpdateAssessmentCommand({
    id: this.currentAssessmentId,
    fundId: this.currentFundId,
    title: this.formGroup.get('title')?.value ?? '',
    type: this.formGroup.get('type')?.value ?? AssessmentType._1,
    attachmentId: this.formGroup.get('attachmentId')?.value || undefined,
    questions: this.formGroup.get('type')?.value === AssessmentType._1 ? this.questions : undefined,
    saveAsDraft: isDraft,
    allowAnonymousResponses: false,
    allowResponseEditing: true,
    description: this.formGroup.get('description')?.value ?? undefined,
    dueDate: this.formGroup.get('dueDate')?.value ? DateTime.fromJSDate(new Date(this.formGroup.get('dueDate')?.value)) : undefined,
    instructions: this.formGroup.get('instructions')?.value ?? undefined
  });

  this.assessmentServiceProxy.update(command)
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response: AssessmentDetailsDtoBaseResponse) => {
        this.isFormSubmitted = false;
        this.isValidationFire = false;

        if (response.successed) {
          const messageKey = isDraft ? 'ASSESSMENTS.SUCCESS_SAVED_DRAFT' : 'ASSESSMENTS.SUCCESS_UPDATED';
          this.errorModalService.showSuccess(this.translateService.instant(messageKey));

          this.router.navigate(['/admin/investment-funds/assessments'], {
            queryParams: { fundId: this.currentFundId }
          });
        } else {
          this.errorModalService.showError(
            response.message || this.translateService.instant('ASSESSMENTS.UPDATE_ERROR')
          );
        }
      },
      error: (error: any) => {
        this.isFormSubmitted = false;
        this.isValidationFire = false;
        this.errorModalService.showError(this.translateService.instant('ASSESSMENTS.UPDATE_ERROR'));
      }
    });
}
```

### Step 4: Remove Placeholder Code
1. Remove the `UpdateAssessmentCommand` interface definition from the component
2. Import it from `@core/api/api.generated` instead
3. Remove all TODO comments and placeholder implementations

### Step 5: Navigation Integration
Add edit navigation to assessment list/details components:

```typescript
// In assessment list component
onEditAssessment(assessment: any): void {
  this.router.navigate(['/admin/investment-funds/assessments/edit', assessment.id], {
    queryParams: { fundId: this.currentFundId }
  });
}
```

```html
<!-- In assessment card template -->
<app-custom-button
  [btnName]="'COMMON.EDIT' | translate"
  [buttonType]="buttonEnum.Secondary"
  [iconName]="IconEnum.edit"
  (click)="onEditAssessment(assessment)">
</app-custom-button>
```

## Testing Checklist

### Unit Testing
- [ ] Component initialization
- [ ] Form validation
- [ ] API error handling
- [ ] Question management
- [ ] File upload handling

### Integration Testing
- [ ] Route navigation with parameters
- [ ] Form pre-population with API data
- [ ] Successful update flow
- [ ] Error handling scenarios
- [ ] Breadcrumb navigation

### User Acceptance Testing
- [ ] Edit existing questionnaire assessment
- [ ] Edit existing attachment assessment
- [ ] Switch between assessment types
- [ ] Add/edit/remove questions
- [ ] Save as draft functionality
- [ ] Form validation messages
- [ ] Success/error notifications

## Security Considerations

1. **Authorization**: Ensure proper role-based access control
2. **Validation**: Server-side validation of all input data
3. **File Upload**: Secure file handling for attachments
4. **Audit Trail**: Log assessment modifications

## Performance Considerations

1. **Lazy Loading**: Component is already configured for lazy loading
2. **Form Optimization**: Use OnPush change detection if needed
3. **API Caching**: Consider caching assessment data for better UX
4. **File Handling**: Optimize large file uploads

## Accessibility Features

1. **Keyboard Navigation**: All interactive elements are keyboard accessible
2. **Screen Reader Support**: Proper ARIA labels and descriptions
3. **Focus Management**: Logical tab order and focus indicators
4. **Color Contrast**: Meets WCAG 2.1 AA standards

## Browser Compatibility

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Deployment Notes

1. Ensure all translation keys are properly deployed
2. Verify NSwag configuration includes new endpoints
3. Test in both development and production environments
4. Monitor API performance and error rates

---

**Note**: This component follows all established JadwaUI patterns and is ready for immediate use once the backend API endpoints are implemented and NSwag clients are regenerated.
