<div class="box-info-card card h-100"
     [ngClass]="cardColorClass"
     role="region"
     [attr.aria-label]="title + ': ' + value"
     tabindex="0">
  <div class="card-body d-flex align-items-center">
    <!-- Icon Section -->
    <div class="icon-section me-3" role="img" [attr.aria-label]="title">
      <i [class]="icon + ' ' + iconColorClass" aria-hidden="true"></i>
    </div>

    <!-- Content Section -->
    <div class="content-section flex-grow-1">
      <div class="d-flex justify-content-between align-items-start">
        <div>
          <h6 class="card-title mb-1 text-muted" id="card-title-{{title}}">{{ title }}</h6>
          <h3 class="card-value mb-0 fw-bold" [attr.aria-labelledby]="'card-title-' + title">{{ value }}</h3>
          <small class="text-muted" *ngIf="subtitle" [attr.aria-describedby]="'card-title-' + title">{{ subtitle }}</small>
        </div>

        <!-- Trend Section -->
        <div class="trend-section"
             *ngIf="trend !== 'neutral' && trendValue"
             role="img"
             [attr.aria-label]="'Trend: ' + trend + ' ' + trendValue">
          <div class="d-flex align-items-center">
            <i [class]="trendIcon" aria-hidden="true"></i>
            <small class="ms-1" [attr.aria-label]="trendValue">{{ trendValue }}</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
