@import "../../../../../assets/scss/variables";

.dialog-container {
  max-width: 100%;

  .header {
    padding: 24px 24px 12px;
    color: #00205A;
    font-size: 20px;
    font-weight: 500;
    line-height: 32px;
    border-bottom: 1px solid #E5E7EB;
  }
}

hr {
  // padding: 0;
  // margin: 0;
}

.form-fields {
    max-height: 500px;
    padding: 0px 24px;
    overflow-x: auto;

    .member-note-wrapper {
  // background-color: #f9fafb;
  border-radius: 8px;

  .member-note-header {
    color: #6b7280;
    font-size: 0.875rem;
    padding: 0px 10px;

    .img-container {
      width: 40px;
      padding: 0;
    }

    .member-note-user-info {
      display: flex;
      gap: 0.5rem;

      .user-name {
        color: #00205A;


        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }

      .user-role,
      .user-time {
        color: #828282;
        font-size: 10px;
        font-weight: 400;
        line-height: 16px;
        margin-bottom: 0px;
      }

    }
  }

  .member-note-body {
    border-radius: 6px;
    font-size: 1rem;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #333;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 0;
  }
}
}

  .form-container {
    // margin-bottom: 16px;
    padding: 24px;
    border-top: 1px solid #E5E7EB;

    label {
      display: block;
      margin-bottom: 8px;
      color: #4F4F4F;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;

      &.required::after {
        content: '';
        color: #dc3545;
      }
    }
    .form-control{
      border: 1px solid $mid-gray-bg;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
      }
    }

  }


.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #E5E7EB;
}
