import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';
import { CreateGuard } from '@core/guards/create.guard';

export const MEETINGS_ROUTES: Routes = [
  
  {
    path: '',
    loadComponent: () =>
      import('./meetings.component').then(
        (m) => m.MeetingsComponent
      ),
  },
  {
    path: 'create',
    loadComponent: () =>
      import('./components/create-meeting/create-meeting.component').then(
        (m) => m.CreateMeetingComponent
      ),
  },
  {
    path: 'edit',
    loadComponent: () =>
      import('./components/edit-meeting/edit-meeting.component').then(
        (m) => m.EditMeetingComponent
      ),
  },
  {
    path: 'add',
    loadComponent: () =>
      import('./components/add-meeting-proposed-meeting/add-meeting-proposed-meeting.component').then(m => m.AddMeetingProposedMeetingComponent),
     canActivate: [AuthGuard , CreateGuard]
  },
  {
    path: 'vote',
    loadComponent: () =>
      import('./components/vote-proposed-meeting/vote-proposed-meeting.component').then(m => m.VoteProposedMeetingComponent),
  },
  {
    path: 'details',
    loadComponent: () =>
      import('./components/view-meeting-details/view-meeting-details.component').then(m => m.ViewMeetingDetailsComponent),
  },
    {
      path: 'proposed-meeting-vote-result',
      loadComponent: () =>
        import('./components/proposed-meeting-view-voting-result/proposed-meeting-view-voting-result.component').then(m => m.ProposedMeetingViewVotingResultComponent),
    },
  
  {
    path: 'edit',
    loadComponent: () =>
      import('./components/edit-meeting/edit-meeting.component').then(
        (m) => m.EditMeetingComponent
      ),
  },
];
