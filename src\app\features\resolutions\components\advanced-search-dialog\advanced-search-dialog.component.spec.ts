import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { AdvancedSearchDialogComponent } from './advanced-search-dialog.component';
import { ResolutionsServiceProxy, TypesServiceProxy, AssessmentServiceProxy } from '@core/api/api.generated';
import { ActivatedRoute } from '@angular/router';
import { DateConversionService } from '@shared/services/date.service';

describe('AdvancedSearchDialogComponent', () => {
  let component: AdvancedSearchDialogComponent;
  let fixture: ComponentFixture<AdvancedSearchDialogComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<AdvancedSearchDialogComponent>>;
  let mockResolutionsProxy: jasmine.SpyObj<ResolutionsServiceProxy>;
  let mockTypesProxy: jasmine.SpyObj<TypesServiceProxy>;
  let mockAssessmentProxy: jasmine.SpyObj<AssessmentServiceProxy>;
  let mockActivatedRoute: any;
  let mockDateConversionService: jasmine.SpyObj<DateConversionService>;

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockResolutionsProxy = jasmine.createSpyObj('ResolutionsServiceProxy', ['statuses']);
    mockTypesProxy = jasmine.createSpyObj('TypesServiceProxy', ['all']);
    mockAssessmentProxy = jasmine.createSpyObj('AssessmentServiceProxy', ['fundPermission']);
    mockDateConversionService = jasmine.createSpyObj('DateConversionService', ['mapStringToSelectedDate']);

    mockActivatedRoute = {
      params: of({}),
      queryParams: of({ fundId: '14' })
    };

    // Setup mock responses
    mockResolutionsProxy.statuses.and.returnValue(of({
      successed: true,
      data: [],
      statusCode: 200,
      message: '',
      errors: []
    } as any));

    mockTypesProxy.all.and.returnValue(of({
      successed: true,
      data: [],
      statusCode: 200,
      message: '',
      errors: []
    } as any));

    // Setup mock assessment service response
    mockAssessmentProxy.fundPermission.and.returnValue(of({
      successed: true,
      data: {
        fundName: 'Test Fund',
        isBoardMember: false,
        boardMemberId: undefined,
        canAdd: false
      },
      statusCode: 200,
      message: '',
      errors: []
    } as any));

    mockDateConversionService.mapStringToSelectedDate.and.returnValue({ year: 2025, month: 1, day: 1 });

    await TestBed.configureTestingModule({
      imports: [
        AdvancedSearchDialogComponent,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: {} },
        { provide: ResolutionsServiceProxy, useValue: mockResolutionsProxy },
        { provide: TypesServiceProxy, useValue: mockTypesProxy },
        { provide: AssessmentServiceProxy, useValue: mockAssessmentProxy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: DateConversionService, useValue: mockDateConversionService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AdvancedSearchDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.formGroup).toBeDefined();
    expect(component.formGroup.get('search')?.value).toBe('');
    expect(component.formGroup.get('status')?.value).toBe('');
    expect(component.formGroup.get('resolutionType')?.value).toBe('');
  });

  it('should apply filters and close dialog with clean data', () => {
    // Set form values
    component.formGroup.patchValue({
      search: 'test search',
      status: 'approved',
      resolutionType: '',
      fromDate: '2025-01-01',
      toDate: '',
      createdBy: 'user1'
    });

    component.applyFilters();

    expect(mockDialogRef.close).toHaveBeenCalledWith({
      search: 'test search',
      status: 'approved',
      fromDate: '2025-01-01',
      createdBy: 'user1'
    });
  });

  it('should reset filters', () => {
    // Set some values first
    component.formGroup.patchValue({
      search: 'test',
      status: 8 // statusId for approved
    });

    component.resetFilters();

    expect(component.formGroup.get('search')?.value).toBe(null);
    expect(component.formGroup.get('status')?.value).toBe(null);
    expect(component.isFormSubmitted).toBe(false);
  });

  it('should close dialog without data', () => {
    component.closeDialog();
    expect(mockDialogRef.close).toHaveBeenCalledWith();
  });

  it('should have correct form controls configuration', () => {
    expect(component.formControls.length).toBe(6);

    const searchControl = component.formControls.find(c => c.formControlName === 'search');
    expect(searchControl?.label).toBe('INVESTMENT_FUNDS.RESOLUTIONS.SEARCH');

    const statusControl = component.formControls.find(c => c.formControlName === 'status');
    expect(statusControl?.options?.length).toBe(5); // All statuses + 4 specific statuses
  });

  it('should hide member voting status filter for non-board members', async () => {
    // Setup non-board member response
    mockAssessmentProxy.fundPermission.and.returnValue(of({
      successed: true,
      data: {
        fundName: 'Test Fund',
        isBoardMember: false,
        boardMemberId: undefined,
        canAdd: false
      }
    } as any));

    // Reinitialize component
    await component.ngOnInit();
    fixture.detectChanges();

    // Check that member voting status is filtered out
    const filteredControls = component.filteredFormControls;
    const memberVotingStatusControl = filteredControls.find(c => c.formControlName === 'memberVotingStatus');
    expect(memberVotingStatusControl).toBeUndefined();
    expect(component.isBoardMember).toBe(false);
  });

  it('should show member voting status filter for board members', async () => {
    // Setup board member response
    mockAssessmentProxy.fundPermission.and.returnValue(of({
      successed: true,
      data: {
        fundName: 'Test Fund',
        isBoardMember: true,
        boardMemberId: 123,
        canAdd: true
      }
    } as any));

    // Reinitialize component
    await component.ngOnInit();
    fixture.detectChanges();

    // Check that member voting status is included
    const filteredControls = component.filteredFormControls;
    const memberVotingStatusControl = filteredControls.find(c => c.formControlName === 'memberVotingStatus');
    expect(memberVotingStatusControl).toBeDefined();
    expect(memberVotingStatusControl?.options?.length).toBeGreaterThan(0);
    expect(component.isBoardMember).toBe(true);
  });

  it('should handle permission check errors gracefully', async () => {
    // Setup error response
    mockAssessmentProxy.fundPermission.and.returnValue(of({
      successed: false,
      data: null,
      message: 'Permission check failed'
    } as any));

    // Reinitialize component
    await component.ngOnInit();
    fixture.detectChanges();

    // Should default to non-board member (hide filter)
    expect(component.isBoardMember).toBe(false);
    const filteredControls = component.filteredFormControls;
    const memberVotingStatusControl = filteredControls.find(c => c.formControlName === 'memberVotingStatus');
    expect(memberVotingStatusControl).toBeUndefined();
  });
});
