import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute ,RouterModule} from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { DurationTimePipe } from '@core/gl-pipes/duration-time.pipe';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { MeetingStatusEnum } from '@shared/enum/meeting-enums';
import { SizeEnum } from '@shared/enum/size-enum';
import { Subject, Subscription, timer, takeUntil, take } from 'rxjs';
import { AttachmentCardComponent } from 'src/app/features/resolutions/components/attachment-card/attachment-card.component';
import { EditorConfig, NgxSimpleTextEditorModule, ST_BUTTONS } from 'ngx-simple-text-editor';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import Swal from 'sweetalert2';
import {
  MeetingServiceProxy,
  MeetingSessionResponseDto,
  MeetingSessionResponseDtoBaseResponse,
  MeetingSessionAttendeeDto,
  MeetingLocationType,
  UpdateAttendanceCommand,
  AttendanceUpdateItem,
  UpdateAttendanceResponseBaseResponse,
  StartMeetingCommand,
  EndMeetingCommand,
  StringBaseResponse,
  RecordMeetingMinutesDto,
  MeetingMinutesDtoBaseResponse,
  MinutesStatus,
  MeetingNotesResponseDtoBaseResponse,
  MeetingNotesResponseDto,
  MeetingNoteDto,
  AddMeetingNoteRequest,
  MeetingNoteDtoBaseResponse
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { DateConversionService } from '@shared/services/date.service';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { TokenService, userRole } from 'src/app/features/auth/services/token.service';
import { MatDialog } from '@angular/material/dialog';
import { AddAttachmentPopupComponent, AddAttachmentDialogData } from '../add-attachment-popup/add-attachment-popup.component';
import { CancelMeetingDialogComponent, CancelMeetingDialogData } from '../cancel-meeting-dialog/cancel-meeting-dialog.component';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';
// Attendance status enum
export enum AttendanceStatus {
  Unregistered = 1,
  Attending = 5,
  Away = 6,
}

// Interface for meeting attendee
export interface MeetingAttendee {
  id: number;
  fullName: string;
  role: string;
  attendanceStatus: AttendanceStatus;
}

// Extended interface for meeting notes with UI properties
export interface ExtendedMeetingNoteDto extends Omit<MeetingNoteDto, 'replies'> {
  showReplyForm?: boolean;
  replyText?: string;
  replies?: ExtendedMeetingNoteDto[];
}

@Component({
  selector: 'app-view-meeting-details',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    AttachmentCardComponent,
    DurationTimePipe,
    FormsModule,
    RouterModule,
    NgxSimpleTextEditorModule,
    MatCardModule,
    MatIconModule ,
    DateHijriConverterPipe
  ],
  templateUrl: './view-meeting-details.component.html',
  styleUrl: './view-meeting-details.component.scss'
})
export class ViewMeetingDetailsComponent implements OnInit, OnDestroy {
  breadcrumbItems: IBreadcrumbItem[] = [];
  breadcrumbSizeEnum = SizeEnum;
  createButtonIcon = IconEnum;
  buttonEnum = ButtonTypeEnum
  meetingStatus = MeetingStatusEnum
  attendanceStatus = AttendanceStatus;

  isExpanded: boolean= true;
  isExpandedItem: boolean= true;
  isExpandedAction: boolean= true;
  isExpandedReply: boolean= true;

  countDown: Subscription | undefined;
  counterTimer: number = 0
  tick = 1000;
  private destroy$ = new Subject<void>();

  // API Integration Properties
  meetingId: number = 0;
  fundId: number = 0;
  isLoading: boolean = false;
  meetingData: MeetingSessionResponseDto | null = null;

  // Flags to prevent duplicate API calls
  isLoadingMeetingData: boolean = false;
  isStartingMeeting: boolean = false;
  isEndingMeeting: boolean = false;
  isUpdatingAttendance: boolean = false;
  isDeletingAttachment: boolean = false;
  isSavingMinutes: boolean = false;

  // Meeting minutes properties
  meetingMinutesContent: string = '';
  meetingMinutesId: number = 0;

  // Meeting notes properties
  meetingNotes: ExtendedMeetingNoteDto[] = [];
  meetingNotesData: MeetingNotesResponseDto | null = null;
  isLoadingNotes: boolean = false;
  isAddingNote: boolean = false;

  // Attendees data - will be populated from API
  attendees: MeetingAttendee[] = [];

 

  comments: any[] = [];

  config: EditorConfig = {   };

  dateNow = new Date().toISOString();

  // Reply functionality
  showReplyForm:  boolean = false;
  replyText: string = '';
  showRemoveButton: boolean=true;
  isEndLoading: boolean=false;
  meetingMinutesStatus: any;

  constructor(
    private route: ActivatedRoute,
    private meetingServiceProxy: MeetingServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService,
    private dateConversionService: DateConversionService,
    private tokenService: TokenService,
    private dialog: MatDialog,
    private router: Router,
    private breadcrumbService: BreadcrumbService
  ) {}

  // Getter methods for dynamic summary counts
  get attendingCount(): number {
    return this.attendees.filter(attendee => attendee.attendanceStatus === AttendanceStatus.Attending).length;
  }

  get awayCount(): number {
    return this.attendees.filter(attendee => attendee.attendanceStatus === AttendanceStatus.Away).length;
  }

  get unregisteredCount(): number {
    return this.attendees.filter(attendee => attendee.attendanceStatus === AttendanceStatus.Unregistered).length;
  }

  ngOnInit() {
    this.config = {
      placeholder: this.translateService.instant('INVESTMENT_FUNDS.MEETING.EDITOR_PLACEHOLDER'),
      buttons: ST_BUTTONS,
    };
  
    // Get meetingId and fundId from route parameters (take first emission only)
    // Timer will be initialized after meeting data is loaded
    this.route.queryParams
      .pipe(
        takeUntil(this.destroy$),
        take(1) // Only take the first emission to prevent duplicate calls
      )
      .subscribe((queryParams: any) => {
        this.meetingId = +queryParams['id'] || 0;
        this.fundId = +queryParams['fundId'] || 0;

        if (this.meetingId > 0) {
          this.loadMeetingData();
        } else {
          this.errorModalService.showError(this.translateService.instant('INVESTMENT_FUNDS.MEETING.INVALID_MEETING_ID'));
        }
      });
      this.initlizationBreadcrumb();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.countDown) {
      this.countDown.unsubscribe();
    }
  }

  initlizationBreadcrumb() {
    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      { label: 'INVESTMENT_FUNDS.MEETING.MEETINGSTITLE', url:  `/admin/investment-funds/meetings?fundId=${this.fundId} ` },
      { label: 'INVESTMENT_FUNDS.MEETING.MEETING_DETAILS', url: '', disabled: true },

   ];
   this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
 
  }

  /**
   * Load meeting data from API (prevents duplicate calls)
   */
  private loadMeetingData(): void {
    // Prevent duplicate API calls
    if (this.isLoadingMeetingData) {
      return;
    }

    this.isLoadingMeetingData = true;
    this.isLoading = true;

    this.meetingServiceProxy.meetingSession(this.meetingId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: MeetingSessionResponseDtoBaseResponse) => {
          this.isLoadingMeetingData = false;
          this.isLoading = false;
          if (response.successed && response.data) {
            this.meetingData = response.data;
            this.populateAttendees(response.data.attendees);
            // Reinitialize timer with new meeting data
            this.initMeetingTimerCounter();
            // Load meeting minutes if meeting is in progress or ended
            if (this.meetingData.meetingStatusId === 3 || this.meetingData.meetingStatusId === 4) {
              this.loadMeetingMinutes();
            }
            // Load meeting notes
            this.loadMeetingNotes();
          } else {
            this.errorModalService.showError(this.translateService.instant(response.message || 'INVESTMENT_FUNDS.MEETING.LOAD_ERROR'));
          }
        },
        error: (error) => {
          this.isLoadingMeetingData = false;
          this.isLoading = false;
          console.error('Error loading meeting data:', error);
          this.errorModalService.showError(error.parsedMessage);
        }
      });
  }

  /**
   * Populate attendees from API response
   */
  private populateAttendees(apiAttendees: MeetingSessionAttendeeDto[] | undefined): void {
    if (!apiAttendees) {
      this.attendees = [];
      return;
    }

    this.attendees = apiAttendees.map(attendee => ({
      id: attendee.id,
      fullName: attendee.attendeeName || 'Unknown',
      role: attendee.attendeeType || 'Unknown Role',
      attendanceStatus: this.mapAttendanceStatus(attendee.attendanceStatusId)
    }));
  }

  /**
   * Map API attendance status to local enum
   */
  private mapAttendanceStatus(statusId: number): AttendanceStatus {
    switch (statusId) {
      case 5:
        return AttendanceStatus.Attending;
      case 6:
        return AttendanceStatus.Away;
      default:
        return AttendanceStatus.Unregistered;
    }
  }

  /**
   * Format meeting date for display (Gregorian)
   */
  formatMeetingDate(): string {
    if (!this.meetingData?.meetingDate) return '';

    try {
      const date = this.meetingData.meetingDate.toJSDate();
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting meeting date:', error);
      return '';
    }
  }

  /**
   * Format meeting date for display (Hijri)
   */
  formatMeetingDateHijri(): any {
    if (!this.meetingData?.meetingDate) return '';

    try {
      var dateStruct = { year:this.meetingData.meetingDate?.year, month:this.meetingData.meetingDate?.month, day: this.meetingData.meetingDate?.day };
      var date= this.dateConversionService.convertGregorianToHijri(dateStruct);
      return date.day+"/"+date.month+"/"+date.year  ;
    } catch (error) {
      console.error('Error formatting Hijri date:', error);
      return '';
    }
  }
 

  /**
   * Get attachment count
   */
  getAttachmentCount(): number {
    return this.meetingData?.attachments?.length || 0;
  }

  /**
   * Check if current user can start/end meetings
   * Only Fund Manager, Legal Counsel, or Board Secretary can start/end meetings
   */
  canControlMeeting(): boolean {
    return this.tokenService.hasRole(userRole.fundManager) ||
           this.tokenService.hasRole(userRole.headOfRealEstate) ||
           this.tokenService.hasRole(userRole.legalCouncil) ||
           this.tokenService.hasRole(userRole.boardSecretary);
  }

  /**
   * Check if user can start the meeting
   */
  canStartMeeting(): boolean {
    return this.canControlMeeting() &&
           (this.meetingData?.canStartMeeting ?? false);
  }

  /**
   * Check if user can end the meeting
   */
  canEndMeeting(): boolean {
    return this.canControlMeeting() &&
           (this.meetingData?.canEndMeeting ?? false);
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }
  toggleExpandItems() {
    this.isExpandedItem = !this.isExpandedItem;
  }

  toggleExpandActions() {
    this.isExpandedAction = !this.isExpandedAction;
  }
  
  toggleExpandReply() {
    this.isExpandedReply = !this.isExpandedReply;
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.meetingStatus.NotStartedYet:
        return 'pending';
      case this.meetingStatus.InProgress:
        return 'meeting-inProgress';
      case this.meetingStatus.Finished:
        return 'finished';
      case this.meetingStatus.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }
  startMeeting() {
    // Prevent duplicate API calls
    if (this.isStartingMeeting) {
      return;
    }

    // Validate user role
    if (!this.canControlMeeting()) {
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.ACCESS_DENIED_ROLE_REQUIRED')
      );
      return;
    }

    // Validate meeting can be started
    if (!this.canStartMeeting()) {
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.CANNOT_START_MEETING')
      );
      return;
    }

    // Create start meeting command
    const command = new StartMeetingCommand();
    command.meetingId = this.meetingId;

    // Set loading states
    this.isStartingMeeting = true;
    this.isLoading = true;

    // Call start meeting API
    this.meetingServiceProxy.startMeeting(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isStartingMeeting = false;
          this.isLoading = false;
          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('INVESTMENT_FUNDS.MEETING.MEETING_STARTED_SUCCESS')
            );
            // Reset the loading flag for meeting data before reloading
            this.isLoadingMeetingData = false;
            this.loadMeetingData();
          } else {
            this.errorModalService.showError(
              this.translateService.instant(response.message || 'INVESTMENT_FUNDS.MEETING.START_MEETING_ERROR')
            );
          }
        },
        error: (error) => {
          this.isStartingMeeting = false;
          this.isLoading = false;
          console.error('Error starting meeting:', error);
          this.errorModalService.showError(
            this.translateService.instant(error.parsedMessage)
          );
        }
      });
  }

  endMeeting() {
    // Prevent duplicate calls - check both dialog opening and API call states
    if (this.isEndingMeeting || this.isEndLoading) {
      return;
    }

    // Validate user role
    if (!this.canControlMeeting()) {
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.ACCESS_DENIED_ROLE_REQUIRED')
      );
      return;
    }

    // Validate meeting can be ended
    if (!this.canEndMeeting()) {
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.CANNOT_END_MEETING')
      );
      return;
    }

    // Set loading state to prevent multiple dialog openings
    this.isEndLoading = true;

    // Show confirmation dialog
    const title = this.translateService.instant('COMMON.CONFIRM');
    const message = this.translateService.instant('INVESTMENT_FUNDS.MEETING.END_MEETING_CONFIRM');

    const dialogData: CancelMeetingDialogData = {
      meetingSubject: this.meetingData?.subject || 'Meeting',
      title: title,
      message: message
    };

    const dialogRef = this.dialog.open(CancelMeetingDialogComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(result => {
      // Reset loading state when dialog closes
      this.isEndLoading = false;
      this.isLoading = false;

      if (result === true) {
        this.performEndMeeting();
      }
    });
  }

  /**
   * Perform the actual end meeting operation
   */
  private performEndMeeting(): void {

    // Create end meeting command
    const command = new EndMeetingCommand();
    command.meetingId = this.meetingId;

    // Set loading states
    this.isEndingMeeting = true;
    this.isLoading = true;

    // Call end meeting API
    this.meetingServiceProxy.endMeeting(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isEndingMeeting = false;
          this.isLoading = false;
          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('INVESTMENT_FUNDS.MEETING.MEETING_ENDED_SUCCESS')
            );
            // Navigate back to meetings list after successful end meeting
            this.router.navigate(['/admin/investment-funds/meetings'], {
              queryParams: { fundId: this.fundId }
            });
          } else {
            this.errorModalService.showError(
              this.translateService.instant(response.message || 'INVESTMENT_FUNDS.MEETING.END_MEETING_ERROR')
            );
          }
        },
        error: (error) => {
          this.isEndingMeeting = false;
          this.isLoading = false;
          console.error('Error ending meeting:', error);
          this.errorModalService.showError(
            error.parsedMessage          );
        }
      });
  }

  cancel() {}

  saveAttendees() {
    // Prevent duplicate API calls
    if (this.isUpdatingAttendance) {
      return;
    }

    if (!this.meetingData || !this.meetingData.canUpdateAttendance) {
      this.errorModalService.showError(this.translateService.instant('INVESTMENT_FUNDS.MEETING.CANNOT_UPDATE_ATTENDANCE'));
      return;
    }

    if (this.attendees.length === 0) {
      this.errorModalService.showError(this.translateService.instant('INVESTMENT_FUNDS.MEETING.NO_ATTENDEES_TO_UPDATE'));
      return;
    }

    // Create attendance update items
    const attendanceUpdates: AttendanceUpdateItem[] = this.attendees.map(attendee => {
      const updateItem = new AttendanceUpdateItem();
      updateItem.attendeeId = attendee.id;
      updateItem.attendanceStatusId = this.mapLocalStatusToApi(attendee.attendanceStatus);
      return updateItem;
    });

    // Create update command
    const command = new UpdateAttendanceCommand();
    command.meetingId = this.meetingId;
    command.attendanceUpdates = attendanceUpdates;
if(command.attendanceUpdates.filter(item => item.attendanceStatusId == 1).length != 0){
  this.errorModalService.showError(this.translateService.instant('INVESTMENT_FUNDS.MEETING.REGISTER_ATTENDEES'));
  return;
}
    // Set loading states
    this.isUpdatingAttendance = true;

    // Call API
    this.meetingServiceProxy.updateAttendanceMeeting(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: UpdateAttendanceResponseBaseResponse) => {
          this.isUpdatingAttendance = false;
          if (response.successed && response.data) {
            const successCount = response.data.successfulUpdates;
            const failedCount = response.data.failedUpdates;

            if (failedCount === 0) {
              this.errorModalService.showSuccess(
                this.translateService.instant('INVESTMENT_FUNDS.MEETING.ATTENDANCE_UPDATED_SUCCESS', { count: successCount })
              );
            } else {
              this.errorModalService.showError(
                this.translateService.instant('INVESTMENT_FUNDS.MEETING.ATTENDANCE_PARTIAL_SUCCESS', {
                  success: successCount,
                  failed: failedCount
                })
              );
            }
          } else {
            this.errorModalService.showError(
              this.translateService.instant(response.message || 'INVESTMENT_FUNDS.MEETING.ATTENDANCE_UPDATE_ERROR')
            );
          }
        },
        error: (error) => {
          this.isUpdatingAttendance = false;
          console.error('Error updating attendance:', error);
          this.errorModalService.showError(
            error.parsedMessage          );
        }
      });
  }

  /**
   * Map local attendance status to API status
   */
  private mapLocalStatusToApi(status: AttendanceStatus): number {
    switch (status) {
      case AttendanceStatus.Attending:
        return 5;
      case AttendanceStatus.Away:
        return 6;
      case AttendanceStatus.Unregistered:
      default:
        return 1;
    }
  }

  /**
   * Save meeting minutes
   * @param isDraft - true for draft (status 3), false for final (status 4)
   */
  saveMeetingMinutes(isDraft: boolean = false): void {
    // Prevent duplicate API calls
    if (this.isSavingMinutes) {
      return;
    }

    // Validate meeting ID
    if (!this.meetingId) {
      this.errorModalService.showError(
        this.translateService.instant('COMMON.VALIDATION_ERROR')
      );
      return;
    }

    // Validate content
    if (!this.meetingMinutesContent || this.meetingMinutesContent.trim().length === 0) {
      this.errorModalService.showError(
        this.translateService.instant('COMMON.VALIDATION_ERROR')
      );
      return;
    }

    // Show confirmation dialog for final save (not draft)
    if (!isDraft) {
      const title = this.translateService.instant('COMMON.CONFIRM');
      const message = this.translateService.instant('INVESTMENT_FUNDS.MEETING.SEND_MINUTES_CONFIRM');

      const dialogData: CancelMeetingDialogData = {
        meetingSubject: this.meetingData?.subject || 'Meeting',
        title: title,
        message: message
      };

      const dialogRef = this.dialog.open(CancelMeetingDialogComponent, {
        width: '500px',
        data: dialogData,
        disableClose: false,
        autoFocus: false
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result === true) {
          this.performSaveMeetingMinutes(isDraft);
        }
      });
    } else {
      // For draft, save directly without confirmation
      this.performSaveMeetingMinutes(isDraft);
    }
  }

  /**
   * Perform the actual save operation
   */
  private performSaveMeetingMinutes(isDraft: boolean): void {

    // Create the request object
    const request = new RecordMeetingMinutesDto();
    request.id = this.meetingMinutesId || 0;
    request.meetingId = this.meetingId;
    request.content = this.meetingMinutesContent.trim();
    request.saveAsDraft = isDraft;
    request.status = isDraft ? MinutesStatus._1 : MinutesStatus._2; // 1 for draft, 2 for final

    // Set loading state
    this.isSavingMinutes = true;

    // Call API
    this.meetingServiceProxy.meetingMinutesPost(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isSavingMinutes = false;
          if (response.successed) {
            const messageKey = isDraft
              ? 'INVESTMENT_FUNDS.MEETING.MINUTES_SAVED_AS_DRAFT'
              : 'INVESTMENT_FUNDS.MEETING.MINUTES_SENT_SUCCESS';

            this.errorModalService.showSuccess(
              this.translateService.instant(messageKey)
            );

            // Reload meeting data to get updated minutes
            this.isLoadingMeetingData = false;
            this.loadMeetingData();
          } else {
            this.errorModalService.showError(
              this.translateService.instant(response.message || 'INVESTMENT_FUNDS.MEETING.MINUTES_SAVE_SEND_ERROR')
            );
          }
        },
        error: (error) => {
          this.isSavingMinutes = false;
          console.error('Error saving meeting minutes:', error);
          this.errorModalService.showError(
            error.parsedMessage          );
        }
      });
  }
  

  updateAttendanceStatus(attendeeId: number, newStatus: AttendanceStatus): void {
    const attendee = this.attendees.find(a => a.id === attendeeId);
    if (attendee) {
      attendee.attendanceStatus = newStatus;
      // The summary counts will automatically update due to the getter methods
    }
  }

  onAttendanceStatusChange(event: Event, attendeeId: number): void {
    const target = event.target as HTMLSelectElement;
    if (target) {
      const newStatus = parseInt(target.value) as AttendanceStatus;
      this.updateAttendanceStatus(attendeeId, newStatus);
    }
  }
  // getAttendanceStatusText(status: AttendanceStatus): string {
  //   switch (status) {
  //     case AttendanceStatus.Attending:
  //       return 'حاضر';
  //     case AttendanceStatus.Away:
  //       return 'غائب';
  //     case AttendanceStatus.Unregistered:
  //     default:
  //       return 'غير مسجل';
  //   }
  // }

  initMeetingTimerCounter() {
    // Prevent duplicate timer subscriptions
    if (this.countDown) {
      this.countDown.unsubscribe();
    }

    // Only start countdown if meeting is in progress (status 3)
    if (this.isMeetingInProgress()) {
      // Calculate the accurate remaining time based on meeting start date and planned duration
      const remainingTime = this.calculateRemainingMeetingTime();
      this.counterTimer = remainingTime;

      // Start the countdown timer
      this.countDown = timer(0, this.tick)
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          --this.counterTimer;
          if (this.counterTimer <= 0) {
            this.counterTimer = 0;
            this.countDown?.unsubscribe();
          }
        });
    } else {
      // If meeting is not in progress, just calculate and display the planned duration
      if (this.meetingData?.startTime && this.meetingData?.endTime) {
        const duration = this.calculateMeetingDurationInSeconds();
        this.counterTimer = duration;
      } else {
        // Fallback to default duration if times are not available
        this.counterTimer = 15 * 60; // 15 minutes default
      }
    }
  }

  /**
   * Calculate meeting duration in seconds from start and end times
   */
  private calculateMeetingDurationInSeconds(): number {
    if (!this.meetingData?.startTime || !this.meetingData?.endTime) {
      return 15 * 60; // Default 15 minutes
    }

    try {
      // Parse time strings (assuming format HH:MM)
      const [startHours, startMinutes] = this.meetingData.startTime.split(':').map(Number);
      const [endHours, endMinutes] = this.meetingData.endTime.split(':').map(Number);

      // Convert to total minutes
      const startTotalMinutes = startHours * 60 + startMinutes;
      const endTotalMinutes = endHours * 60 + endMinutes;

      // Calculate duration in minutes
      let durationMinutes = endTotalMinutes - startTotalMinutes;

      // Handle case where end time is next day (e.g., start: 23:00, end: 01:00)
      if (durationMinutes < 0) {
        durationMinutes += 24 * 60; // Add 24 hours
      }

      // Convert to seconds
      return durationMinutes * 60;
    } catch (error) {
      console.error('Error calculating meeting duration:', error);
      return 15 * 60; // Default 15 minutes
    }
  }

  /**
   * Calculate remaining meeting time in seconds based on actual meeting start date and planned duration
   */
  private calculateRemainingMeetingTime(): number {
    // If meeting hasn't actually started yet, return full duration
    if (!this.meetingData?.meetingStartDate) {
      return this.calculateMeetingDurationInSeconds();
    }

    try {
      // Get the planned meeting duration in seconds
      const totalDurationSeconds = this.calculateMeetingDurationInSeconds();

      // Get current time
      const now = new Date();

      // Get meeting start time (convert from DateTime to JS Date)
      const meetingStartTime = this.meetingData.meetingStartDate.toJSDate();

      // Calculate elapsed time since meeting started (in seconds)
      const elapsedTimeSeconds = Math.floor((now.getTime() - meetingStartTime.getTime()) / 1000);

      // Calculate remaining time
      const remainingTimeSeconds = totalDurationSeconds - elapsedTimeSeconds;

      // Return remaining time (minimum 0)
      return Math.max(0, remainingTimeSeconds);
    } catch (error) {
      console.error('Error calculating remaining meeting time:', error);
      // Fallback to full duration if there's an error
      return this.calculateMeetingDurationInSeconds();
    }
  }
 
  isMeetingInProgress(): boolean {
    if(this.meetingData?.canRemoveAttachment)
    this.showRemoveButton=true
  else
  this.showRemoveButton=false

    return this.meetingData?.meetingStatusId === 3;
  }

  /**
   * Check if countdown should be visible
   */
  shouldShowCountdown(): boolean {
    return this.isMeetingInProgress() && this.counterTimer > 0;
  }

  goBack() {
    this.router.navigate(['/admin/investment-funds/meetings'], {
      queryParams: { fundId: this.fundId ,selectedTab:1}
    });
  }

  /**
   * Open add attachment popup
   */
  addAttachment(): void {
    if (!this.meetingId) {
      this.errorModalService.showError(
        this.translateService.instant('COMMON.VALIDATION_ERROR')
      );
      return;
    }

    const dialogData: AddAttachmentDialogData = {
      meetingId: this.meetingId
    };

    const dialogRef = this.dialog.open(AddAttachmentPopupComponent, {
      width: '600px',
      maxWidth: '90vw',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.success && result?.attachedCount > 0) {
        // Reload meeting data to refresh attachments
        this.isLoadingMeetingData = false; // Reset flag to allow reload
        this.loadMeetingData();
      }
    });
  }

  /**
   * Remove attachment from meeting
   */
  removeAttachment(attachment: any): void {
    // Prevent duplicate API calls
    if (this.isDeletingAttachment) {
      return;
    }

    if (!attachment?.id) {
      this.errorModalService.showError(
        this.translateService.instant('COMMON.VALIDATION_ERROR')
      );
      return;
    }

    // Show confirmation dialog
    const title = this.translateService.instant('COMMON.CONFIRM_DELETE');
    const message = this.translateService.instant('INVESTMENT_FUNDS.MEETING.ATTACHMENT_DELETE_CONFIRM');

    const dialogData: CancelMeetingDialogData = {
      meetingSubject: attachment.fileName || 'Attachment',
      title: title,
      message: message
    };

    const dialogRef = this.dialog.open(CancelMeetingDialogComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.performDeleteAttachment(attachment);
      }
    });
  }

  /**
   * Perform the actual attachment deletion
   */
  private performDeleteAttachment(attachment: any): void {
    // Set loading state
    this.isDeletingAttachment = true;

    // Call delete attachment API
    var id=this.meetingData?.attachments?.find(a => a.id === attachment.id)?.id;
    this.meetingServiceProxy.meetingAttachments(Number(id))
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isDeletingAttachment = false;
          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('INVESTMENT_FUNDS.MEETING.ATTACHMENT_DELETED_SUCCESS')
            );
            // Reload meeting data to refresh attachments
            this.isLoadingMeetingData = false; // Reset flag to allow reload
            this.loadMeetingData();
          } else {
            this.errorModalService.showError(
              this.translateService.instant(response.message || 'COMMON.ERROR')
            );
          }
        },
        error: (error) => {
          this.isDeletingAttachment = false;
          console.error('Error deleting attachment:', error);
          this.errorModalService.showError(
            error.parsedMessage          );
        }
      });
  }
  
  getNotificationTime(createdAt: Date | any): string {
    const now = new Date();
    const notificationDate = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.NOW');
    } else if (diffInMinutes < 60) {
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+`${diffInMinutes}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.MINUTE');
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+` ${hours}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.HOUR');
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return this.translateService.instant('INVESTMENT_FUNDS.VOTING.SINCE')+' '+` ${days}`+this.translateService.instant('INVESTMENT_FUNDS.VOTING.DAY');
    }
  }

  /**
   * Load existing meeting minutes
   */
  private loadMeetingMinutes(): void {
    if (!this.meetingId) {
      return;
    }

    this.meetingServiceProxy.meetingMinutesGet(this.meetingId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: MeetingMinutesDtoBaseResponse) => {
          if (response.successed && response.data) {
            this.meetingMinutesContent = response.data.content || '';
            this.meetingMinutesId = response.data.id || 0;
            this.meetingMinutesStatus = response.data.status || 0;
          }
        },
        error: (error) => {
          console.error('Error loading meeting minutes:', error);
          // Don't show error message as minutes might not exist yet
          this.errorModalService.showError(error.parsedMessage)
        }
      });
  }

  /**
   * Check if save as draft button should be visible (status 3 - in progress)
   */
  canSaveAsDraft(): boolean {
    return ((this.meetingData?.meetingStatusId === 3||this.meetingData?.meetingStatusId === 4)&&
      (this.tokenService.getuserId() == this.meetingData.createdBy.toString())
    );
  }

  /**
   * Check if save meeting minutes button should be visible (status 4 - ended)
   */
  canSaveMeetingMinutes(): boolean {
    return (this.meetingData?.meetingStatusId === 4&&
      (this.tokenService.getuserId() == this.meetingData.createdBy.toString())

     )  }

  /**
   * Check if meeting minutes section should be visible
   */
  shouldShowMeetingMinutes(): boolean {
    return this.meetingData?.meetingStatusId === 3 || this.meetingData?.meetingStatusId === 4;
  }

  canEditMeetingNotes(): boolean { 
        return   (this.meetingMinutesStatus === 1 );
  }

  // Reply functionality methods
  toggleReplyForm(note?: any): void {
    if (note) {
      // Toggle reply form for specific note
      note.showReplyForm = !note.showReplyForm;
      if (!note.showReplyForm) {
        note.replyText = '';
      }
    } else {
      // Legacy method for backward compatibility
      this.showReplyForm = !this.showReplyForm;
    }
  }

  submitReply(note?: any): void {
    if (note && note.replyText && note.replyText.trim()) {
      // Submit reply for specific note
      this.addMeetingNote(note.replyText.trim(), note.id);
      note.showReplyForm = false;
      note.replyText = '';
    } else if (this.replyText && this.replyText.trim()) {
      // Legacy method for backward compatibility
      this.showReplyForm = false;
      this.replyText = '';
    }
  }

  cancelReply(note?: any): void {
    if (note) {
      // Cancel reply for specific note
      note.showReplyForm = false;
      note.replyText = '';
    } else {
      // Legacy method for backward compatibility
      this.showReplyForm = false;
      this.replyText = '';
    }
  }

  addNote() {
    Swal.fire({
      input: 'textarea',
      inputLabel: this.translateService.instant('INVESTMENT_FUNDS.MEETING.ADD_NOTE'),
      inputPlaceholder: this.translateService.instant('INVESTMENT_FUNDS.MEETING.WRITE_NOTE'),
      inputValidator: (value) => {
        if (!value || value.trim().length === 0) {
          return this.translateService.instant('COMMON.VALIDATION_ERROR');
        }
        return null;
      },
      showCancelButton: true,
      // confirmButtonColor: '#00205A',
      // cancelButtonColor: '#6c757d',
      confirmButtonText: this.translateService.instant('INVESTMENT_FUNDS.MEETING.ADD_NOTE'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL')
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        this.addMeetingNote(result.value);
      }
    });
  }

  /**
   * Load meeting notes from API
   */
  loadMeetingNotes(): void {
    if (!this.meetingId || this.isLoadingNotes) {
      return;
    }

    this.isLoadingNotes = true;

    // Call API with optional parameters (attendeeId, limit, offset)
    this.meetingServiceProxy.meetingNotesGet(this.meetingId, undefined, undefined, undefined)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: MeetingNotesResponseDtoBaseResponse) => {
          this.isLoadingNotes = false;
          if (response.successed && response.data) {
            this.meetingNotesData = response.data;
            this.meetingNotes = this.convertToExtendedNotes(response.data.notes || []);
          } else {
            console.error('Failed to load meeting notes:', response.message);
            this.meetingNotes = [];
            this.meetingNotesData = null;
          }
        } 
      });
  }

  /**
   * Add a new meeting note
   */
  addMeetingNote(noteContent: string, parentNoteId?: number): void {
    if (!this.meetingId || !noteContent.trim() || this.isAddingNote) {
      return;
    }

    this.isAddingNote = true;

    const request = new AddMeetingNoteRequest();
    request.meetingId = this.meetingId;
    request.note = noteContent.trim();
    request.parentNoteId = parentNoteId;

    this.meetingServiceProxy.meetingNotesPost(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: MeetingNoteDtoBaseResponse) => {
          this.isAddingNote = false;
          if (response.successed) {
            // Show different success message based on whether it's a note or reply
            const messageKey = parentNoteId
              ? 'INVESTMENT_FUNDS.MEETING.REPLY_ADDED_SUCCESS'
              : 'INVESTMENT_FUNDS.MEETING.NOTE_ADDED_SUCCESS';

            this.errorModalService.showSuccess(
              this.translateService.instant(messageKey)
            );
            // Reload notes to get updated list
            this.loadMeetingNotes();
          } else {
            this.errorModalService.showError(
              this.translateService.instant(response.message || 'INVESTMENT_FUNDS.MEETING.NOTE_REPLY_SAVE_ERROR')
            );
          }
        },
        error: (error) => {
          this.isAddingNote = false;error.message
          console.error('Error adding meeting note:', error);
          this.errorModalService.showError(error.parsedMessage
          );
        }
      });
  }

  /**
   * Check if user can add notes
   */
  showNotesSection(): boolean {
    return this.meetingNotesData?.meetingStatusId!=2 && this.meetingNotesData?.meetingStatusId!=5;
  }

  showMinutesSection(): boolean {
    return (this.meetingMinutesStatus ==2|| (this.meetingMinutesStatus ==1 && this.tokenService.getuserId() == this.meetingData?.createdBy.toString()));
     }

  canAddNotes(): boolean {
    return this.meetingNotesData?.canAddNotes || false;
  }

  /**
   * Check if user can reply to notes
   */
  canReplyToNotes(): boolean {
    return this.meetingNotesData?.canReplyToNotes || false;
  }

  /**
   * Check if notes are enabled for this meeting
   */
  areNotesEnabled(): boolean {
    return this.meetingNotesData?.isNotesEnabled || false;
  }

  /**
   * Convert API notes to extended notes with UI properties
   */
  private convertToExtendedNotes(notes: MeetingNoteDto[]): ExtendedMeetingNoteDto[] {
    return notes.map(note => {
      const extendedNote = note as ExtendedMeetingNoteDto;
      extendedNote.showReplyForm = false;
      extendedNote.replyText = '';
      extendedNote.replies = note.replies ? this.convertToExtendedNotes(note.replies) : [];
      return extendedNote;
    });
  }
    getRoleName(key:any): string{
   return this.translateService.instant(key.toLowerCase());
  }
}
