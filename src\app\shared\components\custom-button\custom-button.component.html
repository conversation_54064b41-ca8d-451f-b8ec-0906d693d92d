<button type="button" [class]="'w-100 font-size-m ' + class" [ngClass]="{
    'btn': true,
    'login-btn': buttonType === ButtonTypeEnum.Login,
    'primary-btn': buttonType === ButtonTypeEnum.Primary,
    'secondary-btn': buttonType === ButtonTypeEnum.Secondary,
    'danger-btn': buttonType === ButtonTypeEnum.Danger,
    'outline-btn': buttonType === ButtonTypeEnum.OutLine,
    'success-btn': buttonType === ButtonTypeEnum.Success
  }"  (click)="action($event)"
  [ngStyle]="{cursor:disabled?'not-allowed':'pointer'}"
  [disabled]="disabled"
  [title]="btnName"
>
  <img *ngIf="iconName"  [src]="iconName"  class="mx-1 {{imgStyle}} {{iconName ==IconEnum.arrowNavyRight?'rotate-icon':''}}"

  alt="icon" />
  <span>
    {{ btnName}}
  </span>
</button>
