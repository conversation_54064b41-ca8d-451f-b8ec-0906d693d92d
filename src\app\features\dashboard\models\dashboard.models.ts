// Dashboard Models and Interfaces
// These interfaces extend the generated API models with additional properties for UI

import {
  FundSummaryStatisticsDto,
  FundResolutionAnalyticsDto,
  FundAssessmentAnalyticsDto,
  FundDocumentAnalyticsDto
} from '@core/api/api.generated';

/**
 * Extended dashboard response with UI-specific properties
 */
export interface DashboardViewModel {
  // Base properties from API
  userName?: string;
  // Analytics properties from API
  fundSummaryStatistics?: FundSummaryStatisticsDto;
  resolutionAnalytics?: FundResolutionAnalyticsDto;
  assessmentAnalytics?: FundAssessmentAnalyticsDto;
  documentAnalytics?: FundDocumentAnalyticsDto;

}
