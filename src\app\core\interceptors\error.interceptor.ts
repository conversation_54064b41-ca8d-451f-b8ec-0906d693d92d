import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { ErrorModalService } from '@core/services/error-modal.service';
import { throwError, from, EMPTY } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const errorModalService = inject(ErrorModalService);
  const router = inject(Router);

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      console.log('error', error);

      const browserLang = localStorage.getItem('lang') || navigator.language ;


      // Skip error modal for 401 errors - they are handled by token interceptor
      if (error.status === 401) {
         if (error?.url?.includes('Sign-Out')) {
          router.navigate(['/auth/login']);
          return EMPTY;
         }
         else{
          router.navigate(['/admin/unauthorized']);
         return EMPTY;
         }
        }


      const getDefaultMessage = () => {
        return browserLang.startsWith('ar')
          ? 'حدث خطأ بالنظام , لم يتمكن من عرض البيانات'
          : 'An error is occurred, can’t display data';
      };

      if (
        error.error instanceof Blob &&
        error.error.type === 'application/json'
      ) {
        // 💡 Convert Blob to JSON
        return from(error.error.text()).pipe(
          switchMap((text: string) => {
            try {
              const parsedError = JSON.parse(text);
              let message =
                parsedError.message ||
                parsedError.error_description ||
                parsedError.title ||
                parsedError.detail ||
                getDefaultMessage();

              // Handle validation errors array
              if (
                Array.isArray(parsedError.Errors) &&
                parsedError.Errors.length > 0
              ) {
                message = parsedError.Errors.map((e: any) => {
                  const prop = e.propertyName || e.PropertyName || '';
                  const msg = e.errorMessage || e.ErrorMessage || '';
                  return `${prop}: ${msg}`;
                }).join('\n');
              }

              const enrichedError = {
                ...error,
                parsedMessage: message,
                responseBody: parsedError,
              };
              errorModalService.showError(message);

              return throwError(() => enrichedError);
            } catch (parseError) {
              console.error(
                'Failed to parse error response as JSON:',
                parseError
              );
              const message = getDefaultMessage();
              const enrichedError = {
                ...error,
                parsedMessage: message,
                responseBody: text,
              };
              errorModalService.showError(enrichedError.parsedMessage);
              return throwError(() => enrichedError);
            }
          })
        );
      }

      // ✅ Normal JSON or string error
      let message = getDefaultMessage();
      const responseBody = error.error;

      if (responseBody) {
        if (typeof responseBody === 'string') {
          message = responseBody;
        } else if (typeof responseBody === 'object') {
          message =
            responseBody.message ||
            responseBody.error_description ||
            responseBody.title ||
            responseBody.detail ||
            getDefaultMessage();

          // Handle validation errors array
          if (
            Array.isArray(responseBody.Errors) &&
            responseBody.Errors.length > 0
          ) {
            message = responseBody.Errors.map((e: any) => {
              const prop = e.propertyName || e.PropertyName || '';
              const msg = e.errorMessage || e.ErrorMessage || '';
              return `${prop} ${msg}`;
            }).join('\n');
          }
        }
      }

      if (error?.url?.includes("/MinIO/MinIOFile/Preview")
        && error?.error?.message?.includes("Error accessing the database")) {
          message= browserLang.includes('ar')
          ? 'فشل تنزيل الملف. يرجى التحقق من اتصالك والمحاولة مرة أخرى.'
          : 'File download failed. Please check your connection and try again.';

      }
      const enrichedError = {
        ...error,
        parsedMessage: message,
        responseBody,
      };
      errorModalService.showError(message);

      return throwError(() => enrichedError);
    })
  );
};
