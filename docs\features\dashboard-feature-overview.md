# 📊 Dashboard Feature Overview

## 🎯 Overview

The Dashboard feature provides a comprehensive, role-based overview of the Jadwa Investment Web Application. It displays key performance indicators (KPIs), recent activities, notifications, and quick actions tailored to each user's role and permissions.

## ✨ Key Features

### 🔑 Role-Based Dashboards
- **Fund Manager Dashboard**: Fund performance metrics, pending approvals, managed funds overview
- **Legal Council Dashboard**: Compliance status, document reviews, regulatory updates
- **Board Secretary Dashboard**: Meeting management, resolution tracking, administrative tasks
- **Board Member Dashboard**: Voting status, fund summaries, meeting notifications

### 📈 Key Performance Indicators (KPIs)
- **Fund Metrics**: Total funds, active funds, fund performance
- **Resolution Metrics**: Total resolutions, pending resolutions, approval rates
- **Member Metrics**: Total members, active members, participation rates
- **Meeting Metrics**: Total meetings, upcoming meetings, attendance rates
- **Assessment Metrics**: Total assessments, pending assessments, completion rates

### 🔔 Real-Time Information
- **Recent Activities**: Latest system activities with timestamps and user attribution
- **Recent Notifications**: Unread and recent notifications with priority indicators
- **Quick Actions**: Role-specific shortcuts to common tasks

### 🎨 User Experience Features
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA attributes
- **Internationalization**: Full Arabic (RTL) and English (LTR) support
- **Real-Time Updates**: Auto-refresh capabilities with manual refresh option
- **Loading States**: Smooth loading indicators and skeleton screens
- **Error Handling**: Graceful error handling with user-friendly messages

## 🏗️ Architecture

### 📁 File Structure
```
src/app/features/dashboard/
├── components/
│   ├── dashboard.component.ts          # Main dashboard component
│   ├── dashboard.component.html        # Dashboard template
│   ├── dashboard.component.scss        # Dashboard styles
│   └── dashboard.component.spec.ts     # Dashboard tests
├── box-info-card/
│   ├── box-info-card.component.ts      # KPI card component
│   ├── box-info-card.component.html    # KPI card template
│   ├── box-info-card.component.scss    # KPI card styles
│   └── box-info-card.component.spec.ts # KPI card tests
├── voting-card/
│   ├── voting-card.component.ts        # Voting widget component
│   ├── voting-card.component.html      # Voting widget template
│   └── voting-card.component.scss      # Voting widget styles
├── services/
│   ├── dashboard.service.ts            # Dashboard API service
│   ├── dashboard.service.spec.ts       # Dashboard service tests
│   ├── dashboard-utils.service.ts      # Dashboard utilities
│   └── dashboard-utils.service.spec.ts # Utils service tests
└── models/
    └── dashboard.models.ts             # Dashboard interfaces and types
```

### 🔄 Data Flow
1. **Component Initialization**: Dashboard component loads on route activation
2. **API Request**: Service calls auto-dashboard endpoint based on user role
3. **Data Processing**: Utils service enhances raw data with UI properties
4. **State Management**: Component manages loading, error, and data states
5. **Template Rendering**: Angular renders dashboard with enhanced data
6. **User Interaction**: Users can refresh data and interact with widgets

### 🎯 Component Hierarchy
```
DashboardComponent
├── PageHeaderComponent (shared)
├── BreadcrumbComponent (shared)
├── AlertComponent (shared)
├── BoxInfoCardComponent (multiple instances)
└── VotingCardComponent (role-dependent)
```

## 🔌 API Integration

### 📡 Available Endpoints
- `GET /api/Dashboard/AutoDashboard` - Auto-detects user role and returns appropriate dashboard
- `GET /api/Dashboard/FundManagerDashboard` - Fund Manager specific dashboard
- `GET /api/Dashboard/LegalCouncilDashboard` - Legal Council specific dashboard
- `GET /api/Dashboard/BoardSecretaryDashboard` - Board Secretary specific dashboard
- `GET /api/Dashboard/BoardMemberDashboard` - Board Member specific dashboard
- `GET /api/Dashboard/DashboardKPIs` - Key Performance Indicators
- `GET /api/Dashboard/RecentNotifications` - Recent notifications
- `GET /api/Dashboard/RecentActivities` - Recent activities

### 🔒 Authentication & Authorization
- All endpoints require valid JWT authentication
- Role-based access control enforced at API level
- Dashboard data filtered based on user permissions

## 🎨 UI Components

### 📊 BoxInfoCardComponent
**Purpose**: Displays KPI metrics in an attractive card format

**Inputs**:
- `title: string` - Card title (translation key)
- `value: number | string` - Main metric value
- `icon: string` - FontAwesome icon class
- `color: 'primary' | 'success' | 'info' | 'warning' | 'danger'` - Color theme
- `subtitle?: string` - Optional subtitle
- `trend?: 'up' | 'down' | 'neutral'` - Trend indicator
- `trendValue?: string` - Trend value (e.g., "+5%")

**Features**:
- Hover animations and focus states
- Accessibility attributes (ARIA labels, roles)
- Responsive design with mobile optimization
- RTL/LTR support

### 🗳️ VotingCardComponent
**Purpose**: Displays voting-related information for board members

**Features**:
- Shows remaining votes
- User profile integration
- Vote status indicators

## 🌍 Internationalization

### 🔤 Translation Keys
All dashboard text uses translation keys from `src/assets/i18n/`:

```json
{
  "DASHBOARD": {
    "TITLE": "Dashboard",
    "WELCOME": "Welcome",
    "ROLE": "Role",
    "LAST_UPDATED": "Last Updated",
    "RECENT_ACTIVITIES": "Recent Activities",
    "RECENT_NOTIFICATIONS": "Recent Notifications",
    "KPI": {
      "TOTAL_FUNDS": "Total Funds",
      "ACTIVE_FUNDS": "Active Funds",
      "TOTAL_RESOLUTIONS": "Total Resolutions",
      "PENDING_RESOLUTIONS": "Pending Resolutions"
    }
  }
}
```

### 🌐 RTL Support
- Proper text direction handling
- Mirrored layouts for Arabic
- Icon and spacing adjustments
- Date and number formatting

## ♿ Accessibility Features

### 🎯 WCAG 2.1 AA Compliance
- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Screen Reader Support**: Proper ARIA labels, roles, and descriptions
- **Focus Management**: Visible focus indicators and logical tab order
- **Color Contrast**: Meets minimum contrast ratios
- **Semantic HTML**: Proper heading hierarchy and landmark roles

### 🔍 Accessibility Attributes
- `role="region"` for dashboard sections
- `aria-label` for interactive elements
- `aria-describedby` for additional context
- `aria-live` for dynamic content updates
- `tabindex="0"` for focusable cards

## 🧪 Testing Strategy

### 🔬 Unit Tests
- **Component Tests**: Dashboard component behavior and rendering
- **Service Tests**: API integration and data processing
- **Utility Tests**: Data transformation and formatting functions
- **Coverage**: >90% code coverage maintained

### 🎭 Test Categories
- **Initialization**: Component setup and default states
- **Data Loading**: API calls and response handling
- **Error Handling**: Network errors and API failures
- **User Interactions**: Refresh, navigation, and accessibility
- **Responsive Design**: Mobile and desktop layouts

## 🚀 Performance Optimizations

### ⚡ Loading Performance
- **Lazy Loading**: Dashboard loaded on-demand via route
- **Code Splitting**: Separate bundle for dashboard feature
- **API Caching**: ShareReplay for repeated requests
- **Change Detection**: OnPush strategy where applicable

### 🎯 Runtime Performance
- **TrackBy Functions**: Efficient list rendering
- **Debounced Updates**: Throttled refresh operations
- **Memory Management**: Proper subscription cleanup
- **Bundle Size**: Optimized imports and tree shaking

## 🔧 Configuration

### ⚙️ Default Settings
```typescript
export const DEFAULT_DASHBOARD_CONFIG = {
  refreshInterval: 5, // minutes
  autoRefresh: false,
  defaultDateRange: 30, // days
  itemsPerSection: 10,
  theme: 'auto'
};
```

### 🎛️ Customization Options
- **Layout Configuration**: Show/hide sections
- **Refresh Settings**: Auto-refresh intervals
- **Data Filters**: Date ranges and item limits
- **Theme Preferences**: Light/dark/auto themes

## 🔮 Future Enhancements

### 📋 Planned Features
- **Dashboard Customization**: Drag-and-drop widget arrangement
- **Advanced Filters**: Custom date ranges and data filtering
- **Export Functionality**: PDF/Excel export of dashboard data
- **Real-Time Updates**: WebSocket integration for live data
- **Mobile App**: Native mobile dashboard experience
- **Analytics**: Dashboard usage tracking and insights

### 🎯 Technical Improvements
- **Progressive Web App**: Offline capability and caching
- **Advanced Caching**: Redis integration for better performance
- **Micro-Frontends**: Modular dashboard architecture
- **GraphQL**: More efficient data fetching
- **AI Insights**: Machine learning-powered recommendations
