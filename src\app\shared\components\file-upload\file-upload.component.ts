import {
  Component,
  Output,
  EventEmitter,
  Input,
  ViewChild,
  ElementRef,
  OnInit,
  ChangeDetectorRef,
  SimpleChanges,
  Inject,
  Optional,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from 'src/app/features/auth/services/auth-service/auth.service';
import { ControlValueAccessor } from '@angular/forms';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import {
  API_BASE_URL,
  FileManagmentServiceProxy,
} from '@core/api/api.generated';
import { FilePreviewService } from '@core/services/file-preview.service';
import { FileUploadService } from '@shared/services/file.service';
import { AttachmentModule } from '@shared/enum/AttachmentModule';

@Component({
  selector: 'app-file-upload',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  providers: [FileUploadService],
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.scss'],
})
export class FileUploadComponent implements OnInit, ControlValueAccessor {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @Input() required: boolean = false;
  @Input() formSubmitted: boolean = false;
  @Input() initialFiles: any[] = [];
  @Input() moduleId: AttachmentModule | undefined;
  @Input() allowedTypes: string[] = ['pdf'];
  @Input() multiple: boolean = false;
  @Input() maxLength: number = 10;
  @Input() maxSize: number = 5;
  @Input() controlName: string | null = null;
  selectedFile: File | null = null;
  selectedFiles: File[] = [];
  uploadedFiles: any[] = []; // Store uploaded file data with URLs
  error: string | null = null;
  @Output() fileUploaded = new EventEmitter<File | File[] | null>();

  file: any;
  fileName: string | null = null;
  isDisabled: boolean = false;
  translatedLabel: string = '';
  errorMessage: string = ''; // ✅ Stores validation errors

  private onChange: (value: File | File[] | null) => void = () => {};
  private onTouched: () => void = () => {};
  fileData: any;
  baseUrl: string;
  fileUrl: any;

  constructor(
    private translate: TranslateService,
    private cdr: ChangeDetectorRef,
    private apiClient: FileManagmentServiceProxy,
    private FileUploadService: FileUploadService,
    private filePreviewService: FilePreviewService,
    @Optional() @Inject(API_BASE_URL) baseUrl?: string
  ) {
    this.baseUrl = baseUrl ?? '';
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['initialFiles'] && this.initialFiles?.length > 0) {
      if (this.multiple) {
        this.uploadedFiles = this.initialFiles;
        this.selectedFiles = this.uploadedFiles;
        this.fileName = `${this.uploadedFiles.length} ملفات`;
        this.fileUrl =
          this.uploadedFiles[0]?.url || this.uploadedFiles[0]?.filePath || '';
      } else {
        const file = this.initialFiles[0];
        this.uploadedFiles = [file];
        this.selectedFile = file;
        this.fileName = file?.fileName || '';
        this.fileUrl = file?.url || file?.filePath || '';
      }
    }

    // if (changes['fileUrl'] && changes['fileUrl'].currentValue && !this.fileName) {
    //   this.buildDownloadUrl(this.fileUrl!);
    // }
  }

  writeValue(value: File | File[] | null): void {}

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  /** ✅ Handles file selection */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      if (this.multiple) {
        this.handleMultipleFiles(Array.from(input.files));
      } else {
        this.handleFile(input.files[0]);
      }
    } else {
      // User canceled file selection - clear any errors and reset state
      this.clearAllFiles();
      this.resetErrors();
    }
    this.onTouched();

    // Reset the input value to allow selecting the same file again
    input.value = '';
  }

  private handleFile(file: File): void {
    this.resetErrors(); // Clear any previous errors

    if (!this.validateFile(file)) {
      return;
    }

    this.selectedFile = file;
    this.selectedFiles = [file];
    this.onChange(file); // Update form control
    this.getFileUrl();
  }

  private handleMultipleFiles(files: File[]): void {
    this.resetErrors(); // Clear any previous errors

    // ✅ Total files after adding new ones
    const totalFilesCount = this.selectedFiles.length + files.length;

    if (this.maxLength && totalFilesCount > this.maxLength) {
      this.error = this.translate.instant('FORM.MAX_FILES_EXCEEDED', {
        max: this.maxLength,
      });
      return;
    }

    const validFiles: File[] = [];

    for (const file of files) {
      const fileExists = this.selectedFiles.some(
        (existingFile) =>
          existingFile.name === file.name && existingFile.size === file.size
      );

      if (!fileExists && this.validateFile(file)) {
        validFiles.push(file);
      } else if (fileExists) {
        console.warn(`File ${file.name} already selected`);
      } else {
        return; // Stop on invalid file
      }
    }

    this.selectedFiles = [...this.selectedFiles, ...validFiles];
    this.selectedFile = this.selectedFiles[0] || null;
    this.onChange(this.selectedFiles);
    this.getMultipleFileUrls();
  }

  private validateFile(file: File): boolean {
    // Check extension - handle both with and without dots
    const ext = file.name.split('.').pop()?.toLowerCase();
    if (ext) {
      const isAllowed = this.allowedTypes.some((type) => {
        const cleanType = type.startsWith('.') ? type.substring(1) : type;
        return cleanType.toLowerCase() === ext;
      });

      if (!isAllowed) {
        this.error = this.translate.instant('FILE_UPLOAD.FILE_NOT_SUPPORTED');
        return false;
      }
    } else {
      this.error = this.translate.instant('FILE_UPLOAD.FILE_NOT_SUPPORTED');
      return false;
    }

    // Check size
    if (file.size > this.maxSize * 1024 * 1024) {
      this.error = this.translate.instant('FILE_UPLOAD.FILE_TOO_LARGE', {
        maxSize: this.maxSize,
      });
      return false;
    }

    return true;
  }

  getAcceptAttribute(): string {
    return this.allowedTypes
      .map((type) => (type.startsWith('.') ? type : `.${type}`))
      .join(',');
  }

  getSupportedFormatsText(): string {
    return this.allowedTypes
      .map((type) => (type.startsWith('.') ? type : `.${type}`))
      .join(', ');
  }

  buildDownloadUrl(path: string) {
    const fileName = path.split(/[/\\]/).pop();
    this.fileName = `${fileName}`;
  }

  getFileUrl() {
    if (this.selectedFile)
      this.FileUploadService.uploadFileToMinIO(
        this.selectedFile,
        this.moduleId || AttachmentModule.Other
      ).subscribe({
        next: (response: any) => {
          this.uploadedFiles = [response.data];
          this.fileUploaded.emit(response.data);
          this.fileName = response.data.fileName;
          this.fileUrl = response.data.url;
        },
        error: () => {
        this.selectedFile = null;          // this.fileName = null;
          // this.fileUrl = '';
          // this.uploadedFiles = [];
        },
      });
  }

  getMultipleFileUrls() {
    if (this.selectedFiles.length === 0) return;

    // Find files that haven't been uploaded yet
    const filesToUpload = this.selectedFiles.filter(
      (file, index) =>
        !this.uploadedFiles[index] ||
        this.uploadedFiles[index].fileName !== file.name
    );

    if (filesToUpload.length === 0) return;

    if (filesToUpload.length === 1 && this.selectedFiles.length === 1) {
      // Handle single file case
      this.getFileUrl();
      return;
    }

    // Handle multiple files - upload sequentially to avoid overwhelming the server
    this.uploadFilesSequentially(filesToUpload, 0, [...this.uploadedFiles]);
  }

  private uploadFilesSequentially(
    files: File[] | any[],
    index: number,
    uploadedFiles: any[]
  ): void {
    if (index >= files.length) {
      // All files uploaded successfully
      this.uploadedFiles = uploadedFiles;
      this.fileUploaded.emit(uploadedFiles);
      this.fileName = `${uploadedFiles.length} files uploaded`;
      this.fileUrl = uploadedFiles[0]?.url || '';
      return;
    }
    if (files[index].id) {
      this.uploadFilesSequentially(files, index + 1, uploadedFiles);
      return;
    }
    this.FileUploadService.uploadFileToMinIO(
      files[index],
      this.moduleId || AttachmentModule.Other
    ).subscribe({
      next: (response: any) => {
        uploadedFiles.push(response.data);
        this.uploadFilesSequentially(files, index + 1, uploadedFiles);
      },
      error: () => {
        this.uploadedFiles.pop();
        this.error = this.translate.instant('FILE_UPLOAD.UPLOAD_FAILED');
      },
    });
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
  }

  /** ✅ Handles file drop */
  onDrop(event: DragEvent): void {
    if (this.selectedFile || this.fileUrl) return;
    event.preventDefault();
    if (event.dataTransfer?.files.length) {
      if (this.multiple) {
        this.handleMultipleFiles(Array.from(event.dataTransfer.files));
      } else {
        this.handleFile(event.dataTransfer.files[0]);
      }
    } else {
      this.clearAllFiles();
    }
    this.onTouched();
  }

  clearFile(): void {
    this.selectedFile = null;
    this.selectedFiles = [];
    this.uploadedFiles = [];
    this.fileUrl = '';
    this.fileName = null;
    this.resetErrors(); // Clear any validation errors
    this.onChange(null); // Update form control
    this.fileUploaded.emit(null);
  }

  clearAllFiles(): void {
    this.selectedFile = null;
    this.selectedFiles = [];
    this.uploadedFiles = [];
    this.file = null;
    this.fileName = null;
    this.fileUrl = '';
    this.resetErrors();
    this.onChange(null); // Update form control

    // Emit null to notify parent that all files are cleared
    this.fileUploaded.emit(null);
  }

  resetErrors(): void {
    this.error = null;
    this.errorMessage = '';
  }

  hasUploadedFiles(): boolean {
    if (this.multiple) {
      return this.selectedFiles.length > 0 || this.uploadedFiles.length > 0;
    }
    return !!(this.selectedFile || this.fileUrl);
  }

  canUploadFiles(): boolean {
    // Allow upload if there are no validation errors or if multiple files are allowed
    return !this.error || this.multiple;
  }

  clearAllMultipleFiles(): void {
    if (this.multiple) {
      this.selectedFiles = [];
      this.uploadedFiles = [];
      this.selectedFile = null;
      this.resetErrors();
      this.onChange(null);
      this.fileUploaded.emit(null);
    }
  }

  removeFile(index?: number): void {
    if (this.multiple) {
      if (index !== undefined && this.uploadedFiles.length > index) {
        this.uploadedFiles.splice(index, 1);
        this.selectedFiles.splice(index, 1);

        // ✅ Emit updated files array to parent
        this.fileUploaded.emit(
          this.uploadedFiles.length > 0 ? this.uploadedFiles : null
        );
        this.onChange(
          this.selectedFiles.length > 0 ? this.selectedFiles : null
        );

        // Clear errors if no files remain
        if (this.uploadedFiles.length === 0) {
          this.resetErrors();
        }
      }
    } else {
      this.fileUrl = '';
      this.fileName = '';
      this.fileData = null;
      this.selectedFile = null;
      this.uploadedFiles = [];
      this.resetErrors(); // Clear any validation errors

      // ✅ Emit null to indicate file cleared
      this.fileUploaded.emit(null);
      this.onChange(null);
    }
  }

  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  downloadFile(fileUrl?: string, fileName?: string): void {
    debugger
    // 1️⃣ Case: Local selected file (before upload)
    if (this.selectedFile && !fileUrl && !this.fileUrl) {
      const url = URL.createObjectURL(this.selectedFile);
      const a = document.createElement('a');
      a.href = url;
      a.download = this.selectedFile.name;
      a.click();
      URL.revokeObjectURL(url);
      return;
    }

    // 2️⃣ Case: Backend file (from input or stored fileUrl)
    const rawUrl = fileUrl || this.fileUrl;

    if (!rawUrl) return;

    const isAbsolute = rawUrl.startsWith('http') || rawUrl.startsWith('blob:');
    const fullUrl = isAbsolute ? rawUrl : `${this.baseUrl}${rawUrl}`;
    const finalName = fileName || this.fileName || 'downloaded-file';

    const a = document.createElement('a');
    a.href = fullUrl;
    a.download = finalName;
    a.target = '_blank';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }

  /**
   * Preview file using the FilePreviewService
   * Extracts attachment ID from uploaded file and opens preview in new window
   */
  previewFile(file?: any): void {
    // If no file provided, try to get from single file context
    if (!file && this.uploadedFiles.length > 0) {
      file = this.uploadedFiles[0];
    }

    // Use the service to handle preview with fallback to download
    this.filePreviewService.previewFile(file, () => {
      // Fallback to download if preview fails
      //this.downloadFile(file?.url || file?.filePath, file?.fileName);
    });
  }
}
