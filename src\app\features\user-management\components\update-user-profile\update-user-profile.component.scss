@import '../../../../shared/styles/form-container';
@import '../../../../../assets/scss/variables';

.update-user-profile-page {
  padding: 0;

  .form-container {
    background-color: #f8fafc;
    border: 1px solid #EAEEF1;
    border-radius: 8px;
    padding: 24px;
    max-width: 100%;
    box-shadow: 0 2px 4px #0000001a;


      .profile-photo-section {
        border-bottom: 1px solid $border-color;
        padding-bottom: 24px;
        margin-bottom: 24px;

        .photo-container {
          position: relative;
          display: inline-block;

          .profile-photo-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid $navy-blue;
            overflow: hidden;
            position: relative;
            background: $border-color;
            display: flex;
            align-items: center;
            justify-content: center;

            .profile-photo {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: opacity 0.3s ease;

              &.loading {
                opacity: 0.5;
              }

              &.error {
                opacity: 0.8;
              }
            }
          }

          .photo-upload-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 36px;
            height: 36px;
            background: $navy-blue;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: 3px solid $white;

            i {
              color: $white;
              font-size: 14px;
            }

            &:hover {
              background: darken($navy-blue, 10%);
            }
          }
        }

        .photo-label {
          color: $dark-gray;
          font-size: 14px;
          margin: 0;
        }
      }

      .status-roles-section {
        border-bottom: 1px solid $border-color;
        padding-bottom: 24px;
        margin-bottom: 24px;

        .form-label {
          font-weight: 600;
          color: $dark-gray;
          font-size: 14px;
          margin-bottom: 8px;
          display: block;
        }

        .field-value {
          padding: 12px 16px;
          font-size: 14px;
          color: $text-color;
          min-height: 44px;
          display: flex;
          align-items: center;
          border-radius: 6px;
          .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;

            &.badge-success {
              background-color: #d4edda;
              color: #155724;
              border: 1px solid #c3e6cb;
            }

            &.badge-secondary {
              background-color: #f8f9fa;
              color: #6c757d;
              border: 1px solid #dee2e6;
            }
          }

          .roles-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .role-chip {
              background-color: #e3f2fd;
              color: #1976d2;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;
              border: 1px solid #bbdefb;
              display: inline-flex;
              align-items: center;

              .role-separator {
                display: none; // Hide separator since we're using chips
              }
            }
          }

          .no-data {
            color: $light-dark;
            font-style: italic;
          }
        }
      }

      .form-fields-section {
        .photo-upload-section {
          display: none; // Hide the form-builder file upload for photo since we have custom UI
        }
      }

      .change-password-section {
        border-top: 1px solid $border-color;
        padding-top: 24px;

        .change-password-link {
          color: $navy-blue;
          text-decoration: none;
          font-weight: 500;
          padding: 0;
          border: none;
          background: none;

          &:hover {
            color: darken($navy-blue, 10%);
            text-decoration: underline;
          }

          i {
            color: $navy-blue;
          }
        }
      }

      .actions {
        display: flex;
        gap: 12px;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid $border-color;

        app-custom-button {
          min-width: 120px;
        }
      }
    }
  }

  .loading-container {
    padding: 40px 20px;

    .spinner-border {
      width: 3rem;
      height: 3rem;
    }

    p {
      color: $dark-gray;
      margin-top: 16px;
      font-size: 14px;
    }
  }

  .error-container {
    padding: 40px 20px;

    .alert {
      max-width: 500px;
      margin: 0 auto 24px;
    }
  }


// RTL Support
[dir="rtl"] {
  .update-user-profile-page {
    .form-container {
      form {
        .profile-photo-section {
          .photo-container {
            .photo-upload-overlay {
              left: 0;
              right: auto;
            }
          }
        }

        .change-password-section {
          .change-password-link {
            i {
              margin-left: 8px;
              margin-right: 0;
            }
          }
        }

        .status-roles-section {
          .field-value {
            text-align: right;

            .badge {
              margin-left: 0;
              margin-right: auto;
            }

            .roles-container {
              justify-content: flex-end;

              .role-chip {
                text-align: right;
              }
            }
          }
        }

        .actions {
          &.justify-content-end {
            justify-content: flex-start;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .update-user-profile-page {
    .form-container {
      padding: 16px;

      form {
        .profile-photo-section {
          .photo-container {
            .profile-photo-circle {
              width: 100px;
              height: 100px;
            }

            .photo-upload-overlay {
              width: 32px;
              height: 32px;

              i {
                font-size: 12px;
              }
            }
          }
        }

        .actions {
          flex-direction: column;

          app-custom-button {
            width: 100%;
          }
        }
      }
    }
  }
}
