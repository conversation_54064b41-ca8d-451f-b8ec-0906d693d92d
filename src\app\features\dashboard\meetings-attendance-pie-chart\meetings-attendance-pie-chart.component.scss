// Meetings Attendance Pie Chart Styles
// Responsive pie chart with attendance/absent visualization

.meetings-attendance-pie-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  // Loading State
  .chart-loading {
    .spinner-border {
      width: 2rem;
      height: 2rem;
    }
  }

  // Empty State
  .chart-empty {
    i {
      opacity: 0.5;
    }

    h6, p {
      margin: 0;
    }
  }

  // Chart Container
  .chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    // Chart Header
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 16px;
      padding: 0 8px;
      margin-bottom: 16px;

      .chart-title-section {
        flex: 1;
        min-width: 200px;

        .chart-title {
          font-size: 1rem;
          font-weight: 600;
          color: #2c3e50;
          margin: 0 0 8px 0;
        }

        .chart-summary {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .summary-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.85rem;

            .summary-label {
              color: #6c757d;
              white-space: nowrap;
            }

            .summary-value {
              font-weight: 700;
              color: #2c3e50;
              background: #f8f9fa;
              padding: 2px 6px;
              border-radius: 3px;
            }
          }
        }
      }

      // Meeting Type Dropdown
      .meeting-type-dropdown {
        min-width: 180px;

        .custom-select {
          ::ng-deep {
            .ng-select {
              min-height: 32px;
              font-size: 0.85rem;

              .ng-select-container {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: #ffffff;

                .ng-value-container {
                  padding: 4px 8px;

                  .ng-placeholder {
                    color: #6c757d;
                  }
                }

                .ng-arrow-wrapper {
                  width: 20px;
                  padding-right: 8px;
                }
              }

              &.ng-select-focused .ng-select-container {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
              }
            }

            .ng-dropdown-panel {
              border: 1px solid #dee2e6;
              border-radius: 4px;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

              .ng-option {
                padding: 8px 12px;
                font-size: 0.85rem;

                &.ng-option-highlighted {
                  background-color: #f8f9fa;
                  color: #2c3e50;
                }

                &.ng-option-selected {
                  background-color: #007bff;
                  color: white;
                }
              }
            }
          }
        }
      }
    }

    // Chart Wrapper
    .chart-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 16px;

      // NGX Charts Overrides
      ::ng-deep {
        .ngx-charts {
          .pie-chart {
            .pie-series {
              .pie-arc {
                stroke: #ffffff;
                stroke-width: 2px;
                transition: all 0.3s ease;

                &:hover {
                  opacity: 0.8;
                  transform: scale(1.05);
                }
              }
            }
          }

          .pie-label {
            font-size: 0.75rem;
            font-weight: 600;
            fill: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
          }
        }
      }
    }

    // See All Section
    .see-all-section {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;

      .see-all-link {
        color: #007bff;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: #0056b3;
          text-decoration: underline;
        }
      }
    }

    // Chart Legend
    .chart-legend {
      padding-top: 16px;
     // border-top: 1px solid #e9ecef;
     display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

      .legend-items {
        // display: flex;
        // flex-wrap: wrap;
        // gap: 16px;
        // justify-content: center;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 0.85rem;
          margin-bottom: 14px;
          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
          }

          .legend-text {
            color: #2c3e50;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .meetings-attendance-pie-chart {
    .chart-container {
      .chart-header {
        flex-direction: column;
        align-items: stretch;

        .chart-title-section {
          min-width: auto;
        }

        .meeting-type-dropdown {
          min-width: auto;
        }
      }

      .chart-legend {
        .legend-items {
          flex-direction: column;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .meetings-attendance-pie-chart {
    .chart-container {
      .chart-header {
        .meeting-type-dropdown {
          .custom-select {
            ::ng-deep {
              .ng-select {
                .ng-select-container {
                  .ng-arrow-wrapper {
                    padding-right: 0;
                    padding-left: 8px;
                  }
                }
              }
            }
          }
        }
      }

      .chart-legend {
        .legend-items {
          .legend-item {
            flex-direction: row-reverse;
          }
        }
      }
    }
  }
}

.donut-container {
  position: relative;
  display: inline-block;
}

.donut-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
   color: #00205A;
  font-size:  14px;
  font-weight: 400;
  line-height: 15px;
}
