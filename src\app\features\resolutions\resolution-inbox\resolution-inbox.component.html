<div class="resolutions-page">
  <!-- Breadcrumb -->
  <!-- <div class="breadcrumb-section">
    <app-breadcrumb (onClickEvent)="onBreadcrumbClicked($event)" [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium" divider=">">
    </app-breadcrumb>
  </div> -->
  <!-- Page Header -->
  <div class="page-header-section">
    <!-- <app-page-header title="INVESTMENT_FUNDS.TITLE" [showCreateButton]=" isHasPermissionAdd" [showSearch]="true" [showFilter]="true"
    createButtonText="INVESTMENT_FUNDS.CREATE_NEW_FUND" searchPlaceholder="INVESTMENT_FUNDS.SEARCH_PLACEHOLDER"
    (create)="addNewResolution()" (search)="onSearch($event)" (filter)="openFilter()"></app-page-header> -->


    <app-page-header [title]="'INVESTMENT_FUNDS.RESOLUTIONS.INBOX_TITLE' | translate" [showSearch]="true"
      [showFilter]="true"
      [showCreateButton]="false"
      [searchPlaceholder]="translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.SEARCH_PLACEHOLDER')"
      [restrictSearchToNumbers]="true"
      (search)="onSearch($event)"
      (filter)="openFilter()">
    </app-page-header>
  </div>

  <!-- Main Content -->
  <div class="content-section">
    <!-- Search and Filters Section -->


    <!-- Loading State -->
    <div class="loading-state" *ngIf="isLoading">
      <!-- <div class="loading-content">
        <i class="fas fa-spinner fa-spin loading-icon"></i>
        <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LOADING' | translate }}</p>
      </div> -->
    </div>

    <!-- Error State -->
    <!-- <div class="error-state" *ngIf="hasError && !isLoading">
      <div class="error-content"> -->
    <!-- <i class="fas fa-exclamation-triangle error-icon"></i> -->
    <!-- <h3>{{ 'COMMON.ERROR' | translate }}</h3> -->
    <!-- <p>{{ errorMessage | translate }}</p> -->
    <!-- <button class="btn btn-primary retry-btn" (click)="loadResolutions()">
          {{ 'COMMON.RETRY' | translate }}
        </button>
      </div>
    </div> -->


    <!-- new Resolutions Grid -->

     <div class="resolutions-grid" *ngIf="!isLoading && !hasError">
   <div class="resolution-card" *ngFor="let resolution of resolutions">
  <ng-container *ngIf="resolution.canView || resolution.canVote || resolution.canViewHisVote">

    <!-- Card Actions -->


    <!-- Card Header -->
    <div class="card-header mb-3">
      <div>
        <div class="">
          <span class="status" [ngClass]="getStatusClass(resolution.statusId)">
                     <span class="dot" [ngClass]="getStatusClass(resolution.statusId)"></span>

            {{ (resolution.resolutionStatus.localizedName || resolution.resolutionStatus.nameAr || resolution.resolutionStatus.nameEn || '') | translate }}
          </span>
        </div>
      </div>
<div class="d-flex align-items-center justify-content-center">
      <!-- Action Buttons -->



      <button class="action-btn details-btn"
              *ngIf="resolution.canView && !resolution.canViewHisVote"
              (click)="viewResolutionDetails(resolution)"
              [title]="'COMMON.VIEW_DETAILS' | translate">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.8335 12C2.8335 12 6.16683 5.33337 12.0002 5.33337C17.8335 5.33337 21.1668 12 21.1668 12C21.1668 12 17.8335 18.6667 12.0002 18.6667C6.16683 18.6667 2.8335 12 2.8335 12Z" stroke="#2F80ED" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M12.0002 14.5C13.3809 14.5 14.5002 13.3808 14.5002 12C14.5002 10.6193 13.3809 9.50004 12.0002 9.50004C10.6195 9.50004 9.50016 10.6193 9.50016 12C9.50016 13.3808 10.6195 14.5 12.0002 14.5Z" stroke="#2F80ED" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>

           <!-- Approve Button -->
                <button class="action-btn details-btn" *ngIf="resolution.canConfirm"
                  (click)="onConfirmResolution(resolution)"
                   [title]="'INVESTMENT_FUNDS.RESOLUTIONS.APPROVE' | translate">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6489 6.34652C19.8737 6.56868 20 6.86984 20 7.18383C20 7.49782 19.8737 7.79898 19.6489 8.02114L9.89031 17.6534C9.77765 17.7645 9.64373 17.8523 9.49634 17.9118C9.34895 17.9713 9.19103 18.0013 9.0318 18C8.87256 17.9986 8.71518 17.9661 8.56882 17.9041C8.42247 17.8422 8.29006 17.7521 8.17931 17.6391L4.33316 13.7148C4.11382 13.4886 3.99412 13.1859 4.00022 12.8728C4.00632 12.5598 4.13773 12.2618 4.36572 12.0441C4.59371 11.8263 4.89975 11.7065 5.21695 11.7109C5.53416 11.7152 5.83674 11.8432 6.05857 12.0671L9.05642 15.1256L17.9523 6.34652C18.1774 6.12463 18.4825 6 18.8006 6C19.1187 6 19.4239 6.12463 19.6489 6.34652Z" fill="#16A249"/>
        </svg>
                      </button>

                 <!-- Reject Button -->
                <button class="action-btn details-btn pt-2" *ngIf="resolution.canReject"
                  (click)="onRejectResolution(resolution)"
                   [title]="'INVESTMENT_FUNDS.RESOLUTIONS.REJECT' | translate">
           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M0.250744 0.261676C0.411343 0.100986 0.629134 0.0107158 0.856221 0.0107158C1.08331 0.0107158 1.3011 0.100986 1.4617 0.261676L5.99465 4.79853L10.5276 0.261676C10.6066 0.17981 10.7011 0.114511 10.8056 0.0695887C10.9101 0.0246667 11.0224 0.00102135 11.1362 3.23632e-05C11.2499 -0.000956622 11.3626 0.0207304 11.4679 0.0638283C11.5731 0.106926 11.6688 0.170572 11.7492 0.251051C11.8296 0.331531 11.8932 0.427232 11.9362 0.532572C11.9793 0.637912 12.001 0.75078 12 0.864591C11.999 0.978402 11.9754 1.09088 11.9305 1.19545C11.8856 1.30003 11.8203 1.39461 11.7386 1.47368L7.2056 6.01053L11.7386 10.5474C11.8946 10.709 11.9809 10.9256 11.9789 11.1503C11.977 11.375 11.8869 11.59 11.7281 11.7489C11.5693 11.9079 11.3545 11.998 11.13 12C10.9054 12.0019 10.6891 11.9155 10.5276 11.7594L5.99465 7.22253L1.4617 11.7594C1.30018 11.9155 1.08385 12.0019 0.859303 12C0.634757 11.998 0.41996 11.9079 0.261176 11.7489C0.102391 11.59 0.0123241 11.375 0.0103729 11.1503C0.00842164 10.9256 0.0947427 10.709 0.250744 10.5474L4.78369 6.01053L0.250744 1.47368C0.0901927 1.31294 0 1.09496 0 0.867676C0 0.640392 0.0901927 0.422414 0.250744 0.261676Z" fill="#C50F1F"/>
       </svg>
          </button>

      <button class="action-btn details-btn"
              *ngIf="resolution.canViewHisVote"
              (click)="viewResolutionVotingDetails(resolution)"
              [title]="'COMMON.Vote' | translate">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15.668 19.208C15.7895 19.2081 15.9063 19.2569 15.9922 19.3428C16.078 19.4287 16.126 19.5456 16.126 19.667C16.1259 19.7884 16.0781 19.9053 15.9922 19.9912C15.9063 20.0771 15.7894 20.1249 15.668 20.125H4.33301C4.21157 20.1249 4.09467 20.0771 4.00879 19.9912C3.92291 19.9053 3.87509 19.7884 3.875 19.667C3.875 19.5456 3.92305 19.4287 4.00879 19.3428C4.09467 19.2569 4.21157 19.2081 4.33301 19.208H15.668ZM15.2979 3.875C15.5959 3.87512 15.8818 3.99349 16.0928 4.2041L19.7969 7.9082C20.0076 8.11938 20.126 8.40575 20.126 8.7041C20.1258 9.00228 20.0075 9.28796 19.7969 9.49902L19.042 10.2539V10.2549C18.8295 10.4657 18.5473 10.5829 18.2471 10.583H18.2432C18.095 10.5829 17.9482 10.5533 17.8115 10.4961C17.7091 10.4532 17.6135 10.3949 17.5283 10.3242L17.4463 10.249L13.7637 6.52441L13.7627 6.52344C13.5547 6.31174 13.4385 6.02631 13.4395 5.72949C13.4405 5.43255 13.5589 5.14782 13.7686 4.9375L14.502 4.2041L14.585 4.12988C14.7852 3.96608 15.0369 3.875 15.2979 3.875ZM15.2559 4.79395C15.2426 4.79656 15.2294 4.80047 15.2168 4.80566C15.1916 4.81605 15.1687 4.83135 15.1494 4.85059L14.416 5.58398C14.3772 5.62305 14.3557 5.67635 14.3555 5.73145C14.3553 5.78606 14.3761 5.83869 14.4141 5.87793L18.0977 9.60352C18.1336 9.6397 18.1803 9.6597 18.2305 9.66309C18.2688 9.65387 18.2972 9.64915 18.3281 9.63867C18.3601 9.62785 18.3799 9.61634 18.3926 9.60352L19.1475 8.84961L19.1738 8.81738C19.1964 8.78332 19.208 8.74261 19.208 8.70117C19.2079 8.65992 19.1962 8.61986 19.1738 8.58594L19.1475 8.55371L15.4434 4.85059C15.4241 4.83144 15.4011 4.81599 15.376 4.80566C15.351 4.79545 15.3238 4.78998 15.2969 4.79004L15.2559 4.79395Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M17.6763 9.27441C17.7378 9.28493 17.7968 9.30778 17.8491 9.3418C17.9015 9.37584 17.9464 9.42066 17.981 9.47266C18.0155 9.52461 18.0391 9.58315 18.0503 9.64453C18.0614 9.706 18.0595 9.76922 18.0454 9.83008C18.0313 9.89093 18.0053 9.94875 17.9683 9.99902L17.9673 9.99805L15.6323 13.5977L15.6313 13.5967C15.6149 13.6227 15.5972 13.6479 15.5757 13.6699H15.5747L12.6548 16.6357V16.6367C12.5024 16.7904 12.3206 16.9122 12.1206 16.9951C11.9707 17.0572 11.8127 17.0969 11.6519 17.1123L11.4897 17.1191C10.9096 17.119 10.3851 16.8198 10.0874 16.3203C9.70451 15.6766 9.83115 14.8412 10.3813 14.2881L12.6499 12.0146C12.5386 11.9028 12.4085 11.8112 12.2632 11.748C12.1281 11.6893 11.9837 11.6551 11.8374 11.6465H11.689C11.4769 11.6571 11.2711 11.7216 11.0903 11.833C10.9096 11.9444 10.7596 12.0995 10.6548 12.2842L10.6499 12.292C10.5853 12.3886 10.4866 12.4577 10.3735 12.4844C10.2605 12.511 10.1415 12.493 10.0405 12.4355C9.93956 12.378 9.8633 12.2848 9.82861 12.1738C9.79395 12.0629 9.80282 11.9427 9.85303 11.8379L9.85693 11.8301C10.0379 11.5118 10.2962 11.2439 10.6079 11.0518C10.9194 10.8598 11.2747 10.7492 11.6401 10.7305C12.006 10.7106 12.3713 10.7839 12.7017 10.9424C13.032 11.1009 13.3173 11.3399 13.5308 11.6377L13.6343 11.7822L13.6753 11.8506C13.7097 11.9218 13.7247 12.0012 13.7192 12.0811C13.7119 12.1877 13.668 12.2889 13.5942 12.3662L13.5923 12.3682L11.0288 14.9336L11.0298 14.9346C10.7706 15.1956 10.7144 15.5833 10.8735 15.8506V15.8516C11.1037 16.2388 11.6875 16.3058 12.0044 15.9902L14.8921 13.0566L17.2046 9.49316C17.235 9.44171 17.275 9.39639 17.3228 9.36035C17.3726 9.32273 17.43 9.29602 17.4907 9.28125C17.5514 9.26648 17.6147 9.26392 17.6763 9.27441ZM14.3218 5.90918C14.3835 5.91579 14.4434 5.93444 14.4976 5.96484C14.5517 5.99525 14.5994 6.03665 14.6372 6.08594C14.6749 6.13516 14.702 6.19188 14.7173 6.25195C14.7325 6.31194 14.7356 6.37441 14.7261 6.43555C14.7164 6.4969 14.6937 6.55582 14.6606 6.6084C14.6276 6.66097 14.5848 6.7069 14.5337 6.74219C14.4841 6.77646 14.4276 6.79891 14.3687 6.81152L14.3696 6.8125L10.7231 7.73926H10.7222C10.2568 7.85709 9.82203 8.07347 9.44775 8.37402C9.07349 8.67457 8.76875 9.05256 8.55322 9.48145C8.17344 10.239 8.09737 11.1133 8.34131 11.9248V11.9258C8.3616 11.9939 8.36628 12.0657 8.354 12.1357C8.34162 12.206 8.31257 12.2728 8.27002 12.3301C8.22754 12.3872 8.17243 12.4337 8.10889 12.4658C8.04535 12.4978 7.97496 12.5144 7.90381 12.5146H7.90283C7.80442 12.5146 7.7084 12.4835 7.62939 12.4248C7.5503 12.366 7.49274 12.2828 7.46436 12.1885C7.3454 11.7931 7.28461 11.3826 7.28467 10.9697C7.28468 10.32 7.44032 9.66471 7.73193 9.07324L7.84033 8.87109C8.10515 8.40718 8.45506 7.99653 8.87256 7.66113C9.34973 7.27783 9.90417 7.00159 10.4976 6.85156L14.144 5.9248V5.92578C14.2017 5.90883 14.2619 5.90278 14.3218 5.90918Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M12.9819 11.874C13.1031 11.8741 13.2194 11.9222 13.3052 12.0078C13.3911 12.0937 13.4398 12.2106 13.4399 12.332C13.4399 12.4536 13.3911 12.5703 13.3052 12.6562C13.2193 12.742 13.1033 12.7909 12.9819 12.791H6.12549V19.208H13.8872V14.2676C13.8872 14.146 13.936 14.0293 14.022 13.9434C14.1078 13.8576 14.2239 13.8087 14.3452 13.8086C14.4668 13.8086 14.5835 13.8574 14.6694 13.9434C14.7554 14.0293 14.8042 14.146 14.8042 14.2676V19.666C14.8042 19.7876 14.7554 19.9043 14.6694 19.9902C14.5835 20.0762 14.4668 20.125 14.3452 20.125H5.6665C5.54507 20.1249 5.42816 20.0761 5.34229 19.9902C5.25656 19.9043 5.2085 19.7874 5.2085 19.666V12.332L5.21729 12.2422C5.23494 12.1542 5.2779 12.0722 5.34229 12.0078C5.42816 11.9219 5.54507 11.8741 5.6665 11.874H12.9819Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M10.5 12.0003C10.8333 11.667 11.8 11.2003 13 12.0003H10.5Z" fill="#D9D9D9"/>
          </svg>
          </button>


      <button class="action-btn"
             *ngIf="resolution?.canEdit && tokenService.hasPermission('Resolution.Edit')"
              (click)="editResolution(resolution)"
              [title]="'COMMON.EDIT' | translate">
           <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_18950_24919)">
          <path d="M0.998208 8.00503C0.998208 9.59092 1.00685 11.1773 0.995024 12.7632C0.98558 13.24 1.13265 13.7071 1.41436 14.0951C1.69608 14.483 2.0974 14.7711 2.55868 14.9165C2.80358 14.9864 3.05784 15.0193 3.31277 15.0141C4.08414 15.0141 4.85551 15.0141 5.62688 15.0141C5.93934 15.0141 6.11626 15.1301 6.18994 15.3724C6.20785 15.4339 6.21314 15.4983 6.20549 15.5618C6.19785 15.6254 6.17742 15.6867 6.14541 15.7424C6.1134 15.7981 6.07044 15.8469 6.01905 15.886C5.96766 15.9252 5.90886 15.9538 5.8461 15.9703C5.79693 15.9839 5.74617 15.9911 5.6951 15.9918C4.78228 15.9824 3.86856 16.0241 2.95665 15.9721C1.61403 15.8964 0.397394 14.8663 0.0958509 13.5675C0.0349554 13.3007 0.00444598 13.0281 0.00488754 12.7546C0.00488754 9.58257 0.00488754 6.41033 0.00488754 3.23795C0.00488754 1.67311 1.01458 0.427146 2.55413 0.0733319C2.80331 0.0214658 3.05768 -0.00227501 3.31231 0.00256908C5.58094 0.00256908 7.84957 0.00256908 10.1182 0.00256908C11.7169 0.00256908 12.9835 0.983842 13.3474 2.49494C13.398 2.70678 13.4239 2.9236 13.4247 3.14121C13.4296 4.05695 13.4296 4.97283 13.4247 5.88887C13.4247 6.27358 13.0863 6.51991 12.752 6.38689C12.5783 6.31747 12.4646 6.19118 12.4441 6.00039C12.4359 5.92246 12.4337 5.84453 12.4332 5.76615C12.4237 4.82832 12.4628 3.8896 12.4146 2.95221C12.3632 1.94989 11.4249 1.04072 10.4093 0.993696C10.0454 0.976677 9.68157 0.982499 9.32044 0.982499C7.30697 0.982499 5.2935 0.991456 3.28048 0.97802C2.15253 0.970855 1.29156 1.73267 1.0637 2.63512C1.01518 2.84741 0.993184 3.06475 0.998208 3.28229V8.00503Z" fill="#EAA300"/>
          <path d="M8.40247 15.9146C8.12958 15.9146 7.85669 15.9594 7.58744 15.9106C7.37036 15.8743 7.1728 15.7649 7.02847 15.6011C6.88415 15.4374 6.80201 15.2294 6.79606 15.0126C6.78969 14.588 6.79606 14.1617 6.80379 13.7393C6.80838 13.4959 6.91095 13.2642 7.08896 13.0953C9.10107 11.1145 11.1144 9.13288 13.1289 7.15033C13.2197 7.0592 13.328 6.98673 13.4474 6.93712C13.5669 6.88751 13.6951 6.86175 13.8248 6.86133C13.9544 6.86092 14.0828 6.88585 14.2026 6.93469C14.3224 6.98353 14.4311 7.0553 14.5225 7.14585C14.9242 7.53191 15.3211 7.92274 15.7132 8.31836C16.0943 8.70173 16.0984 9.26694 15.7323 9.6642C15.6786 9.72197 15.6213 9.77571 15.5649 9.83125C13.6441 11.7227 11.7243 13.6148 9.80558 15.5075C9.52314 15.7874 9.21432 15.9473 8.80999 15.916C8.67491 15.9057 8.53847 15.9146 8.40247 15.9146ZM7.77437 14.3587C7.77437 14.5208 7.77892 14.6825 7.77437 14.8442C7.77028 14.9369 7.79529 14.9727 7.89581 14.9696C8.21873 14.9589 8.5418 14.953 8.86502 14.9521C8.90401 14.9526 8.94266 14.9449 8.97835 14.9295C9.01405 14.914 9.04597 14.8912 9.07196 14.8626C11.0225 12.9379 12.9747 11.0151 14.9286 9.09406C15.0132 9.01076 14.9978 8.96911 14.9227 8.89745C14.598 8.58394 14.2728 8.27357 13.959 7.95111C13.8539 7.84317 13.7993 7.84138 13.6897 7.95111C11.8604 9.7581 10.0281 11.5621 8.1928 13.3631C7.89854 13.6506 7.66704 13.9252 7.77437 14.3587Z" fill="#EAA300"/>
          <path d="M6.70435 4.73432H3.20226C2.91072 4.73432 2.72743 4.62549 2.64648 4.40962C2.62147 4.34447 2.61127 4.27472 2.61662 4.20528C2.62196 4.13583 2.64272 4.06839 2.67741 4.0077C2.71211 3.94702 2.7599 3.89456 2.81742 3.85405C2.87495 3.81353 2.9408 3.78593 3.01033 3.7732C3.08843 3.76005 3.1676 3.75405 3.24683 3.75528H10.1832C10.2909 3.7493 10.3989 3.76281 10.5016 3.79514C10.5992 3.83422 10.6817 3.90262 10.7375 3.9906C10.7933 4.07857 10.8194 4.18164 10.8123 4.28511C10.8034 4.38844 10.7609 4.4862 10.6911 4.56383C10.6213 4.64146 10.5278 4.69481 10.4248 4.71596C10.3469 4.72963 10.2678 4.73593 10.1887 4.73477C9.02771 4.73536 7.86625 4.73521 6.70435 4.73432Z" fill="#EAA300"/>
          <path d="M5.89049 8.4879C5.00117 8.4879 4.1117 8.4879 3.22208 8.4879C3.13154 8.49066 3.04105 8.48086 2.95328 8.45879C2.85065 8.42478 2.76209 8.35868 2.7011 8.27057C2.6401 8.18245 2.61 8.07713 2.61536 7.97062C2.62218 7.8617 2.66642 7.75829 2.7408 7.6774C2.81519 7.5965 2.9153 7.54291 3.02469 7.52544C3.08599 7.51507 3.14806 7.50983 3.21025 7.50977C4.9992 7.50977 6.78966 7.50977 8.58164 7.50977C8.89183 7.50977 9.06466 7.61233 9.1488 7.84029C9.1719 7.90321 9.18135 7.97019 9.17654 8.03692C9.17173 8.10366 9.15278 8.16866 9.1209 8.22774C9.08902 8.28681 9.04491 8.33865 8.99144 8.37992C8.93796 8.42118 8.87629 8.45095 8.81041 8.4673C8.72738 8.48397 8.64271 8.49148 8.55799 8.4897C7.66928 8.4894 6.78011 8.4888 5.89049 8.4879Z" fill="#EAA300"/>
          <path d="M4.41702 12.241C3.99131 12.241 3.56606 12.2454 3.1408 12.241C2.86791 12.2369 2.67507 12.0811 2.62231 11.8433C2.57683 11.648 2.69053 11.4232 2.87974 11.3224C2.96114 11.2819 3.05143 11.2617 3.14262 11.2637C3.99904 11.2637 4.855 11.257 5.71142 11.2637C6.09984 11.2673 6.33589 11.5857 6.21582 11.9154C6.14123 12.1205 5.95657 12.2387 5.69278 12.2405C5.26752 12.2428 4.84227 12.241 4.41702 12.241Z" fill="#EAA300"/>
          </g>
          <defs>
          <clipPath id="clip0_18950_24919">
          <rect width="16" height="16" fill="white" transform="translate(0.00195312)"/>
          </clipPath>
          </defs>
          </svg>
      </button>


      <button class="action-btn"
              *ngIf="resolution.canComplete"
              (click)="editResolution(resolution)"
              [title]="'COMMON.Complete' | translate">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_18950_74292)">
            <path d="M4.99626 12.005C4.99626 13.5909 5.0049 15.1773 4.99307 16.7632C4.98363 17.24 5.13069 17.7071 5.41241 18.0951C5.69412 18.483 6.09545 18.7711 6.55673 18.9165C6.80163 18.9864 7.05589 19.0193 7.31082 19.0141C8.08218 19.0141 8.85355 19.0141 9.62492 19.0141C9.93738 19.0141 10.1143 19.1301 10.188 19.3724C10.2059 19.4339 10.2112 19.4983 10.2035 19.5618C10.1959 19.6254 10.1755 19.6867 10.1435 19.7424C10.1114 19.7981 10.0685 19.8469 10.0171 19.886C9.96571 19.9252 9.90691 19.9538 9.84415 19.9703C9.79497 19.9839 9.74421 19.9911 9.69315 19.9918C8.78033 19.9824 7.8666 20.0241 6.9547 19.9721C5.61208 19.8964 4.39544 18.8663 4.0939 17.5675C4.033 17.3007 4.00249 17.0281 4.00293 16.7546C4.00293 13.5826 4.00293 10.4103 4.00293 7.23795C4.00293 5.67311 5.01263 4.42715 6.55218 4.07333C6.80136 4.02147 7.05573 3.99772 7.31036 4.00257C9.57899 4.00257 11.8476 4.00257 14.1162 4.00257C15.7149 4.00257 16.9816 4.98384 17.3454 6.49494C17.396 6.70678 17.422 6.9236 17.4228 7.14121C17.4276 8.05695 17.4276 8.97283 17.4228 9.88887C17.4228 10.2736 17.0844 10.5199 16.7501 10.3869C16.5763 10.3175 16.4626 10.1912 16.4422 10.0004C16.434 9.92246 16.4317 9.84453 16.4313 9.76615C16.4217 8.82832 16.4608 7.8896 16.4126 6.95221C16.3612 5.94989 15.4229 5.04072 14.4073 4.9937C14.0435 4.97668 13.6796 4.9825 13.3185 4.9825C11.305 4.9825 9.29154 4.99146 7.27853 4.97802C6.15058 4.97085 5.28961 5.73267 5.06175 6.63512C5.01322 6.84741 4.99123 7.06475 4.99626 7.28229V12.005Z" fill="#52A04F"/>
            <path d="M10.7024 8.73432H7.20031C6.90877 8.73432 6.72548 8.62549 6.64452 8.40962C6.61951 8.34447 6.60932 8.27472 6.61466 8.20528C6.62001 8.13583 6.64076 8.06839 6.67546 8.0077C6.71015 7.94702 6.75795 7.89456 6.81547 7.85405C6.87299 7.81353 6.93884 7.78593 7.00838 7.7732C7.08648 7.76005 7.16565 7.75405 7.24488 7.75528H14.1813C14.289 7.7493 14.3969 7.76281 14.4997 7.79514C14.5972 7.83422 14.6798 7.90262 14.7356 7.9906C14.7913 8.07857 14.8175 8.18164 14.8103 8.28511C14.8014 8.38844 14.7589 8.4862 14.6891 8.56383C14.6193 8.64146 14.5259 8.69481 14.4228 8.71596C14.3449 8.72963 14.2659 8.73593 14.1867 8.73477C13.0258 8.73536 11.8643 8.73521 10.7024 8.73432Z" fill="#52A04F"/>
            <path d="M9.88854 12.4879C8.99922 12.4879 8.10975 12.4879 7.22013 12.4879C7.12958 12.4907 7.0391 12.4809 6.95133 12.4588C6.84869 12.4248 6.76014 12.3587 6.69914 12.2706C6.63815 12.1825 6.60804 12.0771 6.6134 11.9706C6.62023 11.8617 6.66447 11.7583 6.73885 11.6774C6.81323 11.5965 6.91334 11.5429 7.02274 11.5254C7.08403 11.5151 7.14611 11.5098 7.2083 11.5098C8.99725 11.5098 8.78771 11.5098 10.5797 11.5098C10.8899 11.5098 11.0627 11.6123 11.1468 11.8403C11.1699 11.9032 11.1794 11.9702 11.1746 12.0369C11.1698 12.1037 11.1508 12.1687 11.1189 12.2277C11.0871 12.2868 11.043 12.3387 10.9895 12.3799C10.936 12.4212 10.8743 12.4509 10.8085 12.4673C10.7254 12.484 10.6408 12.4915 10.556 12.4897C9.66733 12.4894 10.7782 12.4888 9.88854 12.4879Z" fill="#52A04F"/>
            <path d="M8.41506 16.241C7.98936 16.241 7.5641 16.2454 7.13885 16.241C6.86596 16.2369 6.67312 16.0811 6.62036 15.8433C6.57487 15.648 6.68858 15.4232 6.87778 15.3224C6.95918 15.2819 7.04947 15.2617 7.14067 15.2637C7.99709 15.2637 8.85305 15.257 9.70947 15.2637C10.0979 15.2673 10.3339 15.5857 10.2139 15.9154C10.1393 16.1205 9.95462 16.2387 9.69083 16.2405C9.26557 16.2428 8.84032 16.241 8.41506 16.241Z" fill="#52A04F"/>
            <path d="M16.877 14.1738C17.1541 13.9017 17.5471 13.9594 17.6953 14.2773C17.7876 14.4751 17.7399 14.6892 17.5547 14.877C17.2556 15.1792 16.954 15.4796 16.6533 15.7803C16.3523 16.0813 16.0548 16.3851 15.751 16.6826C15.6188 16.8109 15.4636 16.8617 15.3135 16.8428C15.1651 16.8596 15.0123 16.8083 14.8818 16.6816C14.275 16.0874 14.782 16.594 14.1836 15.9893C13.9986 15.8016 13.9517 15.5873 14.0439 15.3896C14.1922 15.0719 14.5842 15.0141 14.8613 15.2861C15.3428 15.7601 15.1282 15.5487 15.3145 15.7354C15.8343 15.2137 16.3521 14.6905 16.877 14.1738Z" fill="#52A04F"/>
            <circle cx="15.498" cy="15.5" r="4" stroke="#52A04F"/>
            </g>
            <defs>
            <clipPath id="clip0_18950_74292">
            <rect width="16" height="16" fill="white" transform="translate(4 4)"/>
            </clipPath>
            </defs>
            </svg>

        <!-- <img src="assets/images/Arrow_Icon.png" class="rotate-icon" alt="complete" /> -->
      </button>

      <button class="action-btn"
               *ngIf="resolution.canCancel && tokenService.hasPermission('Resolution.Cancel')"
              (click)="cancelResolution(resolution)"
              [title]="'COMMON.CANCEL' | translate">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 4C7.584 4 4 7.584 4 12C4 16.416 7.584 20 12 20C16.416 20 20 16.416 20 12C20 7.584 16.416 4 12 4ZM5.6 12C5.6 8.464 8.464 5.6 12 5.6C13.48 5.6 14.84 6.104 15.92 6.952L6.952 15.92C6.0733 14.8026 5.59699 13.4215 5.6 12ZM12 18.4C10.52 18.4 9.16 17.896 8.08 17.048L17.048 8.08C17.9267 9.19743 18.403 10.5785 18.4 12C18.4 15.536 15.536 18.4 12 18.4Z" fill="#C50F1F"/>
      </svg>
        <!-- <img src="assets/images/x.png" class="mx-2" alt="cancel" /> -->
      </button>

      <button class="action-btn"
               *ngIf="resolution.canDelete && tokenService.hasPermission('Resolution.Delete')"
              (click)="deleteResolution(resolution)"
              [title]="'COMMON.DELETE' | translate">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12.3333 4.00033V3.33366C12.3333 2.40024 12.3333 1.93353 12.1517 1.57701C11.9919 1.2634 11.7369 1.00844 11.4233 0.848648C11.0668 0.666992 10.6001 0.666992 9.66667 0.666992H8.33333C7.39991 0.666992 6.9332 0.666992 6.57668 0.848648C6.26308 1.00844 6.00811 1.2634 5.84832 1.57701C5.66667 1.93353 5.66667 2.40024 5.66667 3.33366V4.00033M7.33333 8.58366V12.7503M10.6667 8.58366V12.7503M1.5 4.00033H16.5M14.8333 4.00033V13.3337C14.8333 14.7338 14.8333 15.4339 14.5608 15.9686C14.3212 16.439 13.9387 16.8215 13.4683 17.0612C12.9335 17.3337 12.2335 17.3337 10.8333 17.3337H7.16667C5.76654 17.3337 5.06647 17.3337 4.53169 17.0612C4.06129 16.8215 3.67883 16.439 3.43915 15.9686C3.16667 15.4339 3.16667 14.7338 3.16667 13.3337V4.00033" stroke="#6C737F" stroke-width="1.1" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        <!-- <img src="assets/images/trash.png" alt="delete" /> -->
      </button>


      <button class="action-btn"
               *ngIf="resolution.canSendToVote"
              (click)="onSendToVote(resolution)"
              [title]="'RESOLUTIONS.SEND_TO_VOTE' | translate">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15.668 19.208C15.7895 19.2081 15.9063 19.2569 15.9922 19.3428C16.078 19.4287 16.126 19.5456 16.126 19.667C16.1259 19.7884 16.0781 19.9053 15.9922 19.9912C15.9063 20.0771 15.7894 20.1249 15.668 20.125H4.33301C4.21157 20.1249 4.09467 20.0771 4.00879 19.9912C3.92291 19.9053 3.87509 19.7884 3.875 19.667C3.875 19.5456 3.92305 19.4287 4.00879 19.3428C4.09467 19.2569 4.21157 19.2081 4.33301 19.208H15.668ZM15.2979 3.875C15.5959 3.87512 15.8818 3.99349 16.0928 4.2041L19.7969 7.9082C20.0076 8.11938 20.126 8.40575 20.126 8.7041C20.1258 9.00228 20.0075 9.28796 19.7969 9.49902L19.042 10.2539V10.2549C18.8295 10.4657 18.5473 10.5829 18.2471 10.583H18.2432C18.095 10.5829 17.9482 10.5533 17.8115 10.4961C17.7091 10.4532 17.6135 10.3949 17.5283 10.3242L17.4463 10.249L13.7637 6.52441L13.7627 6.52344C13.5547 6.31174 13.4385 6.02631 13.4395 5.72949C13.4405 5.43255 13.5589 5.14782 13.7686 4.9375L14.502 4.2041L14.585 4.12988C14.7852 3.96608 15.0369 3.875 15.2979 3.875ZM15.2559 4.79395C15.2426 4.79656 15.2294 4.80047 15.2168 4.80566C15.1916 4.81605 15.1687 4.83135 15.1494 4.85059L14.416 5.58398C14.3772 5.62305 14.3557 5.67635 14.3555 5.73145C14.3553 5.78606 14.3761 5.83869 14.4141 5.87793L18.0977 9.60352C18.1336 9.6397 18.1803 9.6597 18.2305 9.66309C18.2688 9.65387 18.2972 9.64915 18.3281 9.63867C18.3601 9.62785 18.3799 9.61634 18.3926 9.60352L19.1475 8.84961L19.1738 8.81738C19.1964 8.78332 19.208 8.74261 19.208 8.70117C19.2079 8.65992 19.1962 8.61986 19.1738 8.58594L19.1475 8.55371L15.4434 4.85059C15.4241 4.83144 15.4011 4.81599 15.376 4.80566C15.351 4.79545 15.3238 4.78998 15.2969 4.79004L15.2559 4.79395Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M17.6763 9.27441C17.7378 9.28493 17.7968 9.30778 17.8491 9.3418C17.9015 9.37584 17.9464 9.42066 17.981 9.47266C18.0155 9.52461 18.0391 9.58315 18.0503 9.64453C18.0614 9.706 18.0595 9.76922 18.0454 9.83008C18.0313 9.89093 18.0053 9.94875 17.9683 9.99902L17.9673 9.99805L15.6323 13.5977L15.6313 13.5967C15.6149 13.6227 15.5972 13.6479 15.5757 13.6699H15.5747L12.6548 16.6357V16.6367C12.5024 16.7904 12.3206 16.9122 12.1206 16.9951C11.9707 17.0572 11.8127 17.0969 11.6519 17.1123L11.4897 17.1191C10.9096 17.119 10.3851 16.8198 10.0874 16.3203C9.70451 15.6766 9.83115 14.8412 10.3813 14.2881L12.6499 12.0146C12.5386 11.9028 12.4085 11.8112 12.2632 11.748C12.1281 11.6893 11.9837 11.6551 11.8374 11.6465H11.689C11.4769 11.6571 11.2711 11.7216 11.0903 11.833C10.9096 11.9444 10.7596 12.0995 10.6548 12.2842L10.6499 12.292C10.5853 12.3886 10.4866 12.4577 10.3735 12.4844C10.2605 12.511 10.1415 12.493 10.0405 12.4355C9.93956 12.378 9.8633 12.2848 9.82861 12.1738C9.79395 12.0629 9.80282 11.9427 9.85303 11.8379L9.85693 11.8301C10.0379 11.5118 10.2962 11.2439 10.6079 11.0518C10.9194 10.8598 11.2747 10.7492 11.6401 10.7305C12.006 10.7106 12.3713 10.7839 12.7017 10.9424C13.032 11.1009 13.3173 11.3399 13.5308 11.6377L13.6343 11.7822L13.6753 11.8506C13.7097 11.9218 13.7247 12.0012 13.7192 12.0811C13.7119 12.1877 13.668 12.2889 13.5942 12.3662L13.5923 12.3682L11.0288 14.9336L11.0298 14.9346C10.7706 15.1956 10.7144 15.5833 10.8735 15.8506V15.8516C11.1037 16.2388 11.6875 16.3058 12.0044 15.9902L14.8921 13.0566L17.2046 9.49316C17.235 9.44171 17.275 9.39639 17.3228 9.36035C17.3726 9.32273 17.43 9.29602 17.4907 9.28125C17.5514 9.26648 17.6147 9.26392 17.6763 9.27441ZM14.3218 5.90918C14.3835 5.91579 14.4434 5.93444 14.4976 5.96484C14.5517 5.99525 14.5994 6.03665 14.6372 6.08594C14.6749 6.13516 14.702 6.19188 14.7173 6.25195C14.7325 6.31194 14.7356 6.37441 14.7261 6.43555C14.7164 6.4969 14.6937 6.55582 14.6606 6.6084C14.6276 6.66097 14.5848 6.7069 14.5337 6.74219C14.4841 6.77646 14.4276 6.79891 14.3687 6.81152L14.3696 6.8125L10.7231 7.73926H10.7222C10.2568 7.85709 9.82203 8.07347 9.44775 8.37402C9.07349 8.67457 8.76875 9.05256 8.55322 9.48145C8.17344 10.239 8.09737 11.1133 8.34131 11.9248V11.9258C8.3616 11.9939 8.36628 12.0657 8.354 12.1357C8.34162 12.206 8.31257 12.2728 8.27002 12.3301C8.22754 12.3872 8.17243 12.4337 8.10889 12.4658C8.04535 12.4978 7.97496 12.5144 7.90381 12.5146H7.90283C7.80442 12.5146 7.7084 12.4835 7.62939 12.4248C7.5503 12.366 7.49274 12.2828 7.46436 12.1885C7.3454 11.7931 7.28461 11.3826 7.28467 10.9697C7.28468 10.32 7.44032 9.66471 7.73193 9.07324L7.84033 8.87109C8.10515 8.40718 8.45506 7.99653 8.87256 7.66113C9.34973 7.27783 9.90417 7.00159 10.4976 6.85156L14.144 5.9248V5.92578C14.2017 5.90883 14.2619 5.90278 14.3218 5.90918Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M12.9819 11.874C13.1031 11.8741 13.2194 11.9222 13.3052 12.0078C13.3911 12.0937 13.4398 12.2106 13.4399 12.332C13.4399 12.4536 13.3911 12.5703 13.3052 12.6562C13.2193 12.742 13.1033 12.7909 12.9819 12.791H6.12549V19.208H13.8872V14.2676C13.8872 14.146 13.936 14.0293 14.022 13.9434C14.1078 13.8576 14.2239 13.8087 14.3452 13.8086C14.4668 13.8086 14.5835 13.8574 14.6694 13.9434C14.7554 14.0293 14.8042 14.146 14.8042 14.2676V19.666C14.8042 19.7876 14.7554 19.9043 14.6694 19.9902C14.5835 20.0762 14.4668 20.125 14.3452 20.125H5.6665C5.54507 20.1249 5.42816 20.0761 5.34229 19.9902C5.25656 19.9043 5.2085 19.7874 5.2085 19.666V12.332L5.21729 12.2422C5.23494 12.1542 5.2779 12.0722 5.34229 12.0078C5.42816 11.9219 5.54507 11.8741 5.6665 11.874H12.9819Z" fill="#5F3177" stroke="#5F3177" stroke-width="0.25"/>
          <path d="M10.5 12.0003C10.8333 11.667 11.8 11.2003 13 12.0003H10.5Z" fill="#D9D9D9"/>
          </svg>
        <!-- <img src="assets/images/vote.svg" alt="vote" /> -->
      </button>





      <!-- Bulk Actions -->
      <button class="action-btn"
               *ngIf="resolution.canVote"
              (click)="bulkApproveResolutionItems(resolution.id)"
              [title]="'INVESTMENT_FUNDS.RESOLUTIONS.APPROVE_ALL_ITEMS' | translate">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6489 6.34652C19.8737 6.56868 20 6.86984 20 7.18383C20 7.49782 19.8737 7.79898 19.6489 8.02114L9.89031 17.6534C9.77765 17.7645 9.64373 17.8523 9.49634 17.9118C9.34895 17.9713 9.19103 18.0013 9.0318 18C8.87256 17.9986 8.71518 17.9661 8.56882 17.9041C8.42247 17.8422 8.29006 17.7521 8.17931 17.6391L4.33316 13.7148C4.11382 13.4886 3.99412 13.1859 4.00022 12.8728C4.00632 12.5598 4.13773 12.2618 4.36572 12.0441C4.59371 11.8263 4.89975 11.7065 5.21695 11.7109C5.53416 11.7152 5.83674 11.8432 6.05857 12.0671L9.05642 15.1256L17.9523 6.34652C18.1774 6.12463 18.4825 6 18.8006 6C19.1187 6 19.4239 6.12463 19.6489 6.34652Z" fill="#16A249"/>
        </svg>
        <!-- <img src="assets/images/approve.png" alt="approve all" /> -->
      </button>



      <button class="action-btn details-btn pt-2"
                    *ngIf="resolution.canVote"
              (click)="bulkRejectResolutionItems(resolution.id)"
              [title]="'INVESTMENT_FUNDS.RESOLUTIONS.REJECT_ALL_ITEMS' | translate">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M0.250744 0.261676C0.411343 0.100986 0.629134 0.0107158 0.856221 0.0107158C1.08331 0.0107158 1.3011 0.100986 1.4617 0.261676L5.99465 4.79853L10.5276 0.261676C10.6066 0.17981 10.7011 0.114511 10.8056 0.0695887C10.9101 0.0246667 11.0224 0.00102135 11.1362 3.23632e-05C11.2499 -0.000956622 11.3626 0.0207304 11.4679 0.0638283C11.5731 0.106926 11.6688 0.170572 11.7492 0.251051C11.8296 0.331531 11.8932 0.427232 11.9362 0.532572C11.9793 0.637912 12.001 0.75078 12 0.864591C11.999 0.978402 11.9754 1.09088 11.9305 1.19545C11.8856 1.30003 11.8203 1.39461 11.7386 1.47368L7.2056 6.01053L11.7386 10.5474C11.8946 10.709 11.9809 10.9256 11.9789 11.1503C11.977 11.375 11.8869 11.59 11.7281 11.7489C11.5693 11.9079 11.3545 11.998 11.13 12C10.9054 12.0019 10.6891 11.9155 10.5276 11.7594L5.99465 7.22253L1.4617 11.7594C1.30018 11.9155 1.08385 12.0019 0.859303 12C0.634757 11.998 0.41996 11.9079 0.261176 11.7489C0.102391 11.59 0.0123241 11.375 0.0103729 11.1503C0.00842164 10.9256 0.0947427 10.709 0.250744 10.5474L4.78369 6.01053L0.250744 1.47368C0.0901927 1.31294 0 1.09496 0 0.867676C0 0.640392 0.0901927 0.422414 0.250744 0.261676Z" fill="#C50F1F"/>
       </svg>
  </button>
  </div>
    </div>
  <div class="fund-name-section">
              <h3 class="fund-name">{{ resolution.fundName || '-' }}</h3>
            </div>
    <!-- Resolution Info -->
    <div class="resolution-info mb-3">


      <h3 class="resolution-title">
        <span class="title">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.CODE' | translate }}:</span>
        {{ resolution.code }}
      </h3>

      <h3 class="resolution-title" *ngIf="resolution.parentResolutionCode">
        <span class="title">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.CODE_RELATED_DECISION' | translate }}:</span>

        <span (click)="viewParentResolutionDetails(resolution)" class="resolution-title referral-code-link">   {{resolution?.parentResolutionCode}}</span>
      </h3>
    </div>

    <!-- Card Content -->
    <div class="card-content">
      <!-- <p class="title">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.TYPE' | translate }}</p> -->
      <p class="resolution-type mb-0">
        {{ resolution.resolutionType.localizedName || resolution.resolutionType.nameAr || resolution.resolutionType.nameEn || '-' }}
      </p>

      <!-- <p class="title">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.DESCRIPTION_CARD' | translate }}</p> -->
      <p class="resolution-description description-text mb-3" [title]="resolution.description">
        {{ resolution.description || '-' }}
      </p>

      <div class="resolution-meta">
        <div class="meta-item">
          <p class="meta-label">{{ 'FUND_DETAILS.CREATION_DATE' | translate }}:</p>
          <div class="d-flex">
         <p class="gregorian mb-0 ">{{(resolution.lastUpdated ? resolution.lastUpdated.toString() : '') | date: 'd/M/y'}}</p>

          <p class="hijri title mx-2">{{ resolution.lastUpdated ? (resolution.lastUpdated | dateHijriConverter) : '' }}</p>
          </div>
        </div>
      </div>
    </div>

  </ng-container>
</div>
</div>


    <!-- Pagination Controls -->


    <div *ngIf="totalCount==0 || hasError || allResolutionsHaveNoViewPermission && !isLoading"
      class="d-flex  flex-column gap-4 justify-content-center mt-5 align-items-center">
      <img src="assets/images/nodata.png" width="350">

      <ng-container>
        <p class="text-center mt-3 header fs-20">{{'INVESTMENT_FUNDS.RESOLUTIONS.Inbox_empty' | translate}}</p>
      </ng-container>
    </div>

    <div class="pagination-section" *ngIf="totalCount > 0 && !allResolutionsHaveNoViewPermission">
      <!-- Records Information -->
      <!-- <div class="pagination-info">
        <div class="records-info">
          <span>{{ 'PAGINATION.RECORDS_INFO' | translate: getRecordsInfo() }}</span>
        </div>
        <div class="page-size-selector">
          <label>{{ 'PAGINATION.PAGE_SIZE' | translate }}:</label>
          <select class="page-size-select" [(ngModel)]="pageSize" (ngModelChange)="onPageSizeChange($event)">
            <option *ngFor="let size of pageSizeOptions" [value]="size">{{size}}</option>
          </select>
        </div>
      </div> -->

      <!-- Pagination Controls -->
      <div class="pagination-controls">
        <!-- First Page Button -->
        <!-- <button class="pagination-btn first-btn"
                [disabled]="!canGoPrevious()"
                (click)="onFirstPage()"
                [title]="'PAGINATION.FIRST' | translate">
          <i class="fas fa-angle-double-left"></i>
          <span class="btn-text">{{ 'PAGINATION.FIRST' | translate }}</span>
        </button> -->

        <!-- Previous Page Button -->
        <button class="pagination-btn prev-btn" [disabled]="!canGoPrevious()" (click)="onPreviousPage()"
          [title]="'PAGINATION.PREVIOUS' | translate">
          <!-- <i class="fas fa-angle-left"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-left2.png' : 'assets/images/arrow-right.png'" class="mx-2"
            alt="previous">

          <span class="btn-text">{{ 'PAGINATION.PREVIOUS' | translate }}</span>
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button *ngFor="let page of getPageNumbers()" class="pagination-btn page-number-btn"
            [class.active]="page === currentPage" (click)="onPageChange(page)">
            {{page}}
          </button>
        </div>

        <!-- Next Page Button -->
        <button class="pagination-btn next-btn" [disabled]="!canGoNext()" (click)="onNextPage()"
          [title]="'PAGINATION.NEXT' | translate">
          <span class="btn-text">{{ 'PAGINATION.NEXT' | translate }}</span>
          <!-- <i class="fas fa-angle-right"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-right.png' : 'assets/images/arrow-left2.png'" class="mx-2"
            alt="next">

        </button>

        <!-- Last Page Button -->
        <!-- <button class="pagination-btn last-btn"
                [disabled]="!canGoNext()"
                (click)="onLastPage()"
                [title]="'PAGINATION.LAST' | translate">
          <span class="btn-text">{{ 'PAGINATION.LAST' | translate }}</span>
          <i class="fas fa-angle-double-right"></i>
        </button> -->
      </div>

      <!-- Page Information -->
      <!-- <div class="page-info">
        <span>{{ 'PAGINATION.PAGE_INFO' | translate: {current: currentPage, total: totalPages} }}</span>
      </div> -->
    </div>
    <!-- Empty State -->
    <!-- <div class="empty-state" >
      <div class="empty-state-content">
        <i class="fas fa-file-alt empty-icon"></i>
        <h3>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_RESOLUTIONS' | translate }}</h3>
        <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_RESOLUTIONS_MESSAGE' | translate }}</p>
        <button class="btn btn-primary"
                (click)="addNewResolution()">
          {{ 'INVESTMENT_FUNDS.RESOLUTIONS.CREATE_FIRST' | translate }}
        </button>
      </div>
    </div> -->
  </div>
</div>
