import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { NgxChartsModule, Color, LegendPosition, ScaleType } from '@swimlane/ngx-charts';

// API Types
import { ResolutionStatusBreakdownDto } from '@core/api/api.generated';

export interface DonutChartData {
  name: string;
  value: number;
  extra?: {
    code: string;
    color: string;
  };
}

@Component({
  selector: 'app-resolution-status-donut-chart',
  standalone: true,
  imports: [CommonModule, TranslateModule, NgxChartsModule],
  templateUrl: './resolution-status-donut-chart.component.html',
  styleUrl: './resolution-status-donut-chart.component.scss'
})
export class ResolutionStatusDonutChartComponent implements OnChanges {
  @Input() resolutionStatusData: ResolutionStatusBreakdownDto[] = [];
  @Input() totalResolutions: number = 0;
  @Input() loading: boolean = false;
  @Input() isBoradMember: boolean = false;


  // Chart configuration
  chartData: DonutChartData[] = [];
  colorScheme: Color = {
    name: 'resolution-status',
    selectable: true,
    group: ScaleType.Ordinal,
    domain: [
'#7555ac',
'#856404',
'#17a2b8',
'#9d7009',
'#27ae60',
'#828282',
'#2f80ed',
'#0e700e',
'#C50F1F',
'#4f4f4f'
]
  };

  // Chart options
   view!: [number, number];
  showLegend: boolean = true;
  showLabels: boolean = true;
  isDoughnut: boolean = true;
  legendPosition: LegendPosition = LegendPosition.Right;
  animations: boolean = true;

  // Status color mapping
  private statusColors: { [key: string]: string } = {
    '1': '#7555ac', //  مسودة
    '2': '#fff3cd', // معلق
    '3': '#9d7009',  // استكمال البيانات
    '4': '#17a2b8',//في انتظار التأكيد
    '5': '#27ae60', // مؤكد
    '6': '#828282', //مرفوض
    '7': '#2f80ed', //التصويت قيد التقدم
    '8': '#0e700e', //معتمد
    '9': '#C50F1F', //غير معتمد
	  '10':'#4f4f4f' //ملغي
  };


ngOnInit(): void {
  this.view = this.isBoradMember ? [170, 170] : [267, 267];
}


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['resolutionStatusData'] || changes['totalResolutions']) {
      this.updateChartData();
    }
     if (changes['isBoradMember']) {
    this.view = this.isBoradMember ? [170, 170] : [267, 267];
  }
  }

  /**
   * Transform API data to chart format
   */
  private updateChartData(): void {
    if (!this.resolutionStatusData || this.resolutionStatusData.length === 0) {
      this.chartData = [];
      return;
    }

    this.chartData = this.resolutionStatusData.map(item => {
      const statusKey = item.statusId.toString() || '0';
      const color = this.statusColors[statusKey] || '#6c757d';

      return {
        name: item.status || 'Unknown',
        value: item.count || 0,
        extra: {
          code: statusKey,
          color: color
        }
      };
    });

    // Update color scheme based on actual data
    this.colorScheme = {
      name: 'resolution-status',
      selectable: true,
      group: ScaleType.Ordinal,
      domain: this.chartData.map(item => item.extra?.color || '#6c757d')
    };
  }


  /**
   * Format label for chart
   */
  formatLabel(label: string): string {
    return label;
  }

  /**
   * Format percentage for display
   */
  formatPercent(value: number): string {
    if (this.totalResolutions === 0) return '0%';
    const percentage = Math.round((value / this.totalResolutions) * 100);
    return `${percentage}%`;
  }

  /**
   * Handle chart select event
   */
  onSelect(event: any): void {
    console.log('Chart item selected:', event);
  }

  /**
   * Handle chart activate event
   */
  onActivate(event: any): void {
    console.log('Chart item activated:', event);
  }

  /**
   * Handle chart deactivate event
   */
  onDeactivate(event: any): void {
    console.log('Chart item deactivated:', event);
  }

  /**
   * Custom tooltip template data
   */
  getTooltipText(item: any): string {
    const percentage = this.formatPercent(item.value);
    return `${item.name}: ${item.value} (${percentage})`;
  }

  /**
   * Check if chart has data
   */
  get hasData(): boolean {
    return this.chartData.length > 0 && this.totalResolutions > 0;
  }

  /**
   * Get total count for center display
   */
  get centerText(): string {
    return this.totalResolutions.toString();
  }

  /**
   * Get center label
   */
  get centerLabel(): string {
    return 'إجمالي القرارات';
  }

  /**
   * TrackBy function for chart data
   */
  trackByName(_index: number, item: DonutChartData): string {
    return item.name;
  }
}
