<div class="question-dialog">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <h2 class="dialog-title">{{ getDialogTitle() | translate }}</h2>
    <button
      type="button"
      class="close-btn"
      (click)="onCancel()"
      [attr.aria-label]="'COMMON.CLOSE' | translate">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Dialog Content -->
  <div class="dialog-content">
    <!-- Basic Question Form -->
    <div class="question-form-section">
      <app-form-builder 
        [formControls]="formControls" 
        [formGroup]="formGroup" 
        [isFormSubmitted]="isValidationFire">
      </app-form-builder>
    </div>

    <!-- Options Section (for Choice questions) -->
    <div class="options-section" *ngIf="isChoiceType()">
      <div class="options-header">
        <h4 class="options-title">{{ 'ASSESSMENTS.OPTIONS' | translate }}</h4>
        <p class="options-description">
          {{ 'ASSESSMENTS.OPTIONS_DESCRIPTION' | translate }}
        </p>
      </div>

      <div class="options-list">
        <div class="option-item"
             *ngFor="let optionGroup of getOptions(optionsFormArray.controls); let i = index"
             [formGroup]="$any(optionGroup)">
          <div class="option-input-group">
            <div class="option-number">{{ i + 1 }}</div>

            <div class="option-input-container">
              <input
                type="text"
                class="form-control option-input"
                [class.is-invalid]="isValidationFire && optionGroup.get('text')?.invalid"
                formControlName="text"
                [placeholder]="('ASSESSMENTS.OPTION_PLACEHOLDER' | translate) + ' ' + (i + 1)"
                maxlength="200">
              <div class="invalid-feedback" *ngIf="isValidationFire && optionGroup.get('text')?.invalid">
                {{ 'ASSESSMENTS.VALIDATION.OPTION_REQUIRED' | translate }}
              </div>
            </div>
            <button class="action-btn"
              (click)="removeOption(i)"
              [title]="'ASSESSMENTS.REMOVE_OPTION' | translate">
              <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21.3333 13.0003V12.3337C21.3333 11.4002 21.3333 10.9335 21.1517 10.577C20.9919 10.2634 20.7369 10.0084 20.4233 9.84865C20.0668 9.66699 19.6001 9.66699 18.6667 9.66699H17.3333C16.3999 9.66699 15.9332 9.66699 15.5767 9.84865C15.2631 10.0084 15.0081 10.2634 14.8483 10.577C14.6667 10.9335 14.6667 11.4002 14.6667 12.3337V13.0003M16.3333 17.5837V21.7503M19.6667 17.5837V21.7503M10.5 13.0003H25.5M23.8333 13.0003V22.3337C23.8333 23.7338 23.8333 24.4339 23.5608 24.9686C23.3212 25.439 22.9387 25.8215 22.4683 26.0612C21.9335 26.3337 21.2335 26.3337 19.8333 26.3337H16.1667C14.7665 26.3337 14.0665 26.3337 13.5317 26.0612C13.0613 25.8215 12.6788 25.439 12.4392 24.9686C12.1667 24.4339 12.1667 23.7338 12.1667 22.3337V13.0003" stroke="#C50F1F" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Add Option Button -->
      <div class="add-option-section" *ngIf="canAddOption()">
        <button 
          type="button"
          class="btn btn-outline-primary btn-sm add-option-btn"
          (click)="addOption()"
          [title]="'ASSESSMENTS.ADD_OPTION' | translate">
          <i class="fas fa-plus"></i>
          {{ 'ASSESSMENTS.ADD_OPTION' | translate }}
        </button>
      </div>

      <!-- Options Limit Info -->
      <div class="options-info" *ngIf="optionsFormArray.length >= maxOptions">
        <small class="text-muted">
          <i class="fas fa-info-circle"></i>
          {{ 'ASSESSMENTS.MAX_OPTIONS_REACHED' | translate: {max: maxOptions} }}
        </small>
      </div>
    </div>
  </div>

  <!-- Dialog Actions -->
  <div class="dialog-actions">
    <app-custom-button 
      [btnName]="'COMMON.CANCEL' | translate" 
      [buttonType]="buttonEnum.Secondary"
      [iconName]="IconEnum.cancel"
      (click)="onCancel()">
    </app-custom-button>

    <app-custom-button
      [btnName]="getSubmitButtonText() | translate"
      [buttonType]="buttonEnum.Primary"
      [iconName]="data.isEdit ? IconEnum.verify : IconEnum.plus"
      (click)="onSubmit()">
    </app-custom-button>
  </div>
</div>
