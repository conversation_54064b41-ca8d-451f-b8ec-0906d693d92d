import { CommonModule } from '@angular/common';
import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { Router, ActivatedRoute } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { AttachmentModule } from '@shared/enum/AttachmentModule';
import { InputType } from '@shared/enum/input-type.enum';
import { MeetingPlaceEnum } from '@shared/enum/meeting-enums';
import { SizeEnum } from '@shared/enum/size-enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { MeetingAgendaPopupComponent } from '../meeting-agenda-popup/meeting-agenda-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { CancelMeetingDialogComponent, CancelMeetingDialogData } from '../cancel-meeting-dialog/cancel-meeting-dialog.component';
import { Subject, takeUntil } from 'rxjs';
import { DateTime } from 'luxon';
import {
  MeetingServiceProxy,
  EditMeetingCommand,
  StringBaseResponse,
  MeetingSessionResponseDtoBaseResponse,
  MeetingSessionResponseDto,
  MeetingLocationType,
  MeetingAgendaItemDto,
  MeetingAgendaItemResponseDto,
  MeetingAttendeeDto,
  CheckUserAvailabilityQuery,
  UserAvailabilityCheckResponseDtoBaseResponse,
  GetUserListResponse,
  GetUserListResponsePaginatedResult,
  UserManagementServiceProxy
} from '@core/api/api.generated';
import moment from 'moment';
import { DateConversionService } from '@shared/services/date.service';
import { ErrorModalService } from '@core/services/error-modal.service';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-edit-meeting',
  standalone: true,
  imports: [
    CommonModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    PageHeaderComponent,
    TranslateModule,
    FormBuilderComponent,
    MatCheckboxModule,
    FormsModule
  ],
  templateUrl: './edit-meeting.component.html',
  styleUrl: './edit-meeting.component.scss'
})
export class EditMeetingComponent implements OnInit, OnDestroy {
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
    { label: 'INVESTMENT_FUNDS.MEETING.TITLE', url: '' },
  ];
  breadcrumbSizeEnum = SizeEnum;
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;
  isMeetingRoom: boolean = true;
  isValidationFire: boolean = false;
  meetingTypes: any[] = [
    { id: 1, name: 'دوري', value: 'Periodic' },
    { id: 2, name: 'سنوي', value: 'Annual' }];
  // API Integration Properties
  meetingId: number = 0;
  fundId: number = 0;
  isLoading: boolean = false;
  isSubmitting: boolean = false;
  meetingData: MeetingSessionResponseDto | null = null;
  agendaItems: MeetingAgendaItemDto[] = [];
  selectedAttendees: MeetingAttendeeDto[] = [];

  // Flag to track if members are loaded
  private membersLoaded: boolean = false;

  // Attachment handling
  attachmentIds: number[] = [];
  // Conflict checking properties
  userConflicts: Map<number, boolean> = new Map(); // Track conflicts per user
  isCheckingConflicts = false;

  // Lifecycle management
  private destroy$ = new Subject<void>();

  formGroup!: FormGroup;
  formControls: IControlOption[] = [
    {
      type: InputType.Mixed,
      formControlName: 'subject',
      id: 'subject',
      name: 'subject',
      label: 'INVESTMENT_FUNDS.MEETING.MEETING_SUBJECT',
      placeholder: 'INVESTMENT_FUNDS.MEETING.ENTER_MEETING_SUBJECT',
      isRequired: true,
      class: 'col-md-6',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'meetingTypeId',
      id: 'meetingTypeId',
      name: 'meetingTypeId',
      label: 'INVESTMENT_FUNDS.MEETING.MEETING_TYPE',
      placeholder: 'INVESTMENT_FUNDS.MEETING.MEETING_TYPE_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-6',
      options: [],
    },
    {
      type: InputType.Date,
      formControlName: 'meetingDate',
      id: 'meetingDate',
      name: 'meetingDate',
      label: 'INVESTMENT_FUNDS.MEETING.MEETING_DATE',
      placeholder: 'INVESTMENT_FUNDS.MEETING.MEETING_DATE',
      isRequired: true,
      class: 'col-md-4',
      onChange: (value: any) => this.onDateChange(value),
    },
    {
      type: InputType.Time,
      formControlName: 'startTime',
      id: 'startTime',
      name: 'startTime',
      label: 'INVESTMENT_FUNDS.MEETING.START_TIME',
      placeholder: 'INVESTMENT_FUNDS.MEETING.START_TIME',
      isRequired: true,
      class: 'col-md-4',
      onChange: (value: any) => this.onDateTimeChange(),

    },
    {
      type: InputType.Time,
      formControlName: 'endTime',
      id: 'endTime',
      name: 'endTime',
      label: 'INVESTMENT_FUNDS.MEETING.END_TIME',
      placeholder: 'INVESTMENT_FUNDS.MEETING.END_TIME',
      isRequired: true,
      class: 'col-md-4',
      onChange: (value: any) => this.onDateTimeChange(),
    },
    {
      type: InputType.Radio,
      formControlName: 'locationType',
      id: 'locationType',
      name: 'locationType',
      label: 'INVESTMENT_FUNDS.MEETING.PLACE',
      isRequired: true,
      class: 'col-md-12',
      options: [
        { name: 'INVESTMENT_FUNDS.MEETING.MEETING_ROOM', id: MeetingPlaceEnum.MeetingRoom },
        { name: 'INVESTMENT_FUNDS.MEETING.VIA_INTERNET', id: MeetingPlaceEnum.ViaInternet },
      ],
      onChange: (value: any) => this.meetingPlaceChanged(value),
    },
    {
      type: InputType.Mixed,
      formControlName: 'meetingRoomDetails',
      id: 'meetingRoomDetails',
      name: 'meetingRoomDetails',
      label: 'INVESTMENT_FUNDS.MEETING.THE_MEETING_ROOM',
      placeholder: 'INVESTMENT_FUNDS.MEETING.MEETING_ROOM_PLACEHOLDER',
      isRequired: this.isMeetingRoom,
      class: 'col-md-12',
      isVisible: () => this.isMeetingRoom,
    },
    {
      type: InputType.Mixed,
      formControlName: 'onlineMeetingLink',
      id: 'onlineMeetingLink',
      name: 'onlineMeetingLink',
      label: 'INVESTMENT_FUNDS.MEETING.MEETING_LINK',
      placeholder: 'INVESTMENT_FUNDS.MEETING.MEETING_LINK',
      isRequired: !this.isMeetingRoom,
      class: 'col-md-12',
    },
    {
      type: InputType.Textarea,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'INVESTMENT_FUNDS.MEETING.MEETING_DESCRIPTION',
      placeholder: 'INVESTMENT_FUNDS.MEETING.MEETING_DESCRIPTION_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-12',
      max: 500,
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: '',
      placeholder: 'RESOLUTIONS.FILE_UPLOAD_TEXT',
      isRequired: false,
      class: 'col-md-12',
      allowedTypes: ['.pdf'],
      multiple: true,
      moduleId: AttachmentModule.Resolution
    },
  ];
  members: any = [];
  defaultAttendees: any[] = [];
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private translateService: TranslateService,
    private meetingServiceProxy: MeetingServiceProxy,
    private DateConversionService: DateConversionService,
    private userManagementServiceProxy: UserManagementServiceProxy,
    private cdr: ChangeDetectorRef,
    private errorModalService: ErrorModalService,
    private breadcrumbService: BreadcrumbService
  ) { }

  ngOnInit() {
    const currentLang = JSON.parse(localStorage.getItem('lang') || LanguageEnum.ar);

    const meetingTypeForm = this.formControls.find(
      (f) => f.formControlName === 'meetingTypeId'
    );
    if (meetingTypeForm)
      meetingTypeForm.options = [
        { id: 1, name: currentLang === 'ar' ? "أجتماع دوري" : "Periodic Meeting" },
        { id: 2, name: currentLang === 'ar' ? "أجتماع سنوي" : "Annual Meeting" },
      ];
    this.initializeForm();
    // Get meetingId and fundId from route parameters
    this.route.queryParams.subscribe((queryParams) => {
      this.meetingId = +queryParams['id'] || 0;
      this.fundId = +queryParams['fundId'] || 0;

      if (this.meetingId > 0) {
        this.loadMeetingData();
      } else {
        this.errorModalService.showError(this.translateService.instant('INVESTMENT_FUNDS.MEETING.INVALID_MEETING_ID'));
      }
      this.initializeBreadcrumbs();
      this.route.queryParams.subscribe((queryParams) => {
        this.fundId = +queryParams['fundId'] || 0;
        this.loadDefaultAttendees();
      });
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumbs(): void {
    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      { label: 'INVESTMENT_FUNDS.MEETING.MEETINGSTITLE', url:  `/admin/investment-funds/meetings?fundId=${this.fundId} ` },
      { label: 'INVESTMENT_FUNDS.MEETING.EDIT_MEETING', url: '', disabled: true },

   ];
   this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  private initializeForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }

      // Set default values
      if (control.formControlName === 'locationType') {
        formGroup[control.formControlName] = [MeetingPlaceEnum.MeetingRoom, validators];
      } else if (control.formControlName === 'meetingStatusId') {
        formGroup[control.formControlName] = [2, validators]; // Default to Scheduled status
      } else {
        formGroup[control.formControlName] = [null, validators];
      }
    });

    this.formGroup = this.formBuilder.group(formGroup);
  }

  meetingPlaceChanged($event: any) {
    if ($event.event.value == MeetingPlaceEnum.MeetingRoom) {
      this.isMeetingRoom = true;
    } else {
      this.isMeetingRoom = false;
    }
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  addMeetingAgendaItem(): void {
    const dialogRef = this.dialog.open(MeetingAgendaPopupComponent, {
      width: '600px',
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Create new agenda item
        const newItem: MeetingAgendaItemDto = new MeetingAgendaItemDto({
          id: 0, // New item
          itemSubject: result.subject,
          itemDescription: result.description
        });

        this.agendaItems.push(newItem);
      }
    });
  }

  /**
   * Load meeting data for editing using meeting session API
   */
  loadMeetingData(): void {
    this.isLoading = true;

    this.meetingServiceProxy.meetingSession(this.meetingId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: MeetingSessionResponseDtoBaseResponse) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.meetingData = response.data;
            this.populateForm(response.data);
            if (response.data.attendees != undefined)
              response.data.attendees.forEach((attendee: any) => {
                this.selectedAttendees.push(attendee.boardMemberId);
              });

          } else {
            this.errorModalService.showError(this.translateService.instant(response.message || 'INVESTMENT_FUNDS.MEETING.LOAD_ERROR'));
          }
        },
        error: (error: any) => {
          this.isLoading = false;
          this.errorModalService.showError(error.parsedMessage);
          console.error('Error loading meeting session data:', error);
        }
      });
  }

  /**
   * Populate form with meeting session data
   */
  private populateForm(meetingData: MeetingSessionResponseDto): void {
    if (this.meetingData?.locationType == MeetingPlaceEnum.MeetingRoom) {
      this.isMeetingRoom = true;
    } else {
      this.isMeetingRoom = false;
    }
    // Handle meeting date - ensure it's properly converted
    let meetingDate = null;
    if (meetingData.meetingDate) {
      try {
        // If meetingDate is a DateTime object, convert to JS Date
        if (meetingData.meetingDate.toJSDate) {
          meetingDate = meetingData.meetingDate.toJSDate();
        } else {
          // If it's a string, parse it
          meetingDate = new Date(meetingData.meetingDate.toString());
        }
      } catch (error) {
        console.error('Error parsing meeting date:', error);
        meetingDate = new Date(); // Fallback to current date
      }
    }

    // Populate form values with available data
    this.formGroup.patchValue({
      subject: meetingData.subject || '',
      meetingTypeId: meetingData.meetingTypeId || 1,
      startTime: meetingData.startTime || '09:00',
      endTime: meetingData.endTime || '10:00',
      meetingRoomDetails: meetingData.meetingRoomDetails || '',
      onlineMeetingLink: meetingData.onlineMeetingLink || '',
      description: meetingData.description || '',
      locationType: meetingData.locationType,
      attachmentId: null // Will be handled separately
    });
    this.setDateValues();
    // Set agenda items from session data
    this.agendaItems = meetingData.agendaItems?.map(agendaItem => {
      const dto = new MeetingAgendaItemDto();
      dto.id = agendaItem.id;
      dto.itemSubject = agendaItem.itemSubject;
      dto.itemDescription = agendaItem.itemDescription;
      return dto;
    }) || [];

    // Store attendees for later processing
    this.selectedAttendees = meetingData.attendees?.map(attendee => {
      const dto = new MeetingAttendeeDto();
      dto.boardMemberId = attendee.boardMemberId;
      dto.fundManagerId = attendee.fundManagerId;
      dto.legalCouncilId = attendee.legalCouncilId;
      dto.boardSecretaryId = attendee.boardSecretaryId;
      dto.attendanceStatusId = attendee.attendanceStatusId;
      dto.isRequired = attendee.isRequired;
      dto.attendeeType = attendee.attendeeType;
      return dto;
    }) || [];

    // Apply member selections if members are already loaded
    if (this.membersLoaded && meetingData.attendees) {
      this.applyMeetingAttendeeSelections(meetingData.attendees);
    }

    // Set attachments from session data
    if (meetingData.attachments && meetingData.attachments.length > 0) {
      // Extract attachment IDs for form handling
      this.attachmentIds = meetingData.attachments.map(attachment => attachment.attachmentId);
      this.formControls.find(
        (c) => c.formControlName === 'attachmentId'
      )!.initialFiles = meetingData.attachments;
      console.log('Loaded attachments from session:', this.attachmentIds);
    } else {
      this.attachmentIds = [];
    }

  }

  setDateValues(): void {
    const isoString: any = this.meetingData?.meetingDate;
    if (!isoString) return;
    const luxonDate = DateTime.fromISO(isoString);
    const dateString = luxonDate.toISODate(); // This is now a string
    moment.locale('en');
    const formattedDate = moment(dateString).format('DD-MM-YYYY');
    const meetingDate = this.DateConversionService.mapStringToSelectedDate(formattedDate);
    this.formGroup.get('meetingDate')?.setValue(meetingDate);
  }

  /**
   * Update members selection based on attendees
   */
  private updateMembersSelection(): void {
    this.members.forEach((member: any) => {
      member.selected = this.selectedAttendees.some(
        (attendee: MeetingAttendeeDto) => attendee.boardMemberId === parseInt(member.id)
      );
    });
  }

  onFileUpload($event: any): void {
    this.attachmentIds=[];
    console.log('File upload event received:', $event);

    if ($event == null) {
      console.log('No file upload event data');
      return;
    }

    // Handle when $event.file is a list of objects containing IDs
    if ($event.file && Array.isArray($event.file)) {
      console.log('Multiple files uploaded as array:', $event.file);

      $event.file.forEach((fileObj: any) => {
        // Extract ID from file object (could be fileObj.id or fileObj.attachmentId)
        const attachmentId = fileObj.id || fileObj.attachmentId || fileObj;

        if (attachmentId && !this.attachmentIds.includes(attachmentId)) {
          this.attachmentIds.push(attachmentId);
          console.log('Added attachment ID:', attachmentId);
        } else if (attachmentId) {
          console.log('Attachment ID already exists:', attachmentId);
        } else {
          console.warn('No valid ID found in file object:', fileObj);
        }
      });
    }
    // Handle single file object
    else if ($event.file && typeof $event.file === 'object') {
      console.log('Single file uploaded as object:', $event.file);

      const attachmentId = $event.file.id || $event.file.attachmentId;

      if (attachmentId && !this.attachmentIds.includes(attachmentId)) {
        this.attachmentIds.push(attachmentId);
        console.log('Added attachment ID:', attachmentId);
      } else if (attachmentId) {
        console.log('Attachment ID already exists:', attachmentId);
      } else {
        console.warn('No valid ID found in file object:', $event.file);
      }
    }
    // Handle direct ID value
    else if ($event.file) {
      console.log('File uploaded as direct value:', $event.file);

      const attachmentId = $event.file;

      if (!this.attachmentIds.includes(attachmentId)) {
        this.attachmentIds.push(attachmentId);
        console.log('Added attachment ID:', attachmentId);
      } else {
        console.log('Attachment ID already exists:', attachmentId);
      }
    }
    else {
      console.warn('Unexpected file upload event structure:', $event);
    }

    // Update form control with current attachment IDs
    this.formGroup.get('attachmentId')?.setValue(this.attachmentIds);

    console.log('Current attachment IDs:', this.attachmentIds);
    console.log('Total files attached:', this.attachmentIds.length);
  }

  /**
   * Remove attachment ID from the list
   */
  removeAttachment(attachmentId: number): void {
    const index = this.attachmentIds.indexOf(attachmentId);
    if (index > -1) {
      this.attachmentIds.splice(index, 1);
      console.log('Removed attachment ID:', attachmentId);
      console.log('Current attachment IDs:', this.attachmentIds);
    }
  }


  dateSelected($event: any) {
    // Handle date selection from form builder
    console.log('Date selected from form builder - Full event:', $event);

    // The form builder sends { event, control } structure where event is DateValues object
    let dateValues = null;
    let controlName = '';

    if ($event && typeof $event === 'object') {
      // Extract from form builder event structure
      if ($event.event !== undefined) {
        dateValues = $event.event; // This should be a DateValues object
        controlName = $event.control?.formControlName || '';
        console.log('Form builder event structure - dateValues:', dateValues, 'control:', controlName);
      } else {
        // Direct DateValues object
        dateValues = $event;
        console.log('Direct DateValues object:', dateValues);
      }
    }

    // Process the DateValues object to extract a usable date
    let finalDateValue = null;

    if (dateValues && dateValues.gregorian) {
      // Convert NgbDateStruct to JavaScript Date
      const gregDate = dateValues.gregorian;
      if (gregDate.year && gregDate.month && gregDate.day) {
        // Create JavaScript Date (month is 0-based in JS Date, but 1-based in NgbDateStruct)
        finalDateValue = new Date(gregDate.year, gregDate.month - 1, gregDate.day);
        console.log('Converted Gregorian date to JS Date:', finalDateValue);

        // Also try the formatted string approach
        const formattedDate = dateValues.formattedGregorian ||
          `${gregDate.year}-${String(gregDate.month).padStart(2, '0')}-${String(gregDate.day).padStart(2, '0')}`;
        console.log('Formatted date string:', formattedDate);

        // Use the formatted string for the form (ISO format)
        finalDateValue = formattedDate;
      }
    } else if (dateValues && dateValues.formattedGregorian) {
      // Use the formatted Gregorian date string
      finalDateValue = dateValues.formattedGregorian;
      console.log('Using formatted Gregorian date:', finalDateValue);
    }

    console.log('Final processed date value:', finalDateValue);

    // Update the form control if we have a valid date
    if (finalDateValue) {
      // If we know the specific control name, use it; otherwise default to meetingDate
      const formControlName = controlName || 'meetingDate';
      const formControl = this.formGroup.get(formControlName);

      if (formControl) {
        formControl.setValue(finalDateValue);
        console.log(`Updated ${formControlName} form control to:`, finalDateValue);

        // Verify the form value was actually updated
        setTimeout(() => {
          const updatedValue = this.formGroup.get(formControlName)?.value;
          console.log(`Verified ${formControlName} form value:`, updatedValue);

          // Trigger conflict checking if this is the meeting date
          if (formControlName === 'meetingDate') {
            this.checkUserAvailability();
          }
        }, 200);
      } else {
        console.error(`Form control ${formControlName} not found`);
      }
    } else {
      console.warn('No valid date value extracted from DateValues object');
    }
  }

  onValueChange($event: any, control: IControlOption): void {
    console.log('Value changed:', $event, control);
    if(control.formControlName === 'startTime' || control.formControlName === 'endTime'){
      this.checkUserAvailability();
    }
  }



  /**
   * Validation method
   */
  private validateMeetingData(): { isValid: boolean; errorMessage: string } {
    const formData = this.formGroup.value;

    // Check mandatory fields
    if (!formData.subject || !formData.meetingTypeId || !formData.meetingDate ||
      !formData.startTime || !formData.endTime || !formData.locationType) {
      return { isValid: false, errorMessage: 'INVESTMENT_FUNDS.MEETING.MSG_CREATE_001' };
    }

    // Check if date is in the future
    const meetingDate = new Date(formData.meetingDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (meetingDate <= today) {
      return { isValid: false, errorMessage: 'INVESTMENT_FUNDS.MEETING.MSG_CREATE_002' };
    }

    // Check if end time is after start time
    const formattedStartTime = formData.startTime.includes(':') ? formData.startTime : this.formatTime(formData.startTime);
    const formattedEndTime = formData.endTime.includes(':') ? formData.endTime : this.formatTime(formData.endTime);

    if (!formattedStartTime || !formattedEndTime) {
      return { isValid: false, errorMessage: 'INVESTMENT_FUNDS.MEETING.MSG_CREATE_001' }; // Invalid time format
    }

    const startTime = this.parseTime(formattedStartTime);
    const endTime = this.parseTime(formattedEndTime);

    if (endTime <= startTime) {
      return { isValid: false, errorMessage: 'INVESTMENT_FUNDS.MEETING.MSG_CREATE_003' };
    }

    // Check meeting room field if place is "Meeting Room"
    if (formData.locationType === MeetingPlaceEnum.MeetingRoom && !formData.meetingRoomDetails) {
      return { isValid: false, errorMessage: 'INVESTMENT_FUNDS.MEETING.MSG_CREATE_001' };
    }

    // Check if at least one agenda item exists
    if (this.agendaItems.length === 0) {
      return { isValid: false, errorMessage: 'INVESTMENT_FUNDS.MEETING.MSG_CREATE_005' };
    }

    return { isValid: true, errorMessage: '' };
  }

  /**
   * Submit the form to update the meeting
   */
  onSubmit(isDraft: boolean = false): void {
    // Prevent multiple submissions
    if (this.isSubmitting) {
      return;
    }

    this.isValidationFire = true;

    const validationResult = this.validateMeetingData();
    if (!validationResult.isValid) {
      this.errorModalService.showError(this.translateService.instant(validationResult.errorMessage));
      return;
    }

    if (this.meetingId <= 0) {
      this.errorModalService.showError(this.translateService.instant('INVESTMENT_FUNDS.MEETING.INVALID_MEETING_ID'));
      return;
    }
    const hasAnyConflict = [...this.userConflicts.values()].some(value => value === true);

    if (hasAnyConflict) {
      this.errorModalService.showError(this.translateService.instant('INVESTMENT_FUNDS.MEETING.MSG_CREATE_004'));
      return;
    }
    this.isSubmitting = true;

    const formData = this.formGroup.value;
    const command = this.mapFormToCommand(formData, isDraft);

    this.meetingServiceProxy.editMeeting(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isSubmitting = false;
          if (response.successed) {
            this.errorModalService.showSuccess(this.translateService.instant('INVESTMENT_FUNDS.MEETING.UPDATE_SUCCESS'));
            this.showSuccessAndNavigate();
          } else {
            this.errorModalService.showError(this.translateService.instant(response.message || 'INVESTMENT_FUNDS.MEETING.UPDATE_ERROR'));
          }
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.errorModalService.showError(error.parsedMessage);
          console.error('Error updating meeting:', error);
        }
      });
  }

  /**
   * Map form data to EditMeetingCommand
   */
  private mapFormToCommand(formData: any, isDraft: boolean): EditMeetingCommand {
    const command = new EditMeetingCommand();

    command.id = this.meetingId;
    command.fundId = this.fundId;
    command.subject = formData.subject;
    command.description = formData.description;
    command.meetingTypeId = formData.meetingTypeId || this.meetingData?.meetingTypeId || 1;

    // Convert date and time
    if (formData.meetingDate) {
      command.meetingDate = DateTime.fromJSDate(new Date(formData.meetingDate));
    } else if (this.meetingData?.meetingDate) {
      command.meetingDate = this.meetingData.meetingDate;
    } else {
      command.meetingDate = DateTime.now();
    }

    command.startTime = this.formatTime(formData.startTime) || '09:00';
    command.endTime = this.formatTime(formData.endTime) || '10:00';

    // Location type mapping
    // Location type mapping
    command.locationType = formData.locationType === MeetingPlaceEnum.MeetingRoom
      ? MeetingLocationType._2
      : MeetingLocationType._1;

    command.meetingRoomDetails = formData.meetingRoomDetails;
    command.onlineMeetingLink = formData.onlineMeetingLink;

    // Status: 1 = Draft, 2 = Scheduled
    command.meetingStatusId = isDraft ? 1 : (this.meetingData?.meetingStatusId || 2);

    // Preserve existing names if available
    command.fundName = this.meetingData?.fundName;

    // Add agenda items
    command.agendaItems = this.agendaItems.length > 0 ? this.agendaItems : undefined;

    // Add attendees
    command.attendees = this.getSelectedAttendees();

    // Add attachments (use form data or stored attachment IDs)
    command.attachments = this.attachmentIds.length > 0 ? [...this.attachmentIds] : undefined;

    return command;
  }

  /**
   * Cancel form and navigate back
   */
  onCancel(): void {
    this.router.navigate(['/admin/investment-funds/meetings'], {
      queryParams: { fundId: this.fundId  ,selectedTab:1}
    });
  }

  /**
   * Get selected attendees from the members list
   */
  private getSelectedAttendees(): MeetingAttendeeDto[] {
    const attendees: MeetingAttendeeDto[] = [];

    this.members.forEach((member: any) => {
      if (member.selected) {
        const attendee = new MeetingAttendeeDto();
        const memberId = parseInt(member.id);

        // Set the appropriate member ID based on role
        this.setMemberIdByRole(attendee, member.role, memberId);

        attendee.attendanceStatusId = 1; // Pending
        attendee.isRequired = true;
        attendee.attendeeType = member.role;
        attendees.push(attendee);
      }
    });

    return attendees;
  }

  /**
   * Set the appropriate member ID property based on the member's role
   */
  private setMemberIdByRole(attendee: MeetingAttendeeDto, role: string, memberId: number): void {
    // Normalize role string for comparison (case-insensitive)
    const normalizedRole = role?.toLowerCase().trim() || '';

    console.log(`Setting member ID ${memberId} for role: ${role} (normalized: ${normalizedRole})`);

    // Map roles to appropriate ID properties
    if (normalizedRole.includes('fund manager') || normalizedRole.includes('fundmanager')) {
      attendee.fundManagerId = memberId;
      console.log(`Set fundManagerId: ${memberId}`);
    }
    else if (normalizedRole.includes('legal council') || normalizedRole.includes('legal counsel') ||
      normalizedRole.includes('legalcouncil') || normalizedRole.includes('legalcounsel')) {
      attendee.legalCouncilId = memberId;
      console.log(`Set legalCouncilId: ${memberId}`);
    }
    else if (normalizedRole.includes('board secretary') || normalizedRole.includes('boardsecretary')) {
      attendee.boardSecretaryId = memberId;
      console.log(`Set boardSecretaryId: ${memberId}`);
    }
    else if (normalizedRole.includes('board member') || normalizedRole.includes('boardmember') ||
      normalizedRole.includes('board') || normalizedRole === 'member') {
      attendee.boardMemberId = memberId;
      console.log(`Set boardMemberId: ${memberId}`);
    }
    else {
      // Default to boardMemberId for unknown roles
      attendee.boardMemberId = memberId;
      console.log(`Unknown role "${role}", defaulting to boardMemberId: ${memberId}`);
    }
  }
  /**
   * Show success message and navigate to meetings list
   */
  private showSuccessAndNavigate(): void {
    // Navigate immediately since toastr will show the success message
    this.router.navigate(['/admin/investment-funds/meetings'], {
      queryParams: { fundId: this.fundId  ,selectedTab:1}
    });
  }
  /**
   * Delete agenda item
   */
  deleteAgendaItem(index: number): void {
    if (index < 0 || index >= this.agendaItems.length) {
      return;
    }

    const agendaItem = this.agendaItems[index];
    const title = this.translateService.instant('COMMON.CONFIRM_DELETE');
    const message = this.translateService.instant('INVESTMENT_FUNDS.MEETING.AGENDA_DELETE_CONFIRM', {
      title: agendaItem.itemSubject || 'Agenda Item'
    });

    const dialogData: CancelMeetingDialogData = {
      meetingSubject: agendaItem.itemSubject || 'Agenda Item',
      title: title,
      message: message
    };

    const dialogRef = this.dialog.open(CancelMeetingDialogComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.agendaItems.splice(index, 1);
      }
    });
  }

  /**
   * Edit existing agenda item
   */
  editAgendaItem(index: number): void {
    if (index < 0 || index >= this.agendaItems.length) {
      return;
    }

    const itemToEdit = this.agendaItems[index];

    const dialogRef = this.dialog.open(MeetingAgendaPopupComponent, {
      width: '600px',
      data: {
        isEdit: true,
        agendaItem: { ...itemToEdit }, // Pass a copy
        existingItems: this.agendaItems.filter((_, i) => i !== index) // Exclude current item from validation
      },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Update the existing item
        this.agendaItems[index].itemSubject = result.subject;
        this.agendaItems[index].itemDescription = result.description;
      }
    });
  }

  /**
   * Check user availability for conflicts using meetingCheckUserAvailability API
   */
  checkUserAvailability(): void {
    const formData = this.formGroup.value;

    console.log('Checking user availability with form data:', formData);

    // Validate required fields
    if (!formData.meetingDate || !formData.startTime || !formData.endTime) {
      console.log('Missing required fields for conflict checking:', {
        meetingDate: formData.meetingDate,
        startTime: formData.startTime,
        endTime: formData.endTime
      });
      return;
    }

    // Get selected users
    const selectedUsers = this.members.filter((member: any) => member.selected);
    if (selectedUsers.length === 0) {
      console.log('No users selected for conflict checking');
      this.userConflicts.clear();
      return;
    }

    // Prepare user IDs
    const userIds = selectedUsers.map((member: any) => parseInt(member.userId));
    console.log('Selected user IDs for conflict checking:', userIds);

    // Validate and parse the meeting date
    let meetingDateTime: DateTime;
    try {
      meetingDateTime = DateTime.fromJSDate(new Date(formData.meetingDate));
      if (!meetingDateTime.isValid) {
        console.error('Invalid meeting date:', formData.meetingDate);
        return;
      }
    } catch (error) {
      console.error('Error parsing meeting date:', formData.meetingDate, error);
      return;
    }

    // Format time values to ensure proper HH:MM format
    const formattedStartTime = this.formatTime(formData.startTime) || '09:00';
    const formattedEndTime = this.formatTime(formData.endTime) || '10:00';

    console.log('Formatted times for API:', {
      originalStartTime: formData.startTime,
      formattedStartTime: formattedStartTime,
      originalEndTime: formData.endTime,
      formattedEndTime: formattedEndTime
    });

    // Create the query object
    const query = new CheckUserAvailabilityQuery({
      meetingDate: meetingDateTime,
      startTime: formattedStartTime,
      endTime: formattedEndTime,
      userIds: userIds,
      meetingId: this.meetingId
    });

    console.log('Calling meetingCheckUserAvailability API with query:', query);
    this.isCheckingConflicts = true;

    // Call the API
    this.meetingServiceProxy.meetingCheckUserAvailability(query)
    .subscribe({
      next: (response: UserAvailabilityCheckResponseDtoBaseResponse) => {
        console.log('User availability check response:', response);
        this.processAvailabilityResponse(response);
        this.isCheckingConflicts = false;
      },
      error: (error) => {
        console.error('Error checking user availability:', error);
        this.isCheckingConflicts = false;
        this.errorModalService.showError(error.parsedMessage);
        // Clear conflicts on error
        this.userConflicts.clear();
      }
    });
  }

  /**
   * Process the availability response and update conflict status
   */
  private processAvailabilityResponse(response: UserAvailabilityCheckResponseDtoBaseResponse): void {
    this.userConflicts.clear();

    if (response.successed && response.data && response.data.userAvailabilities) {
      response.data.userAvailabilities.forEach(userAvailability => {
        if (userAvailability.userId && userAvailability.isAvailable !== undefined) {
          // Store conflict status (true if NOT available)
          this.userConflicts.set(userAvailability.userId, !userAvailability.isAvailable);
        }
      });
    }
  }

  /**
   * Check if a specific user has conflicts
   */
  hasUserConflict(userId: number): boolean {
    return this.userConflicts.get(userId) || false;
  }

  /**
   * Handle member selection change
   */
  onMemberSelectionChange(member: any): void {
    // Trigger conflict checking when member selection changes
    setTimeout(() => {
      this.checkUserAvailability();
    }, 100); // Small delay to ensure UI is updated
  }

  /**
   * Toggle member selection and update UI
   */
  toggleMemberSelection(event: any, member: any): void {
    member.selected = event.checked;
    this.onMemberSelectionChange(member);
    // Force change detection
    this.cdr.detectChanges();
  }

  /**
   * Apply meeting attendee selections to members after members are loaded
   */
  private applyMeetingAttendeeSelections(attendees: any[]): void {
    console.log('Applying meeting attendee selections:', attendees);
    console.log('Current members:', this.members);

    if (!attendees || attendees.length === 0) {
      console.log('No attendees to apply');
      return;
    }

    // Reset all members to unselected first
    this.members.forEach((member: any) => {
      member.selected = false;
    });

    // Set selected based on attendees
    attendees.forEach(attendee => {
      let memberId: string | null = null;

      // Determine which ID to use for matching
      if (attendee.boardMemberId) {
        memberId = attendee.boardMemberId.toString();
      } else if (attendee.fundManagerId) {
        memberId = attendee.fundManagerId.toString();
      } else if (attendee.legalCouncilId) {
        memberId = attendee.legalCouncilId.toString();
      } else if (attendee.boardSecretaryId) {
        memberId = attendee.boardSecretaryId.toString();
      }

      if (memberId) {
        const member = this.members.find((m: any) => m.id === memberId);
        if (member) {
          member.selected = true;
          console.log(`Selected member: ${member.fullName} (ID: ${member.id})`);
        } else {
          console.log(`Member not found for ID: ${memberId}`);
        }
      }
    });

    // Trigger change detection
    this.membersLoaded = true;
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 0);

    console.log('Members after applying selections:', this.members.map((m: any) => ({ id: m.id, name: m.fullName, selected: m.selected })));
  }

  /**
   * Handle date changes specifically
   */
  onDateChange(value: any): void {
    console.log('Date changed to:', value);

    // Update the form control value explicitly if needed
    if (value && this.formGroup.get('meetingDate')) {
      this.formGroup.get('meetingDate')?.setValue(value);
      console.log('Updated form control meetingDate to:', value);
    }

    // Trigger conflict checking with delay
    this.onDateTimeChange();
  }

  /**
   * Handle date/time changes
   */
  onDateTimeChange(): void {
    // Trigger conflict checking when date or time changes with proper delay
    // to ensure form value is updated
    setTimeout(() => {
      // Double-check that form values are available before calling API
      const formData = this.formGroup.value;
      console.log('Date/Time changed - Form values:', {
        meetingDate: formData.meetingDate,
        startTime: formData.startTime,
        endTime: formData.endTime
      });

      // Only call API if we have the required values
      if (formData.meetingDate && formData.startTime && formData.endTime) {
        this.checkUserAvailability();
      } else {
        console.log('Skipping conflict check - missing required date/time values');
      }
    }, 500); // Increased delay to ensure form value is properly updated
  }

  private formatTime(timeValue: any): string | null {
    if (!timeValue) {
      return null;
    }

    // If it's already a string, handle different formats
    if (typeof timeValue === 'string') {
      // Check if it matches HH:MM or H:MM format
      const timeRegex = /^(\d{1,2}):(\d{2}):(\d{2})$/;
      const match = timeValue.match(timeRegex);

      if (match) {
        const hours = parseInt(match[1], 10);
        const minutes = parseInt(match[2], 10);

        // Validate hours (0-23) and minutes (0-59)
        if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
          // Ensure two-digit format
          return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }
      }

      // Check if it's in HHMM format (e.g., "2248" for 22:48)
      if (/^\d{4}$/.test(timeValue)) {
        const hours = parseInt(timeValue.substring(0, 2), 10);
        const minutes = parseInt(timeValue.substring(2, 4), 10);

        // Validate hours (0-23) and minutes (0-59)
        if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
          return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }
      }

      // Check if it's in HMM format (e.g., "948" for 09:48)
      if (/^\d{3}$/.test(timeValue)) {
        const hours = parseInt(timeValue.substring(0, 1), 10);
        const minutes = parseInt(timeValue.substring(1, 3), 10);

        // Validate hours (0-9) and minutes (0-59)
        if (hours >= 0 && hours <= 9 && minutes >= 0 && minutes <= 59) {
          return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }
      }
    }

    // If it's a Date object, extract time
    if (timeValue instanceof Date) {
      const hours = timeValue.getHours();
      const minutes = timeValue.getMinutes();
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    // If it's an object with time properties
    if (typeof timeValue === 'object' && timeValue.hours !== undefined && timeValue.minutes !== undefined) {
      const hours = parseInt(timeValue.hours, 10);
      const minutes = parseInt(timeValue.minutes, 10);

      if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      }
    }

    // Try to parse as a number (minutes since midnight)
    if (typeof timeValue === 'number') {
      // First, check if it's in HHMM format as a number (e.g., 2248 for 22:48)
      if (timeValue >= 0 && timeValue <= 2359) {
        const hours = Math.floor(timeValue / 100);
        const minutes = timeValue % 100;

        // Validate hours (0-23) and minutes (0-59)
        if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
          return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }
      }

      // Otherwise, treat as minutes since midnight
      const hours = Math.floor(timeValue / 60);
      const minutes = timeValue % 60;

      if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      }
    }

    console.warn('Invalid time format:', timeValue);
    return null;
  }
  /**
    * Load default attendees as per business rules
    */
  private loadDefaultAttendees(): void {
    if (!this.fundId || this.fundId <= 0) {
      console.warn('Invalid fundId for loading default attendees');
      return;
    }

    // JDWA-1935: Load all fund users for this specific fund
    this.isLoading = true;

    console.log('Loading all fund users for fundId:', this.fundId);

    // Call GetAllFundUsers API with fundId
    this.userManagementServiceProxy.getAllFundUsers(
      this.fundId,     // fundId - REQUIRED
      1,               // pageNumber
      100,             // pageSize (get all users)
      undefined,       // search (no search filter)
      undefined        // orderBy (default ordering)
    ).subscribe({
      next: (response: GetUserListResponsePaginatedResult) => {
        console.log('GetAllFundUsers API response:', response);
        this.isLoading = false;

        if (response.successed && response.data && response.data.length > 0) {
          // Convert API response to members format
          this.members = response.data.map(user => ({
            id: user.id.toString(),
            fullName: user.fullName || user.userName || 'Unknown User',
            role: this.getUserPrimaryRole(user),
            email: user.email || '',
            userId: user.userId,
            selected: false // Start with false, will be set based on meeting data
          }));

          console.log('Loaded fund users:', this.members.length);
          console.log('Members data:', this.members);

          // Store default attendees for reference
          this.defaultAttendees = [...this.members];

          // Set flag that members are loaded
          this.membersLoaded = true;

          // Now that members are loaded, apply meeting attendee selections if we have meeting data
          if (this.meetingData && this.meetingData.attendees) {
            this.applyMeetingAttendeeSelections(this.meetingData.attendees);
          }
        } else {
          console.warn('No fund users found or API call failed');
          this.members = [];
          this.defaultAttendees = [];
        }
      },
      error: (error: any) => {
        console.error('Error loading all fund users for fund:', this.fundId, error);
        this.isLoading = false;
        this.errorModalService.showError(error.parsedMessage);

        // Clear members on error
        this.members = [];
        this.defaultAttendees = [];
      }
    });
  }
  private getUserPrimaryRole(user: GetUserListResponse): string {
    if (user.primaryRole) {
      return user.primaryRole;
    }
    if (user.enRoles && user.enRoles.length > 0) {
      return user.enRoles[0];
    }
    return 'Member';
  }
  private parseTime(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }
  getRoleName(key:any): string{
    return this.translateService.instant(key.toLowerCase());
   }
}
