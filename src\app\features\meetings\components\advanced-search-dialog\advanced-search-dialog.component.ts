import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { TranslateModule } from '@ngx-translate/core';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";
import { FormBuilderComponent } from "@shared/components/form-builder/form-builder.component";
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';

@Component({
  selector: 'app-advanced-search-dialog',
  standalone: true,
  imports: [CustomButtonComponent, FormBuilderComponent,
        ReactiveFormsModule,
        MatDialogModule,
        TranslateModule,CommonModule
  ],
  templateUrl: './advanced-search-dialog.component.html',
  styleUrl: './advanced-search-dialog.component.scss'
})
export class AdvancedSearchDialogComponent {
   formGroup!: FormGroup;
   isFormSubmitted = false;
   buttonEnum = ButtonTypeEnum;
   IconEnum = IconEnum;

   formControls: IControlOption[] = [
     {
       type: InputType.Dropdown,
       formControlName: 'resolutionType',
       id: 'resolutionType',
       name: 'resolutionType',
       label: 'INVESTMENT_FUNDS.RESOLUTIONS.FILTER_BY_TYPE',
       placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ALL_TYPES',
       isRequired: false,
       class: 'col-md-12',
       options: [], // Will be populated dynamically from API
     },
   ];

   constructor(
     public dialogRef: MatDialogRef<AdvancedSearchDialogComponent>,
     @Inject(MAT_DIALOG_DATA) public data: any,
     private formBuilder: FormBuilder,
   ) {
     this.initForm();
   }

   ngOnInit(): void {

   }


   private initForm(): void {
     this.formGroup = this.formBuilder.group({
       search: [''],
       status: [''],
       resolutionType: [''],
       fromDate: [''], // Set today's date as default
       toDate: [''], // Set today's date as default
       createdBy: [''],
     });
   }



   dropdownChanged(event: any): void {
     // Handle dropdown changes if needed
     console.log('Dropdown changed:', event);
   }

   applyFilters(): void {
     this.isFormSubmitted = true;
     if (this.formGroup.valid) {
       const filters = this.formGroup.value;
       console.log('Raw form values:', filters);

       // Remove empty values
       const cleanFilters = Object.keys(filters).reduce((acc: any, key) => {
         const value = filters[key];
         console.log(
           `Checking filter key: ${key}, value: ${value}, type: ${typeof value}`
         );

         if (value !== '' && value !== null && value !== undefined) {
           acc[key] = value;
           console.log(`Added ${key} to clean filters:`, value);
         } else {
           console.log(
             `Filtered out ${key} because it was empty/null/undefined`
           );
         }
         return acc;
       }, {});

       console.log('Clean filters being sent:', cleanFilters);
       this.dialogRef.close(cleanFilters);
     }
   }

   resetFilters(): void {
     this.formGroup.reset();
     this.isFormSubmitted = false;
   }

   closeDialog(): void {
     this.dialogRef.close();
   }













}
