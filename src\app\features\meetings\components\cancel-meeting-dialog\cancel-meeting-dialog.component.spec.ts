import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';

import { CancelMeetingDialogComponent } from './cancel-meeting-dialog.component';

describe('CancelMeetingDialogComponent', () => {
  let component: CancelMeetingDialogComponent;
  let fixture: ComponentFixture<CancelMeetingDialogComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<CancelMeetingDialogComponent>>;

  beforeEach(async () => {
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        CancelMeetingDialogComponent,
        TranslateModule.forRoot()
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            meetingSubject: 'Test Meeting',
            title: 'Cancel Meeting',
            message: 'Are you sure you want to cancel this meeting?'
          }
        }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CancelMeetingDialogComponent);
    component = fixture.componentInstance;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<CancelMeetingDialogComponent>>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should close dialog with true when confirmed', () => {
    component.onConfirm();
    expect(mockDialogRef.close).toHaveBeenCalledWith(true);
  });

  it('should close dialog with false when cancelled', () => {
    component.onCancel();
    expect(mockDialogRef.close).toHaveBeenCalledWith(false);
  });
});
