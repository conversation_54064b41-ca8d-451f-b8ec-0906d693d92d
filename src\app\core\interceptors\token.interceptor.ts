import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { SpinnerService } from '@core/gl-services/spinner-services/spinner.service';
import { catchError, EMPTY, finalize, throwError } from 'rxjs';

export const tokenInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);
  const loader = inject(SpinnerService);

  const token = localStorage.getItem('auth_token');
  const lang = localStorage.getItem('lang') || 'ar';

  // Parse the stored language properly (it's JSON stringified)
  let currentLang = 'ar'; // Default fallback

  try {
    if (lang) {
      // Try to parse as JSON first (in case it's stringified)
      try {
        const parsedLang = JSON.parse(lang);
        if (typeof parsedLang === 'string') {
          currentLang = parsedLang.toLowerCase().trim();
        }
      } catch (jsonError) {
        // If JSON parsing fails, use the raw value
        currentLang = lang.toLowerCase().trim();
      }

      // Remove quotes if they exist
      currentLang = currentLang.replace(/['"]/g, '');
    }
  } catch (e) {
    console.warn('Error processing language from localStorage:', e);
    currentLang = 'ar';
  }

  // Map to proper locale codes
  const language = currentLang === 'en' ? 'en-US' : 'ar-EG';


  // Start loader
  loader.show();

  req = req.clone({
    setHeaders: {
      ...(token && { Authorization: `Bearer ${token}` }),
      'Accept-Language': language,
    },
  });

  return next(req).pipe(
    catchError((error) => {
      debugger;
      if (error.status === 401) {
        router.navigate(['/admin/unauthorized']);
        return EMPTY;
      }
      return throwError(() => error);
    }),
    finalize(() => {
      loader.hide();
    })
  );
};
