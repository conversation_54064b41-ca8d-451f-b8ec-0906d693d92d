import { Component,Input } from '@angular/core';
import{CommonModule} from '@angular/common';
import { ResolutionStatus } from '@shared/enum/resolution-status';
import { DateHijriConverterPipe } from "../../../../shared/pipes/dateHijriConverter/dateHijriConverter.pipe";
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateConversionService } from '@shared/services/date.service';

@Component({
  selector: 'app-timeline',
  standalone: true,
  imports: [CommonModule, DateHijriConverterPipe,TranslateModule],
  templateUrl: './timeline.component.html',
  styleUrl: './timeline.component.scss'
})
export class TimelineComponent {
  resolutionStatus =ResolutionStatus;
  @Input() steps:any ;

  constructor(private translateService: TranslateService,private DateConversionService: DateConversionService) {}
  ngOnInit() {

      this.steps = this.steps?.map((step:any) => ({
        ...step,
        formattedChangeDate: step.formattedChangeDate || '',
        hijriDate: step.hijriDate || this.DateConversionService.convertGregorianToHijri(
    step.changedAt
    ),
        actionName: step.actionName || this.mapActionName(step.notes),
        userRole: step.userRole || this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.ROLES.FUND_MANAGER'),
        statusText: step.statusText || this.mapStatusText(step.newStatus || step.statusId),
        iconClass: step.iconClass || this.mapIcon(step.statusId),
        color: step.color || this.mapColor(step.statusId),
        reason: step.reason || ''
      }));
  }

  mapActionName(notes: string): string {
    switch (notes) {
      case 'AuditActionResolutionCreation':
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.ACTIONS.RESOLUTION_CREATION');
      case 'AuditActionResolutionDataUpdate':
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.ACTIONS.RESOLUTION_DATA_UPDATE');
      case 'AuditActionResolutionRejected':
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.ACTIONS.RESOLUTION_REJECTED');
      case 'AuditActionResolutionApproved':
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.ACTIONS.RESOLUTION_APPROVED');
      default:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.ACTIONS.RESOLUTION_UPDATE');
    }
  }

  mapStatusText(statusId: number): string {
    switch (statusId) {
      case this.resolutionStatus.Draft:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.DRAFT');
      case this.resolutionStatus.Pending:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.PENDING');
      case this.resolutionStatus.CompletingData:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.COMPLETING_DATA');
      case this.resolutionStatus.WaitingForConfirmation:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.WAITING_FOR_CONFIRMATION');
      case this.resolutionStatus.Confirmed:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.CONFIRMED');
      case this.resolutionStatus.Rejected:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.REJECTED');
      case this.resolutionStatus.VotingInProgress:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.VOTING_IN_PROGRESS');
      case this.resolutionStatus.Approved:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.APPROVED');
      case this.resolutionStatus.NotApproved:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.NOT_APPROVED');
      case this.resolutionStatus.Cancelled:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.CANCELLED');
      default:
        return this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.TIMELINE.STATUSES.UNKNOWN');
    }
  }

  mapIcon(statusId: number): string {
    switch (statusId) {
      case this.resolutionStatus.Rejected: return 'Cricle.png';
      case this.resolutionStatus.CompletingData: return 'StepperBaseblue.png';
      case this.resolutionStatus.Draft: return 'StepperBaseOrange.png';
      default: return 'Default.png';
    }
  }

  mapColor(statusId: number): string {
    switch (statusId) {
      case this.resolutionStatus.Rejected: return '#e74c3c';
      case this.resolutionStatus.CompletingData: return '#f39c12';
      case this.resolutionStatus.Draft: return '#3498db';
      default: return '#cccccc';
    }
  }

  getHijriDate(_gregorian: string): string {
    // Static fallback
    return '21 ذو القعدة 1446';
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.resolutionStatus.Draft:
        return 'draft';
      case this.resolutionStatus.Pending:
        return 'pending';
      case this.resolutionStatus.CompletingData:
        return 'completing-data';
      case this.resolutionStatus.WaitingForConfirmation:
        return 'waiting-for-confirmation';
      case this.resolutionStatus.Confirmed:
        return 'confirmed';
        case this.resolutionStatus.Rejected:
        return 'rejected';
      case this.resolutionStatus.VotingInProgress:
        return 'voting-inProgress';
      case this.resolutionStatus.Approved:
        return 'approved';
      case this.resolutionStatus.NotApproved:
        return 'not-approved';
      case this.resolutionStatus.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === 'en';
  }
}
