.unauthorized-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.unauthorized-content {
  text-align: center;
  max-width: 600px;
  padding: 60px 40px;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-image {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.unauthorized-svg {
  max-width: 300px;
  max-height: 300px;
  width: 100%;
  height: auto;
  animation: fadeInScale 0.8s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.error-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 20px 0;
}

.error-description {
  font-size: 1.2rem;
  color: #2c3e50;
  margin: 0 0 20px 0;
  line-height: 1.6;
}

.action-buttons {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
}

.help-text {
  font-size: 0.95rem;
  color: #868e96;
  margin: 0;
  font-style: italic;
}

/* RTL Support */
[dir="rtl"] .unauthorized-content {
  text-align: center;
}

[dir="rtl"] .action-buttons {
  flex-direction: row-reverse;
}

/* Responsive Design */
@media (max-width: 768px) {
  .unauthorized-container {
    padding: 15px;
  }

  .unauthorized-content {
    padding: 40px 25px;
  }

  .unauthorized-svg {
    max-width: 250px;
    max-height: 250px;
  }

  .error-title {
    font-size: 2rem;
  }

  .error-description {
    font-size: 1.1rem;
  }

  .current-date {
    font-size: 0.9rem;
    padding: 8px 15px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-buttons app-custom-button {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .unauthorized-svg {
    max-width: 200px;
    max-height: 200px;
  }

  .error-title {
    font-size: 1.5rem;
  }

  .error-description {
    font-size: 1rem;
  }

  .current-date {
    font-size: 0.85rem;
    padding: 6px 12px;
  }
}
