<div class="dialog-container">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <h2 class="dialog-title">{{ getDialogTitle() }}</h2>
    <button
      type="button"
      class="close-btn"
      (click)="onCancel()"
      [attr.aria-label]="'COMMON.CLOSE' | translate">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Dialog Content -->
  <div class="dialog-content">
    <!-- Assessment Info -->
    <div class="assessment-info mb-3">
      <p class="info-text">
        {{ 'ASSESSMENTS.REJECT_CONFIRMATION_MESSAGE' | translate }}
      </p>
    </div>

    <!-- Rejection Form -->
    <div class="form-section">
      <app-form-builder
        [formControls]="formControls"
        [formGroup]="formGroup"
        [isFormSubmitted]="isValidationFire">
      </app-form-builder>
    </div>
  </div>

  <!-- Dialog Actions -->
  <div class="dialog-actions">
    <app-custom-button
      [btnName]="'COMMON.CANCEL' | translate"
      (click)="onCancel()"
      [buttonType]="buttonEnum.Secondary"
      [iconName]="IconEnum.cancel"
      class="me-2">
    </app-custom-button>

    <app-custom-button
      [btnName]="'ASSESSMENTS.REJECT' | translate"
      (click)="onSubmit()"
      [buttonType]="buttonEnum.Danger"
      [iconName]="IconEnum.verify"
      [disabled]="isFormSubmitted">
    </app-custom-button>
  </div>
</div>
