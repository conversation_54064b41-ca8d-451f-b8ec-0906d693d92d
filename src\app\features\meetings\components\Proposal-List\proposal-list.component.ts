import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from "@shared/components/breadcrumb/breadcrumb.component";
import { PageHeaderComponent } from "@shared/components/page-header/page-header.component";
import { MatDialog } from '@angular/material/dialog';
import { Subject, takeUntil } from 'rxjs';
import {
  MeetingsProposalListDto,
  MeetingsProposalListDtoPaginatedResult,
  MeetingsProposalServiceProxy
} from '@core/api/api.generated';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { GeorgianDatePipe } from '@shared/pipes/georgian-date/georgian-date.pipe';
import { MeetingsProposalFilterComponent } from '../meetings-proposal-filter/meetings-proposal-filter.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';


@Component({
  selector: 'app-proposal-list',
  standalone: true,
  imports: [PageHeaderComponent, CommonModule, TranslateModule, GeorgianDatePipe,MatTooltipModule],
  templateUrl: './proposal-list.component.html',
  styleUrl: './proposal-list.component.scss'
})
export class ProposalListComponent implements OnInit, OnDestroy {
   breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  fundId = 0;

  // API Integration Properties
  MeetingsProposals: MeetingsProposalListDto[] = [];
  isLoading = false;
  hasError = false;
  errorMessage = '';
  currentPage = 1;
  pageSize = 12;
  totalCount = 0;
  totalPages = 0;
  pageSizeOptions = [10, 25, 50, 100];
  search = '';
  statusId?: number;

  // Lifecycle management
  private destroy$ = new Subject<void>();
  isHasPermissionAdd: any;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public translateService: TranslateService,
    private dialog: MatDialog,
    private MeetingsProposalServiceProxy: MeetingsProposalServiceProxy,
    private TokenService: TokenService,
   private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit() {
    this.isHasPermissionAdd = this.TokenService.hasPermission('MeetingProposal.Add');
    this.route.queryParams.subscribe((queryParams) => {
      this.fundId = +queryParams['fundId'] || 0;
      // Load meetings proposals after getting fundId
      this.loadMeetingsProposals();
    });
    this.initializeBreadcrumbs();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load meetings proposals from API
   */
  loadMeetingsProposals(): void {
    if (this.fundId <= 0) {
      this.hasError = true;
      this.errorMessage = 'INVESTMENT_FUNDS.MEETING.INVALID_FUND_ID';
      return;
    }

    this.isLoading = true;
    this.hasError = false;

    this.MeetingsProposalServiceProxy.getProposalsList(
      this.statusId,
      this.fundId,
      this.search || undefined,
      this.currentPage,
      this.pageSize,
      'id desc'
    )
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response: MeetingsProposalListDtoPaginatedResult) => {
        this.isLoading = false;
        if (response.successed && response.data) {
          this.MeetingsProposals = response.data;
          this.totalCount = response.totalCount;
          this.totalPages = response.totalPages;
          this.currentPage = response.currentPage;
        } else {
          this.hasError = true;
          this.errorMessage = response.message || 'INVESTMENT_FUNDS.MEETING.LOAD_ERROR';
        }
      },
      error: (error: any) => {
        this.isLoading = false;
        this.hasError = true;
        this.errorMessage = 'INVESTMENT_FUNDS.MEETING.NETWORK_ERROR';
        console.error('Error loading meetings proposals:', error);
      }
    });
  }


  private initializeBreadcrumbs() {
    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      { label: 'INVESTMENT_FUNDS.MEETING.MEETINGSTITLE', url: '', disabled: true },
   ];
   this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
   
  }
  /**
   * Handle search input
   */
  onSearch(searchTerm: string): void {
    this.search = searchTerm;
    this.currentPage = 1; // Reset to first page
    this.loadMeetingsProposals();
  }

  /**
   * Open advanced search/filter dialog
   */
  openFilter(): void {
    const dialogRef = this.dialog.open(MeetingsProposalFilterComponent, {
      width: '480px',
      data: {
        search: this.search,
        status: this.statusId,
        fromDate: '',
        toDate: '',
        createdBy: '',
      },
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        this.search = result.search || '';
        this.statusId = result.status;
        this.currentPage = 1; // Reset to first page
        this.loadMeetingsProposals();
      }
    });
  }
  /**
   * Navigate to add new meeting proposal
   */
  addNewProposalMeeting(): void {
    this.router.navigate(['/admin/investment-funds/meetings/add'], {
      queryParams: { fundId: this.fundId }
    });
  }

  /**
   * Navigate to meeting details view
   */
  viewMeetingDetails(meetingProposalId: number): void {
    if (!meetingProposalId) {
      console.error('Meeting proposal ID is required');
      return;
    }

    // Navigate to meeting details route with the proposal ID
    this.router.navigate([`/admin/investment-funds/meetings/proposed-meeting-vote-result`], {
      queryParams: {
        proposalId: meetingProposalId,
        fundId: this.fundId
      }
    });
  }

    VoteMeetingsProposal(meetingProposalId: number): void {
    if (!meetingProposalId) {
      console.error('Meeting proposal ID is required');
      return;
    }

    // Navigate to meeting details route with the proposal ID
    this.router.navigate([`/admin/investment-funds/meetings/vote`], {
      queryParams: {
        proposalId: meetingProposalId,
        fundId: this.fundId
      }
    });
  }

  // Pagination methods
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadMeetingsProposals();
    }
  }

  onPreviousPage(): void {
    if (this.canGoPrevious()) {
      this.onPageChange(this.currentPage - 1);
    }
  }

  onNextPage(): void {
    if (this.canGoNext()) {
      this.onPageChange(this.currentPage + 1);
    }
  }

  onFirstPage(): void {
    this.onPageChange(1);
  }

  onLastPage(): void {
    this.onPageChange(this.totalPages);
  }

  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.currentPage = 1; // Reset to first page when changing page size
    this.loadMeetingsProposals();
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === 'en';
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const halfRange = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfRange);
    let endPage = Math.min(this.totalPages, this.currentPage + halfRange);

    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxPagesToShow) {
      if (startPage === 1) {
        endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);
      } else {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  getRecordsInfo(): { start: number; end: number; total: number } {
    const start = (this.currentPage - 1) * this.pageSize + 1;
    const end = Math.min(this.currentPage * this.pageSize, this.totalCount);
    return { start, end, total: this.totalCount };
  }

  /**
   * Get CSS class for meeting proposal status
   */
  getStatusClass(statusId: number): string {
    switch (statusId) {
      case 1:
        return 'draft';
      case 2:
        return 'pending';
      case 3:
        return 'active';
      case 4:
        return 'completed';
      case 5:
        return 'cancelled';
      default:
        return 'unknown';
    }
  }
}
