@import "../../../../../assets/scss/variables";

.create-meeting-page {
  padding: 20px;
  min-height: 100vh;

  .breadcrumb-section {
    margin-bottom: 20px;
  }

  .page-header-section {
    margin-bottom: 30px;
  }

  .header-container {
    display: flex;
    .rotate-icon{
    cursor: pointer;
  }
  }

  .title-container {
    .title {
      color: $navy-blue;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;


    }

  }

   .span-value {
        display: flex;
              align-items: center;
              gap: 8px;
              height: 24px;
              border-radius: 20px;
              padding: 10px;

              font-size: 14px;
              font-weight: 400;
              line-height: 16px;

        &.under-vote{
        background: rgba(47, 128, 237, 0.1);
        color: #2f80ed;
      }

      &.complete {
        background: #f1faf1;
        color: #0e700e;
      }
      }

  .voting-container {
    background-color: $card-background;
    border: 1px solid $border-color;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:last-of-type {
      margin-bottom: 30px;
    }
  }

  .title {
    color: $navy-blue;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;

    .span-value {
      border-radius: 14px;
      background: rgba(38, 86, 135, 0.12);
      color: var(--Color---Black-1, #000);

      font-size: 16px;
      font-weight: 400;
      line-height: 18px;
      /* 112.5% */
      display: flex;
      padding: 8px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      margin-right: 10px;
      margin-left: 10px;
    }
  }

  .result-container {
    background-color: $card-background;
    border: 1px solid $border-color;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
.result{
  @media (max-width: 768px) {
    flex-direction: column;
     margin-bottom: 20px;
  }
}
    .member-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .member-avatar {
        .avatar-img {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          object-fit: cover;
          border: 1px solid #e5e7eb;
          background-color: #f9fafb;
        }
      }

      .member-details {
        .member-name {
          margin: 0 0 4px 0;

          color: #00205a;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
        }

        .member-role {
          margin: 0;
          color: #828282;
          font-size: 10px;
          font-weight: 400;
          line-height: 16px;
        }
      }
    }

    .svg-date,
    .svg-time {
      width: 16px;
      height: 16px;
    }

    .svg-vote {
      width: 20px;
      height: 20px;

      path {
        stroke: #828282;

      }
    }

    .text-date {
      color: #00205A;
      font-size: 18px;
      font-style: normal;
      line-height: 20px;
    }

    .text-time {
      color: #828282;
      font-size: 18px;
      font-style: normal;
      line-height: 20px;
    }

    .vote-member {
      color: #828282;
      font-size: 20px;
      font-style: normal;
      line-height: 20px;
    }

    .no-vote{
      text-align: center;
      .svg-no-vote{
        width: 40px;
        height: 40px;
        margin-bottom: 20px;
      }
    }

    .votes {
      font-weight: bold;
      color: #555;
      margin-bottom: 5px;
      border: 1px solid lightgray;
      padding: 10px 17px;
      border-radius: 20px;

      &.status-green {
        background-color: #27ae60;
        color: #f1faf1;
        border: 1px solid transparent;

        path {
          stroke: #f1faf1;

        }

      }
    }


  }

  .suggestion-voting {
    background-color: #F8FAFC;
    border: 1px solid #EAEEF1;
    border-radius: 8px;
    padding: 20px;

    .section-title {
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      display: flex;
      color: #00205A;
      align-items: center;
    }
  }

  h2 {
    margin-bottom: 20px;
  }

  .vote-option {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #ffff;
  }

  .option-details {
    display: flex;
  }

  .votes {
    font-weight: bold;
    color: #555;
    margin-bottom: 5px;
  }

  .date-time {
    color: #333;
  }

  .confirm-button {
    padding: 10px;
    margin-top: 20px;
    font-size: 16px;
  }

  .header {
    margin: 0 0 42px;
    font-size: 24px;
    font-weight: 700;
    line-height: 20px;
  }

  .voting-header {

    margin-bottom: 5px;

    .header-actions {
      display: flex;
      gap: 12px;

      button {
        background: none;
        border: none;
        cursor: pointer;
        padding: 8px;
        color: #666;
        transition: color 0.2s ease;

        &:hover {
          color: #333;
        }

        .fa-pencil {
          color: #eaa300;
        }

        .expand {
          color: $navy-blue;
        }

        i {
          font-size: 16px;
        }
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      display: flex;
      align-items: center;

      span {
        border-radius: 14px;
        background: rgba(38, 86, 135, 0.12);
        color: var(--Color---Black-1, #000);

        font-size: 16px;
        font-weight: 400;
        line-height: 18px;
        /* 112.5% */
        display: flex;
        padding: 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-right: 10px;
        margin-left: 10px;
      }
    }

    .sub-title {
      color: #828282;
      font-weight: 600;
    }

    .info {
      color: #4f4f4f;
      font-size: 18px;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 33px;

    }

    .result-card {
      display: flex;
      align-items: center;
      border: 1px solid #e0e6ed;
      border-radius: 12px;
      padding: 12px 10px;
      background-color: #fff;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.02);
      justify-content: center;
      flex-direction: column;

      .result {
        color: #00205A;
        font-size: 32px;
        font-weight: 700;
        line-height: 32px;
        margin-bottom: 0;
      }

      .result-text {
        color: #333;
        font-size: 16px;
        font-weight: 400;
        line-height: 21px;
        margin-top: 12px;
        padding: 0 18px;
      }
    }

    .voting-data {
      align-items: center;
      border: 1px solid #e0e6ed;
      border-radius: 12px;
      padding: 12px 10px;
      background-color: #fff;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.02);
    }

    .voting-header {
      display: flex;
      justify-content: space-between;
      padding: 20px 20px 0;
      text-align: center;
    }

    .voting-date {
      color: #00205A;
      font-size: 20px;
      font-weight: 400;
      line-height: 21px;
    }

    .vote-count {

      border: 1px solid lightgray;
      border-radius: 27px;
      padding: 10px;
    }

    .voters-list {
      background-color: white;
    }

    .voter-item {
      padding: 15px 20px;
      border-bottom: 1px solid #eee;
      transition: background-color 0.3s;
    }

    .voter-item:last-child {
      border-bottom: none;
    }

    .voter-item:hover {
      background-color: #f9f9f9;
    }

    .voter-name {
      font-size: 1.1rem;
      color: #333;
    }

  }




  .main-content {
    .create-form-container {
      background: #F8FAFC;
      border-radius: 16px;
      border: 0.5px solid #DCE0E3;
      padding: 16px;

      .form-section {
        margin-bottom: 40px;

        &:last-child {
          margin-bottom: 0;

          .form-group {
            padding: 12px;
            margin-bottom: 0;

            &.col-12 {
              width: 100%;
            }

            &.col-md-6 {
              @media (min-width: 768px) {
                width: 50%;
              }

              @media (max-width: 767px) {
                width: 100%;
              }
            }

            label {
              font-size: 16px !important;
              font-weight: 600 !important;
              color: #00205a !important;
              margin-bottom: 10px !important;
            }

            .form-control,
            textarea {
              width: 100%;
              border-radius: 8px;
              border: 1px solid var(--border-color);
              padding: 0 16px;
              font-size: 14px;
              color: var(--navy-blue);
              background-color: white;
              transition: all 0.2s ease;

            }

            .form-control {
              height: 42px;
            }

          }
        }

        .section-header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 20px;
          margin-top: 20px;
          padding-bottom: 10px;

          .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;

            // RTL support
            html[dir="rtl"] & {
              flex-direction: row-reverse;
            }
          }

          h6 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #00205a;
          }

          .items-num {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 14px;
            background: rgba(38, 86, 135, 0.12);
            color: #000;
            font-size: 16px;
            font-weight: 400;
            line-height: 18px;
          }
        }

        // Form builder header styling
        .header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 10px;
        }

      }
    }
  }

  // Assessment Questions Styles - Following resolution items pattern
  .items-container {
    margin-bottom: 20px;

    .assessment-question-card {
      padding: 16px 10px;
      border-radius: 8px;
      background-color: white;
      margin-bottom: 16px;

      .item-header {
        .item-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .item-number {
            font-size: 20px;
            font-weight: 500;
            line-height: 32px;
            color: #00205a;
          }

          .item-actions {
            display: flex;
            gap: 8px;

            .btn {
              padding: 8px;

              &:hover {
                background: unset;
                border: unset;
                color: unset;
              }
            }
          }
        }
      }

      .item-body {
        .item-description {
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
          line-height: 1.4;
        }

        .question-type-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .question-type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: #e3f2fd;
            color: #1976d2;
          }

          .question-options {
            .options-count {
              font-size: 12px;
              color: #666;
            }
          }
        }
      }
    }
  }

  .empty-items {
    text-align: center;
    padding: 40px 20px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .add-item-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: white;
    color: $navy-blue;
    border-radius: 8px;
    font-weight: 400;
    transition: all 0.2s ease;
    border: 1px solid #e0e0e0;

    &:hover {
      background: #f8f9fa;
      border-color: $navy-blue;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 32, 90, 0.15);
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    // RTL support
    html[dir="rtl"] & {
      flex-direction: row-reverse;
    }

    svg {
      flex-shrink: 0;
    }
  }

  .assessment-questions {
    max-height: 500px;
    overflow-y: auto;
  }

  // Form Actions
  .form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;

    .actions-container {
      display: flex;
      justify-content: flex-end;
      gap: 12px;

    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .create-assessment-page {
    padding: 15px;

    .main-content {
      .create-form-container {
        padding: 20px;

        .form-actions {
          .actions-container {
            flex-direction: column;

            app-custom-button {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
