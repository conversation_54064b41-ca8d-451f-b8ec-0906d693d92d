import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import Swal from 'sweetalert2';

// Shared Components
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { MemberNoteComponent } from '../../../resolutions/components/member-note/member-note.component';
import { SingleNoteDialogComponent } from '../../../resolutions/components/single-note-dialog/single-note-dialog.component';

// Core Services and Interfaces
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@shared/enum/size-enum';

// API Generated Types
import {
  AssessmentServiceProxy,
  AssessmentType,
  StringBaseResponse,
  RejectAssessmentCommand,
  QuestionType,
  AssessmentDetailsDtoBaseResponse,
  AssessmentDetailsDto,
  AssessmentStatus,
  ResponseStatus,
  CreateAssessmentOptionDto,
  BoardMembersServiceProxy,
  BoardMemberResponsePaginatedResult
} from '@core/api/api.generated';

// Services
import { ErrorModalService } from '@core/services/error-modal.service';
import { TokenService } from 'src/app/features/auth/services/token.service';

// Dialog Components
import { RejectAssessmentDialogComponent, RejectAssessmentDialogData } from '../reject-assessment-dialog/reject-assessment-dialog.component';
import { AttachmentCardComponent } from 'src/app/features/resolutions/components/attachment-card/attachment-card.component';
import { AssessmentTimelineComponent } from '../assessment-timeline/assessment-timeline.component';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-assessment-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    TranslateModule,
    // MemberNoteComponent,
    AttachmentCardComponent,
    AssessmentTimelineComponent,
    MatTooltipModule
    // DateHijriConverterPipe
  ],
  templateUrl: './assessment-details.component.html',
  styleUrls: ['./assessment-details.component.scss']
})
export class AssessmentDetailsComponent implements OnInit, OnDestroy {
  // Lifecycle management
  private destroy$ = new Subject<void>();

  // UI Properties
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  assessmentType = AssessmentType;

  currentAssessmentId = 0;
  currentFundId = 0;
  assessment: AssessmentDetailsDto | null = null;
  isLoading = false;
  isExpanded = true;
  isQuestionsExpanded = true;
  isVotingExpanded = true;
  isNotesExpanded = true;
  isExpandedItem = true;
  isExpandedAction = true;


  userRole = '';

  // Processing states
  isApproving = false;
  isRejecting = false;
  isDeleting = false;
  isDistribute = false;

  // Voting state
  userAnswers: { [key: string]: any } = {};
  votingForm: FormGroup | null = null;
  questionType = QuestionType;
  currentFundName = "";
  assessmentStatus: any | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private assessmentServiceProxy: AssessmentServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService,
    public tokenService: TokenService,
    private dialog: MatDialog,
    private location: Location,
    private breadcrumbService: BreadcrumbService,
    private boardMembersServiceProxy: BoardMembersServiceProxy
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Component Initialization
  private initializeComponent(): void {
    this.getRouteParameters();
    this.loadAssessmentDetails();
  }

  // Route Parameter Handling
  private getRouteParameters(): void {
    this.currentAssessmentId = Number(this.route.snapshot.paramMap.get('id'));

    if (!this.currentAssessmentId) {
      this.errorModalService.showError('ASSESSMENTS.INVALID_ASSESSMENT_ID');
      this.router.navigate(['/admin/investment-funds/assessments']);
      return;
    }
  }

  // Breadcrumb Management
  private updateBreadcrumbWithFallback(): void {
    this.breadcrumbItems = [
      // { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.INVESTMENT_FUNDS', url: '/admin/investment-funds' },
      {
        label: this.currentFundName || 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`
      },
      {
        label: 'BREADCRUMB.ASSESSMENTS',
        url: `/admin/investment-funds/assessments?fundId=${this.currentFundId}`
      },
      { label: 'BREADCRUMB.ASSESSMENT_DETAILS' ,url: '', disabled: true }
    ];
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  onBreadcrumbClicked(event: any): void {
     if (!event) {
      console.warn('Breadcrumb item is null or undefined');
      return;
    }

    if (event.disabled) {
      console.log('Breadcrumb item is disabled, ignoring click');
      return;
    }

    if (!event.url) {
      console.warn('Breadcrumb item has no URL:', event);
      return;
    }

    if (event?.url) {
      this.router.navigateByUrl(event.url);
    }
  }

  goBack() {
    this.location.back();
  }

  // Load Assessment Details using NSwag-generated API
  private loadAssessmentDetails(): void {

    // Load basic assessment data
    this.assessmentServiceProxy.details(this.currentAssessmentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: AssessmentDetailsDtoBaseResponse) => {
          if (response.successed && response.data) {
            this.assessment = response.data;
            this.currentFundName = response.data.fundName || 'BREADCRUMB.FUND_DETAILS';
            this.currentFundId = response.data.fundId;
            this.assessmentStatus = response.data.statusHistory;
            this.updateBreadcrumbWithFallback();

            // Load comprehensive data for voting functionality
          } else {
            this.errorModalService.showError(
              response.message || this.translateService.instant('ASSESSMENTS.LOAD_ERROR')
            );
          }
        },
        error: (error: any) => {
          console.error('Error loading assessment details:', error);

          let errorMessage = this.translateService.instant('ASSESSMENTS.LOAD_ERROR');
          if (error?.error?.message) {
            errorMessage = error.error.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }

          this.errorModalService.showError(errorMessage);
        }
      });
  }

  // Utility methods
  isQuestionnaireType(): boolean {
    return this.assessment?.type === AssessmentType._1;
  }

  isAttachmentType(): boolean {
    return this.assessment?.type === AssessmentType._2;
  }

  shouldShowNoResponsesMessage(): boolean {
    if (!this.assessment) return false;

    // Show message when:
    // 1. Assessment is active (status 4 = Active/Under Assessment)
    // 2. No responses have been received yet
    const isActive = this.assessment.status === AssessmentStatus._5; // AssessmentStatus.Active
    const hasNoResponses = this.assessment.responses?.filter(r=>r.status == ResponseStatus._2).length === 0;

    return isActive && hasNoResponses;
  }

  // UI Control methods
  toggleExpand(): void {
    this.isExpanded = !this.isExpanded;
  }

  toggleQuestionsExpand(): void {
    this.isQuestionsExpanded = !this.isQuestionsExpanded;
  }

  toggleVotingExpand(): void {
    this.isVotingExpanded = !this.isVotingExpanded;
  }

  toggleNotesExpand(): void {
    this.isNotesExpanded = !this.isNotesExpanded;
  }

  toggleExpandItems(): void {
    this.isExpandedItem = !this.isExpandedItem;
  }

  toggleExpandActions(): void {
    this.isExpandedAction = !this.isExpandedAction;
  }

  // Permission checking methods are now handled by API-provided permission flags
  // assessment.canApprove, assessment.canReject, assessment.canDelete, etc.

  // Action handlers
  approveAssessment(): void {
    if (!this.assessment || this.isApproving) return;

    this.isApproving = true;

    // Use NSwag-generated approve method
    this.assessmentServiceProxy.approve(this.currentAssessmentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isApproving = false;

          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('ASSESSMENTS.APPROVE_SUCCESS')
            );
            // Navigate back to assessments list
            this.cancel();
          } else {
            this.errorModalService.showError(
              response.message || this.translateService.instant('ASSESSMENTS.APPROVE_ERROR')
            );
          }
        },
        error: (error) => {
          this.isApproving = false;
          console.error('Error approving assessment:', error);
          this.errorModalService.showError(
            this.translateService.instant('ASSESSMENTS.APPROVE_ERROR')
          );
        }
      });
  }

  rejectAssessment(): void {
    this.dialog.closeAll();
    if (!this.assessment || this.isRejecting) return;

    // Open reject dialog
    const dialogData: RejectAssessmentDialogData = {
      assessmentId: this.currentAssessmentId,
      assessmentTitle: this.assessment.title || ''
    };

    const dialogRef = this.dialog.open(RejectAssessmentDialogComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe((command: RejectAssessmentCommand) => {
      if (command) {
        this.executeRejectAssessment(command);
      }
    });
  }

  private executeRejectAssessment(command: RejectAssessmentCommand): void {
    this.isRejecting = true;

    // Use NSwag-generated reject method
    this.assessmentServiceProxy.reject(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isRejecting = false;

          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('ASSESSMENTS.REJECT_SUCCESS')
            );
            // Navigate back to assessments list
            this.cancel();
          } else {
            this.errorModalService.showError(
              response.message || this.translateService.instant('ASSESSMENTS.REJECT_ERROR')
            );
          }
        },
        error: (error) => {
          this.isRejecting = false;
          console.error('Error rejecting assessment:', error);
          this.errorModalService.showError(
            this.translateService.instant('ASSESSMENTS.REJECT_ERROR')
          );
        }
      });
  }

  // Navigation methods
  cancel(): void {
    this.router.navigate(['/admin/investment-funds/assessments'], {
      queryParams: { fundId: this.currentFundId }
    });
  }

  editAssessment(): void {
    this.router.navigate(['/admin/investment-funds/assessments/edit', this.currentAssessmentId], {
    });
  }

  deleteAssessment(): void {
    if (!this.assessment || this.isDeleting) return;

    const assessmentTitle = this.assessment.title || '';

    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM_DELETE'),
      text: this.translateService.instant('ASSESSMENTS.DELETE_CONFIRM', { title: assessmentTitle }),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: this.translateService.instant('COMMON.DELETE'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL')
    }).then((result) => {
      if (result.isConfirmed) {
        this.executeDeleteAssessment();
      }
    });
  }

  private executeDeleteAssessment(): void {
    this.isDeleting = true;

    // Use NSwag-generated delete method
    this.assessmentServiceProxy.delete(this.currentAssessmentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isDeleting = false;

          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('ASSESSMENTS.DELETE_SUCCESS')
            );
            // Navigate back to assessments list
            this.cancel();
          } else {
            this.errorModalService.showError(
              response.message || this.translateService.instant('ASSESSMENTS.DELETE_ERROR')
            );
          }
        },
        error: (error) => {
          this.isDeleting = false;
          console.error('Error deleting assessment:', error);
          this.errorModalService.showError(
            this.translateService.instant('ASSESSMENTS.DELETE_ERROR')
          );
        }
      });
  }

  // Get question type display name
  getQuestionTypeDisplayName(type: QuestionType): string {
    switch (type) {
      case QuestionType._1:
        return this.translateService.instant('ASSESSMENTS.SINGLE_CHOICE');
      case QuestionType._2:
        return this.translateService.instant('ASSESSMENTS.MULTIPLE_CHOICE');
      case QuestionType._3:
        return this.translateService.instant('ASSESSMENTS.TEXT_ANSWER');
      default:
        return '';
    }
  }

  // Navigation to member response
  navigateToMemberResponse(memberId: number): void {
    if (this.currentAssessmentId && memberId) {
      this.router.navigate([
        '/admin/investment-funds/assessments/member-response'
      ], {
        queryParams: { assessmentId: this.currentAssessmentId,memberId:memberId }
      });
    }
  }

  // Check if member can respond to assessment
  canRespondToAssessment(responseStatus: number): boolean {
    // Allow response if status is pending or draft
    // This logic should be updated based on actual status values
    return responseStatus === 1 || responseStatus === 0; // Adjust status values as needed
  }

  // Calculate pending responses count
  getPendingResponsesCount(): number {
    if (!this.assessment?.statistics) {
      return 0;
    }
    // Pending = Total Expected - (Completed + Draft)
    return this.assessment.statistics.totalExpectedResponses -
           (this.assessment.statistics.completedResponses + this.assessment.statistics.draftResponses);
  }

  // Helper method to format DateTime for display
  formatDateTime(dateTime: any): string {
    if (!dateTime) {
      return 'Unknown';
    }

    try {
      // Handle DateTime object from Luxon
      if (dateTime.toJSDate) {
        return new Date(dateTime.toJSDate()).toLocaleString();
      }
      // Handle regular Date or string
      return new Date(dateTime).toLocaleString();
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'Unknown';
    }
  }

  getAssessmentStatusClass(status: AssessmentStatus | undefined): string {
    if (!status) return 'status-grey';

    switch (status) {
      case AssessmentStatus._1: // Draft
        return 'status-grey';
      case AssessmentStatus._2: // Under Review
        return 'status-blue';
      case AssessmentStatus._3: // Approved
        return 'status-green';
      case AssessmentStatus._4: // Rejected
        return 'status-red';
        case AssessmentStatus._5: // Distributed
        return 'status-blue';
      case AssessmentStatus._6: // Completed
      return 'status-purple';
      default:
        return 'status-grey';
    }
  }
  getResponseStatusClass(status: ResponseStatus):string{
     switch (status) {
      case ResponseStatus._1: // pending
        return 'status-blue';
      case ResponseStatus._2: // Submited
        return 'status-green';
      default:
        return 'status-grey';
    }
  }
  getAssessmentStatusDisplayName(status: AssessmentStatus | undefined, statusDisplayName: string | undefined): string {
    // If we have a display name from the API, use it
    if (statusDisplayName) {
      return statusDisplayName;
    }

    // Fallback to translation keys based on status
    if (!status) return this.translateService.instant('ASSESSMENTS.ASSESSMENT_STATUS.UNKNOWN');

    switch (status) {
      case AssessmentStatus._1:
        return this.translateService.instant('ASSESSMENTS.STATUS.DRAFT');
      case AssessmentStatus._2:
        return this.translateService.instant('ASSESSMENTS.STATUS.WAITINGFORAPPROVAL');
      case AssessmentStatus._3:
        return this.translateService.instant('ASSESSMENTS.STATUS.APPROVED');
      case AssessmentStatus._4:
        return this.translateService.instant('ASSESSMENTS.STATUS.REJECTED');
      case AssessmentStatus._5:
        return this.translateService.instant('ASSESSMENTS.STATUS.ACTIVE');
      case AssessmentStatus._6:
        return this.translateService.instant('ASSESSMENTS.STATUS.COMPLETED');
      default:
        return this.translateService.instant('ASSESSMENTS.ASSESSMENT_STATUS.UNKNOWN');
    }
  }
  
distributeAssessment()
{
    if (!this.assessment || this.isDistribute) return;

    this.isDistribute = true;

    // First get the board member count
    this.boardMembersServiceProxy.boardMembersList(
      this.currentFundId,
      0, // page
      1000, // large page size to get all members
      '', // search
      'IsActive desc' // order by active status
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response: BoardMemberResponsePaginatedResult) => {
        let boardMemberCount = 0;
        if (response.successed && response.data) {
          // Count only active board members
          boardMemberCount = response.data.filter(member => member.isActive).length;
        }

        // Show confirmation dialog with board member count
        this.showDistributeConfirmation(boardMemberCount);
      },
      error: (error) => {
        console.error('Error getting board member count:', error);
        // Fallback: show dialog without count
        this.showDistributeConfirmation(0);
      }
    });
}

private showDistributeConfirmation(boardMemberCount: number): void {
    Swal.fire({
          title: this.translateService.instant('COMMON.CONFIRM_Distribute'),
          text: this.translateService.instant('ASSESSMENTS.Distribute_CONFIRM', { count: boardMemberCount }),
          imageUrl: 'assets/images/confirmation-green.svg',
          showCancelButton: true,
          customClass: {
            confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
            cancelButton: 'btn outline-btn',
          },
          reverseButtons: true,
          buttonsStyling: false,
          confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('ASSESSMENTS.Distribute'),
          cancelButtonText: `<img src="assets/images/back-icon.png" alt="back" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('COMMON.CANCEL')
        }).then((result) => {
      if (result.isConfirmed) {
        this.assessmentServiceProxy.distribute(this.currentAssessmentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isDistribute = false;

          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('ASSESSMENTS.Distribute_SUCCESS')
            );
            // Navigate back to assessments list
            this.cancel();
          } else {
            this.errorModalService.showError(
              response.message || this.translateService.instant('ASSESSMENTS.Distribute_ERROR')
            );
          }
        },
        error: (error) => {
          this.isDistribute = false;
          console.error('Error approving assessment:', error);
          this.errorModalService.showError(
            this.translateService.instant('ASSESSMENTS.Distribute_ERROR')
          );
        }
      });
      }
      else
            this.isDistribute = false;

    });

    
}
getOptions(options:CreateAssessmentOptionDto[]):CreateAssessmentOptionDto[]
{
  return options.filter(o=>!o.isOther);
}

  isEnglish(): boolean {
    return this.translateService.currentLang === 'en';
  }
}
