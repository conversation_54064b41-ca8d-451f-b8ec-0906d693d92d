<div class="add-attachment-dialog">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <h2 mat-dialog-title class="dialog-title">
      {{ 'INVESTMENT_FUNDS.MEETING.ADD_ATTACHMENTS' | translate }}
    </h2>
    <button
      mat-icon-button
      class="close-button"
      (click)="cancel()"
      [disabled]="isLoading || isAttachingFiles">
      <span class="close-icon">×</span>
    </button>
  </div>

  <!-- Dialog Content -->
  <mat-dialog-content class="dialog-content">
    <!-- File Upload Section -->
    <div class="upload-section">
      <h3 class="section-title">
        {{ 'RESOLUTIONS.FILE_UPLOAD_TEXT' | translate }}
      </h3>

      <!-- File Upload Component -->
      <app-file-upload
        [moduleId]="attachmentModule.Other"
        [initialFiles]="[]"
        [allowedTypes]="['pdf']"
        [maxLength]="1"
        [maxSize]="10"
        [multiple]="false"
        (fileUploaded)="onFileUploaded($event)">
      </app-file-upload>
    </div>

   

    <!-- Loading State -->
    <div class="loading-section" *ngIf="isLoading">
      <div class="loading-content">
        <div class="spinner"></div>
        <span class="loading-text">
          {{ 'COMMON.SUBMITTING' | translate }}
        </span>
      </div>
    </div>
  </mat-dialog-content>

  <!-- Dialog Actions -->
  <mat-dialog-actions class="dialog-actions">
    <app-custom-button
      [btnName]="'COMMON.CANCEL' | translate"
      [buttonType]="buttonTypeEnum.OutLine"
      (click)="cancel()"
      [disabled]="isLoading || isAttachingFiles"
      class="cancel-btn">
    </app-custom-button>
    
    <app-custom-button
      [btnName]="'COMMON.SAVE' | translate"
      [buttonType]="buttonTypeEnum.Primary"
      [iconName]="iconEnum.add"
      (click)="saveAttachments()"
      [disabled]="isSaveDisabled()"
      class="save-btn">
    </app-custom-button>
  </mat-dialog-actions>
</div>
