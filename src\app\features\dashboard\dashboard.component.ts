import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, finalize } from 'rxjs';

// Shared Components
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { AlertComponent } from '@shared/components/alert/alert.component';

// Dashboard Components
import { VotingCardComponent } from "./voting-card/voting-card.component";
import { BoxInfoCardComponent } from "./box-info-card/box-info-card.component";
import { DashboardAnalyticsCardComponent } from "./dashboard-analytics-card/dashboard-analytics-card.component";
import { ResolutionStatusDonutChartComponent } from "./resolution-status-donut-chart/resolution-status-donut-chart.component";
import { VoteResultGaugeChartComponent } from "./vote-result-gauge-chart/vote-result-gauge-chart.component";
import { FundDropdownComponent } from "./fund-dropdown/fund-dropdown.component";
import { AssessmentStatusPieChartComponent } from "./assessment-status-pie-chart/assessment-status-pie-chart.component";
import { MeetingsAttendancePieChartComponent } from "./meetings-attendance-pie-chart/meetings-attendance-pie-chart.component";
import { DocumentCarouselComponent } from "./document-carousel/document-carousel.component";
import { DashboardNotificationsComponent, NotificationItem, MeetingItem } from "./dashboard-notifications/dashboard-notifications.component";

// Services and Models
import { DashboardService } from './services/dashboard.service';
import { TokenService, userRole } from '../auth/services/token.service';

// Dashboard Models
import {
  DashboardViewModel,

} from './models/dashboard.models';

// API Types
import {
  FundSummaryStatisticsDto,
  FundResolutionAnalyticsDto,
  FundAssessmentAnalyticsDto,
  FundAnalyticsResponseBaseResponse,
  FundDocumentAnalyticsDto,
  DefaultFundInfoDto,
  GetResolutionVoteDistributionServiceProxy,
  ResolutionVotesBreakdownDtoListBaseResponse
} from '@core/api/api.generated';


// Core Interfaces and Enums
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { AlertType } from '@core/enums/alert-type';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    PageHeaderComponent,
    AlertComponent,
    VotingCardComponent,
    BoxInfoCardComponent,
    DashboardAnalyticsCardComponent,
    ResolutionStatusDonutChartComponent,
    VoteResultGaugeChartComponent,
    FundDropdownComponent,
    AssessmentStatusPieChartComponent,
    MeetingsAttendancePieChartComponent,
    DocumentCarouselComponent,
    DashboardNotificationsComponent, FormsModule, NgSelectModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss'
})
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Loading and Error States
  loading = false;
  meetingsLoading = false;
  errorMessage = '';
  alertType = AlertType;
  SizeEnum = SizeEnum;

  // User Role and Permissions
  currentUserRole: string = '';
  userPermissions: string[] = [];
  userName: string = '';

  // Dashboard Data
  dashboardData: DashboardViewModel | null = null;
  quickActions: any[] = [];

  // Fund Management
  defaultFunds: DefaultFundInfoDto[] = [];
  selectedFundId: number | null = null;

  // Analytics Data
  fundStatistics: FundSummaryStatisticsDto | null = null;
  resolutionAnalytics: FundResolutionAnalyticsDto | null = null;
  assessmentAnalytics: FundAssessmentAnalyticsDto | null = null;
  documentAnalytics: FundDocumentAnalyticsDto | null = null;
  meetingTypes: any[] = [
    { id: 1, name: 'INVESTMENT_FUNDS.MEETING.PERIODIC_MEETING', value: 'Periodic' },
    { id: 2, name: 'INVESTMENT_FUNDS.MEETING.ANNUAL_MEETING', value: 'Annual' }
  ];
  selectedMeetingTypeId: number = 1;
  statusResolutionsColors: { [key: number]: string } = {
    1: '#7555ac', //  مسودة
    2: '#fff3cd', // معلق
    3: '#9d7009',  // استكمال البيانات
    4: '#17a2b8',//في انتظار التأكيد
    5: '#27ae60', // مؤكد
    6: '#828282', //مرفوض
    7: '#2f80ed', //التصويت قيد التقدم
    8: '#0e700e', //معتمد
    9: '#C50F1F', //غير معتمد
    10: '#828282' //ملغي
  };

  voteColors: { [key: string]: string } = {
    1: '#ffc107',
    2: '#28a745',
    3: '#dc3545',
  };

  statusAssessmentColors: { [key: string]: string } = {
    '1': ' #7555ac',
    '2': ' #17a2b8',
    '3': ' #0e700e',
    '4': ' #C50F1F',
    '5': '#2f80ed',
    '6': '#27ae60',
  };

  defaultColor = '#999';

  itemsPerView = 4;

  mockMeetings: MeetingItem[] = [
    {
      id: '1',
      title: 'اجتماع مجلس الإدارة',
      fundName: 'صندوق جدوى ريت السعودي',
      meetingDate: new Date(),
      status: 'upcoming',
      statusDisplay: 'قادم'
    },
    {
      id: '2',
      title: 'اجتماع الجمعية العمومية',
      fundName: 'صندوق جدوى ريت السعودي',
      meetingDate: new Date(),
      status: 'in-progress',
      statusDisplay: 'جاري'
    }
  ];

  // UI State
  showVotingSection = false;
  showComplianceSection = false;
  showPerformanceSection = false;
  showMeetingSection = false;

  // Breadcrumb Configuration
  breadcrumbs: IBreadcrumbItem[] = [
    {
      label: 'sidebar.dashboard',
      url: '/admin/dashboard',
      disabled: true
    }
  ];
  isAdministrator: boolean = false;
  fundResolutionForVoting: any;
  selectedResolutionId: any;
  meetingCount: any;

  constructor(
    private dashboardService: DashboardService,
    public tokenService: TokenService,
    private router: Router,
    private getResolutionVoteDistributionService: GetResolutionVoteDistributionServiceProxy) { }

  ngOnInit(): void {
    this.isAdministrator = this.checkIsAdministrator()
    this.loadDashboardData();
    this.updateItemsPerView();
    window.addEventListener('resize', this.updateItemsPerView.bind(this));

  }
  checkIsAdministrator() {
    return this.tokenService.hasRole(userRole.fundManager) ||
      this.tokenService.hasRole(userRole.associatedFundManager) ||
      this.tokenService.hasRole(userRole.financeController) ||
      this.tokenService.hasRole(userRole.complianceLegalManagingDirector) ||
      this.tokenService.hasRole(userRole.headOfRealEstate) ||
      this.tokenService.hasRole(userRole.legalCouncil) ||
      this.tokenService.hasRole(userRole.boardSecretary);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    window.removeEventListener('resize', this.updateItemsPerView.bind(this));

  }



  /**
   * Load dashboard data based on user's role and selected fund
   */
  private loadDashboardData(fundId?: number): void {
    this.loading = true;
    this.errorMessage = '';

    // Use selected fund ID or default
    const targetFundId = fundId || this.selectedFundId;

    // Load dashboard data from API
    this.dashboardService.getDashboard(targetFundId || undefined)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: (response) => this.processDashboardResponse(response),
        error: (error) => {
          console.error('Dashboard loading error:', error);
          this.errorMessage = 'DASHBOARD.ERROR.LOAD_FAILED';
        }
      });
  }

  /**
   * Process dashboard response and update component properties
   */
  private processDashboardResponse(response: any): void {
    if (response.successed && response.data) {
      // Extract analytics data from response
      this.fundStatistics = response.data.fundSummaryStatistics || null;
      this.resolutionAnalytics = response.data.resolutionAnalytics || null;
      this.fundResolutionForVoting = this.resolutionAnalytics?.fundResolutions;
      if (!this.isAdministrator) {
        if (this.resolutionAnalytics?.resolutionStatusDistribution)
          this.resolutionAnalytics.resolutionStatusDistribution = (this.resolutionAnalytics?.resolutionStatusDistribution?.filter((item: any) => item.statusId >= 7 && item.statusId <= 9) || null);

        if (this.resolutionAnalytics)
          this.resolutionAnalytics.totalResolutions = this.resolutionAnalytics?.resolutionStatusDistribution?.reduce((total: number, item: any) => total + (item.count || 0), 0) || 0;
      }
      if (this.fundResolutionForVoting?.length > 0)
        this.selectedResolutionId = this.fundResolutionForVoting[0].id;

      this.assessmentAnalytics = response.data.assessmentAnalytics || null;
      this.documentAnalytics = response.data.documentAnalytics || null;

      // Extract default funds if available
      if (response.data.defaultFunds && Array.isArray(response.data.defaultFunds)) {
        this.defaultFunds = response.data.defaultFunds;

        // Set default selected fund if not already set
        if (!this.selectedFundId && this.defaultFunds.length > 0) {
          this.selectedFundId = this.defaultFunds[0].fundId || null;
          localStorage.setItem('fundName', this.defaultFunds[0].fundName || '');
        }
      }

      // Create dashboard view model if needed
      this.dashboardData = {
        userName: response.data.userName || '',
        fundSummaryStatistics: this.fundStatistics || undefined,
        resolutionAnalytics: this.resolutionAnalytics || undefined,
        assessmentAnalytics: this.assessmentAnalytics || undefined,
        documentAnalytics: this.documentAnalytics || undefined
      };

    } else {
      this.errorMessage = response.message || 'DASHBOARD.ERROR.LOAD_FAILED';
    }
  }

  /**
   * Get total votes from resolution analytics
   */
  getTotalVotes(): number {
    if (!this.resolutionAnalytics?.resolutionVoteResultDistribution) return 0;

    return this.resolutionAnalytics.resolutionVoteResultDistribution.reduce(
      (total, item) => total + (item.count || 0), 0
    );
  }

  /**
   * Get total assessments from assessment analytics
   */
  getTotalAssessments(): number {
    if (!this.assessmentAnalytics?.assessmentStatusDistribution) return 0;

    return this.assessmentAnalytics.assessmentStatusDistribution.reduce(
      (total, item) => total + (item.count || 0), 0
    );
  }

  /**
   * Handle fund selection change
   */
  onResolutionChangedForVoting(event: any): void {
    const resolutionId = event; // Convert to number
    this.selectedResolutionId = resolutionId;
    this.loadResolutionVoteDistribution(resolutionId);
  }

  private loadResolutionVoteDistribution(resolutionId: number): void {
    if (!resolutionId) {
      return;
    }

    this.getResolutionVoteDistributionService.resolutionVoteDistribution(resolutionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: ResolutionVotesBreakdownDtoListBaseResponse) => {
          if (response.successed && response.data) {
            // Update the resolution analytics with the new vote distribution data
            if (this.resolutionAnalytics) {
              this.resolutionAnalytics.resolutionVoteResultDistribution = response.data;
            }
          }
        },
        error: (error) => {
          console.error('Error loading resolution vote distribution:', error);
        }
      });
  }

  onFundChanged(fundId: number): void {
    if (this.selectedFundId !== fundId) {
      this.selectedFundId = fundId;
      localStorage.setItem('fundName', this.defaultFunds.find(f => f.fundId === fundId)?.fundName || '');
      this.loadDashboardData(fundId);
    }
  }

  /**
   * Refresh dashboard data
   */
  onRefresh(): void {
    this.loadDashboardData();
  }
  onMeetingTypeChanged(event: any) {
    this.selectedMeetingTypeId = event;
  }
  getMeetingCount(event: any) {
    this.meetingCount = event;
  }

  onMeetingsLoadingChange(loading: boolean) {
    this.meetingsLoading = loading;
  }
  goToDocument() {
    this.router.navigate(['/admin/investment-funds/documents'], {
      queryParams: { fundId: this.selectedFundId }
    });
  }
  goToResolution() {
    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.selectedFundId }
    });
  }
  goToResolutionDetails() {
    this.router.navigate(['/admin/investment-funds/resolutions/details', this.selectedResolutionId],
      { queryParams: { fundId: this.selectedFundId } });
  }
  goToMeeting() {
    this.router.navigate(['/admin/investment-funds/meetings'], {
      queryParams: { fundId: this.selectedFundId, selectedTab: 1 }
    });
  }
  goToAssesment() {
    this.router.navigate(['/admin/investment-funds/assessments'], {
      queryParams: { fundId: this.selectedFundId }
    });
  }
  goToResolutionInbox() {
    if (!this.checkIsAdministrator())
      this.router.navigate(['/admin/investment-funds/resolutions/inbox']);
  }
  goToFund() {
    if (!this.checkIsAdministrator())
      this.router.navigate(['/admin/investment-funds']);
  }

  private updateItemsPerView(): void {
    this.itemsPerView = window.innerWidth <= 768 ? 1 : 4;
  }

}
