<div class="create-assessment-page">
  <!-- Breadcrumb Section -->
  <!-- <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div> -->

  <!-- Page Header -->
  <div class="page-header-section">
    <app-page-header [title]="title | translate">
    </app-page-header>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <div class="create-form-container">

      <!-- Form Container using reusable form-builder -->
      <div class="form-section">
        <!-- Form Builder Component -->
        <app-form-builder
          [formControls]="formControls"
          [formGroup]="formGroup"
          [isFormSubmitted]="isValidationFire"
          (fileUploaded)="onFileUpload($event)"
          (dateSelected)="dateSelected($event)">
          <p slot="top" class="header mt-2">{{ 'ASSESSMENTS.BASIC_INFO' | translate }}</p>
        </app-form-builder>
      </div>

      <!-- Assessment Questions Section - Following resolution items pattern -->
      <div class="form-section mb-3" *ngIf="isQuestionnaireType()">
        <div class="section-header">
          <div class="header-content">
            <div class="d-flex align-items-center gap-2">
              <h6>{{ 'ASSESSMENTS.ASSESSMENT_QUESTIONS' | translate }}</h6>
              <span class="items-num" *ngIf="questions.length">{{questions.length}} {{ 'ASSESSMENTS.QUESTION_WITHOUT' | translate }}</span>
            </div>
            <button *ngIf="questions.length" type="button" class="btn add-item-btn" (click)="addAssessmentQuestion()">            
              <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M13.0855 0.5H3.91447C2.8772 0.502951 1.88325 0.916315 1.14978 1.64978C0.416315 2.38325 0.00295102 3.3772 0 4.41447L0 13.5855C0.00295102 14.6228 0.416315 15.6167 1.14978 16.3502C1.88325 17.0837 2.8772 17.497 3.91447 17.5H13.0855C14.1228 17.497 15.1167 17.0837 15.8502 16.3502C16.5837 15.6167 16.997 14.6228 17 13.5855V4.41447C16.997 3.3772 16.5837 2.38325 15.8502 1.64978C15.1167 0.916315 14.1228 0.502951 13.0855 0.5ZM11.9906 9.73257H9.41039C9.39052 9.73242 9.37082 9.73622 9.35244 9.74376C9.33405 9.75129 9.31735 9.76241 9.3033 9.77646C9.28925 9.79051 9.27813 9.80721 9.2706 9.8256C9.26306 9.84398 9.25926 9.86368 9.25941 9.88355V12.4671C9.25941 12.6685 9.1794 12.8617 9.03698 13.0041C8.89457 13.1465 8.70141 13.2265 8.5 13.2265C8.29859 13.2265 8.10543 13.1465 7.96302 13.0041C7.8206 12.8617 7.74059 12.6685 7.74059 12.4671V9.88355C7.74074 9.86368 7.73694 9.84398 7.7294 9.8256C7.72187 9.80721 7.71075 9.79051 7.6967 9.77646C7.68265 9.76241 7.66595 9.75129 7.64756 9.74376C7.62918 9.73622 7.60947 9.73242 7.58961 9.73257H5.00941C4.90968 9.73257 4.81093 9.71292 4.71879 9.67476C4.62666 9.6366 4.54294 9.58066 4.47243 9.51014C4.40191 9.43962 4.34597 9.35591 4.30781 9.26377C4.26964 9.17163 4.25 9.07288 4.25 8.97316C4.25 8.87343 4.26964 8.77468 4.30781 8.68255C4.34597 8.59041 4.40191 8.50669 4.47243 8.43618C4.54294 8.36566 4.62666 8.30972 4.71879 8.27156C4.81093 8.23339 4.90968 8.21375 5.00941 8.21375H7.58961C7.60947 8.2139 7.62918 8.21009 7.64756 8.20256C7.66595 8.19502 7.68265 8.18391 7.6967 8.16986C7.71075 8.15581 7.72187 8.1391 7.7294 8.12072C7.73694 8.10233 7.74074 8.08263 7.74059 8.06276V5.48257C7.74059 5.28116 7.8206 5.088 7.96302 4.94558C8.10543 4.80317 8.29859 4.72316 8.5 4.72316C8.70141 4.72316 8.89457 4.80317 9.03698 4.94558C9.1794 5.088 9.25941 5.28116 9.25941 5.48257V8.06276C9.25881 8.08291 9.26227 8.10297 9.26961 8.12175C9.27694 8.14053 9.28798 8.15763 9.30208 8.17204C9.31617 8.18645 9.33303 8.19787 9.35164 8.20561C9.37026 8.21336 9.39024 8.21726 9.41039 8.2171H11.9906C12.192 8.2171 12.3852 8.29711 12.5276 8.43953C12.67 8.58195 12.75 8.77511 12.75 8.97651C12.75 9.17792 12.67 9.37108 12.5276 9.5135C12.3852 9.65591 12.192 9.73592 11.9906 9.73592V9.73257Z"
                  fill="#00205A" />
              </svg>
              {{ 'ASSESSMENTS.ADD_QUESTION' | translate }}
            </button>
          </div>
        </div>

        <!-- Questions List -->
        <div class="items-container assessment-questions" *ngIf="questions.length > 0">
          <div class="assessment-question-card" *ngFor="let question of questions; let i = index">
            <div class="item-header">
              <div class="item-info">
                <div class="item-number">
                  {{ 'ASSESSMENTS.QUESTION' | translate }} {{ i + 1 }}
                  <span class="question-type-badge">
                    {{ (question.type === 1 ? 'ASSESSMENTS.SINGLE_CHOICE' :
                        question.type === 2 ? 'ASSESSMENTS.MULTI_CHOICE' :
                        'ASSESSMENTS.TEXT_ANSWER') | translate }}
                  </span>
                </div>
                <div class="item-actions">
                  <button type="button"
                          class="btn btn-sm btn-outline-secondary edit-btn"
                          (click)="editAssessmentQuestion(question, i)"
                          title="{{ 'COMMON.EDIT' | translate }}">
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M20.3511 11.3427L9.3468 22.3593C9.29139 22.4149 9.2519 22.4844 9.23245 22.5605L8.01269 27.4614C7.99475 27.5341 7.99585 27.6102 8.01588 27.6824C8.03591 27.7546 8.0742 27.8204 8.12705 27.8734C8.20822 27.9544 8.31812 27.9999 8.43272 28C8.46807 28 8.50329 27.9956 8.53757 27.987L13.4333 26.7658C13.5094 26.7466 13.5789 26.7071 13.6343 26.6515L24.6396 15.6356L20.3511 11.3427ZM27.3658 9.84056L26.1408 8.61436C25.3221 7.79481 23.8952 7.79562 23.0775 8.61436L21.577 10.1165L25.8653 14.4092L27.3658 12.9072C27.7747 12.498 28 11.9533 28 11.374C28 10.7947 27.7747 10.25 27.3658 9.84056Z" fill="#EAA300"/>
                    </svg>
                  </button>
                  <button type="button"
                          class="btn btn-sm btn-outline-danger delete-btn"
                          (click)="removeQuestion(i)"
                          title="{{ 'COMMON.DELETE' | translate }}">
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21.3333 13.0003V12.3337C21.3333 11.4002 21.3333 10.9335 21.1517 10.577C20.9919 10.2634 20.7369 10.0084 20.4233 9.84865C20.0668 9.66699 19.6001 9.66699 18.6667 9.66699H17.3333C16.3999 9.66699 15.9332 9.66699 15.5767 9.84865C15.2631 10.0084 15.0081 10.2634 14.8483 10.577C14.6667 10.9335 14.6667 11.4002 14.6667 12.3337V13.0003M16.3333 17.5837V21.7503M19.6667 17.5837V21.7503M10.5 13.0003H25.5M23.8333 13.0003V22.3337C23.8333 23.7338 23.8333 24.4339 23.5608 24.9686C23.3212 25.439 22.9387 25.8215 22.4683 26.0612C21.9335 26.3337 21.2335 26.3337 19.8333 26.3337H16.1667C14.7665 26.3337 14.0665 26.3337 13.5317 26.0612C13.0613 25.8215 12.6788 25.439 12.4392 24.9686C12.1667 24.4339 12.1667 23.7338 12.1667 22.3337V13.0003" stroke="#C50F1F" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <div class="item-body mt-3">
              <div class="item-description" *ngIf="question.questionText">{{ question.questionText }}</div>
              <div class="question-type-info" *ngIf="question.type">
                
                <div class="question-options mt-3" *ngIf="(question.type === 1 || question.type === 2) && question.options">
                  <div class="options-header">
                    <h6 class="options-title">
                      {{ 'ASSESSMENTS.OPTIONS' | translate }} :
                      <div class="question-stats">
                        <div class="stats-item">
                          <span class="stats-text">{{  getOptions(question.options).length }} {{ 'ASSESSMENTS.OPTIONS_WITHOUT' | translate }}</span>
                        </div>
                      </div>
                    </h6>
                  </div>
                  <div class="options-list">
                    <div *ngFor="let option of getOptions(question.options); let optionIndex = index"
                        class="option-item"
                        [attr.data-option-index]="optionIndex + 1">
                      <div class="option-indicator" >
                        <span class="option-number">{{ optionIndex + 1 }}</span>
                      </div>
                      <div class="option-content">
                        <span class="option-text">{{ option.optionText }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div class="empty-items d-flex align-items-center justify-content-center" *ngIf="questions.length === 0">
          <button type="button" class="btn add-item-btn" (click)="addAssessmentQuestion()">
            <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M13.0855 0.5H3.91447C2.8772 0.502951 1.88325 0.916315 1.14978 1.64978C0.416315 2.38325 0.00295102 3.3772 0 4.41447L0 13.5855C0.00295102 14.6228 0.416315 15.6167 1.14978 16.3502C1.88325 17.0837 2.8772 17.497 3.91447 17.5H13.0855C14.1228 17.497 15.1167 17.0837 15.8502 16.3502C16.5837 15.6167 16.997 14.6228 17 13.5855V4.41447C16.997 3.3772 16.5837 2.38325 15.8502 1.64978C15.1167 0.916315 14.1228 0.502951 13.0855 0.5ZM11.9906 9.73257H9.41039C9.39052 9.73242 9.37082 9.73622 9.35244 9.74376C9.33405 9.75129 9.31735 9.76241 9.3033 9.77646C9.28925 9.79051 9.27813 9.80721 9.2706 9.8256C9.26306 9.84398 9.25926 9.86368 9.25941 9.88355V12.4671C9.25941 12.6685 9.1794 12.8617 9.03698 13.0041C8.89457 13.1465 8.70141 13.2265 8.5 13.2265C8.29859 13.2265 8.10543 13.1465 7.96302 13.0041C7.8206 12.8617 7.74059 12.6685 7.74059 12.4671V9.88355C7.74074 9.86368 7.73694 9.84398 7.7294 9.8256C7.72187 9.80721 7.71075 9.79051 7.6967 9.77646C7.68265 9.76241 7.66595 9.75129 7.64756 9.74376C7.62918 9.73622 7.60947 9.73242 7.58961 9.73257H5.00941C4.90968 9.73257 4.81093 9.71292 4.71879 9.67476C4.62666 9.6366 4.54294 9.58066 4.47243 9.51014C4.40191 9.43962 4.34597 9.35591 4.30781 9.26377C4.26964 9.17163 4.25 9.07288 4.25 8.97316C4.25 8.87343 4.26964 8.77468 4.30781 8.68255C4.34597 8.59041 4.40191 8.50669 4.47243 8.43618C4.54294 8.36566 4.62666 8.30972 4.71879 8.27156C4.81093 8.23339 4.90968 8.21375 5.00941 8.21375H7.58961C7.60947 8.2139 7.62918 8.21009 7.64756 8.20256C7.66595 8.19502 7.68265 8.18391 7.6967 8.16986C7.71075 8.15581 7.72187 8.1391 7.7294 8.12072C7.73694 8.10233 7.74074 8.08263 7.74059 8.06276V5.48257C7.74059 5.28116 7.8206 5.088 7.96302 4.94558C8.10543 4.80317 8.29859 4.72316 8.5 4.72316C8.70141 4.72316 8.89457 4.80317 9.03698 4.94558C9.1794 5.088 9.25941 5.28116 9.25941 5.48257V8.06276C9.25881 8.08291 9.26227 8.10297 9.26961 8.12175C9.27694 8.14053 9.28798 8.15763 9.30208 8.17204C9.31617 8.18645 9.33303 8.19787 9.35164 8.20561C9.37026 8.21336 9.39024 8.21726 9.41039 8.2171H11.9906C12.192 8.2171 12.3852 8.29711 12.5276 8.43953C12.67 8.58195 12.75 8.77511 12.75 8.97651C12.75 9.17792 12.67 9.37108 12.5276 9.5135C12.3852 9.65591 12.192 9.73592 11.9906 9.73592V9.73257Z"
                fill="#00205A" />
            </svg>
            {{ 'ASSESSMENTS.ADD_FIRST_QUESTION' | translate }}
          </button>
        </div>
      </div>

      <!-- Form Actions - Following edit-resolution pattern -->
      <div class="form-actions">
        <div class="actions-container d-flex gap-3 align-items-center justify-content-end">
          <!-- Cancel Button -->
          <app-custom-button
            [btnName]="'COMMON.CANCEL' | translate"
            (click)="onCancel()"
            [buttonType]="buttonEnum.Secondary"
            [iconName]="IconEnum.cancel">
          </app-custom-button>

          <!-- Save as Draft Button -->
          <app-custom-button
            [btnName]="'ASSESSMENTS.SAVE_AS_DRAFT' | translate"
            [buttonType]="buttonEnum.OutLine"
            [iconName]="IconEnum.draft"
            [disabled]="isApiCallInProgress"
            (click)="onSaveAsDraft()">
          </app-custom-button>

          <!-- Submit for Approval Button -->
          <app-custom-button
            [btnName]="isApiCallInProgress ? ('COMMON.SUBMITTING' | translate) : ('ASSESSMENTS.SUBMIT_FOR_APPROVAL' | translate)"
            [buttonType]="buttonEnum.Primary"
            [iconName]="IconEnum.verify"
            [disabled]="isApiCallInProgress"
            (click)="onSubmit()">
          </app-custom-button>
        </div>
      </div>
    </div>
  </div>
</div>
