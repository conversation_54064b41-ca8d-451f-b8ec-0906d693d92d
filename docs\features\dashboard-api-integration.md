# 🔌 Dashboard API Integration Guide

## 📋 Overview

This document provides comprehensive information about the Dashboard API integration, including endpoints, data models, authentication, and implementation patterns used in the Jadwa Investment Web Application.

## 🌐 API Endpoints

### 🎯 Auto Dashboard Endpoint
**Endpoint**: `GET /api/Dashboard/AutoDashboard`

**Description**: Automatically detects the user's role and returns the appropriate dashboard data.

**Parameters**:
- `fromDate?: DateTime` - Start date for data filtering
- `toDate?: DateTime` - End date for data filtering  
- `recentItemsLimit?: number` - Limit for recent items (default: 10)

**Response**: `BaseDashboardResponseBaseResponse`

**Usage**:
```typescript
// Service method
getAutoDashboard(fromDate?: Date, toDate?: Date, recentItemsLimit: number = 10) {
  const fromDateTime = fromDate ? DateTime.fromJSDate(fromDate) : undefined;
  const toDateTime = toDate ? DateTime.fromJSDate(toDate) : undefined;
  
  return this.dashboardProxy.autoDashboard(fromDateTime, toDateTime, recentItemsLimit)
    .pipe(
      catchError(error => this.handleError(error)),
      shareReplay(1)
    );
}
```

### 👨‍💼 Fund Manager Dashboard
**Endpoint**: `GET /api/Dashboard/FundManagerDashboard`

**Description**: Returns dashboard data specific to Fund Manager role.

**Parameters**:
- `fromDate?: DateTime` - Start date for data filtering
- `toDate?: DateTime` - End date for data filtering
- `recentItemsLimit?: number` - Limit for recent items (default: 10)
- `includePerformanceMetrics?: boolean` - Include performance data (default: true)
- `includePendingAssessments?: boolean` - Include pending assessments (default: true)

**Response**: `FundManagerDashboardResponseBaseResponse`

**Additional Data**:
- `managedFunds: FundSummaryDto[]` - List of managed funds
- `performanceMetrics: PerformanceMetricsDto` - Fund performance data
- `pendingApprovals: PendingApprovalDto[]` - Items requiring approval
- `fundStatusDistribution: FundStatusDistributionDto` - Fund status breakdown

### ⚖️ Legal Council Dashboard
**Endpoint**: `GET /api/Dashboard/LegalCouncilDashboard`

**Description**: Returns dashboard data specific to Legal Council role.

**Parameters**:
- `fromDate?: DateTime` - Start date for data filtering
- `toDate?: DateTime` - End date for data filtering
- `recentItemsLimit?: number` - Limit for recent items (default: 10)
- `includeComplianceMetrics?: boolean` - Include compliance data (default: true)
- `includeRegulatoryUpdates?: boolean` - Include regulatory updates (default: true)

**Response**: `LegalCouncilDashboardResponseBaseResponse`

**Additional Data**:
- `complianceStatus: ComplianceStatusDto` - Compliance metrics
- `pendingReviews: DocumentReviewDto[]` - Documents pending review
- `regulatoryUpdates: RegulatoryUpdateDto[]` - Recent regulatory changes
- `legalMetrics: LegalMetricsDto` - Legal department KPIs

### 📝 Board Secretary Dashboard
**Endpoint**: `GET /api/Dashboard/BoardSecretaryDashboard`

**Description**: Returns dashboard data specific to Board Secretary role.

**Parameters**:
- `fromDate?: DateTime` - Start date for data filtering
- `toDate?: DateTime` - End date for data filtering
- `recentItemsLimit?: number` - Limit for recent items (default: 10)
- `includeMeetingPreparation?: boolean` - Include meeting prep data (default: true)
- `includeBoardMemberActivity?: boolean` - Include member activity (default: true)

**Response**: `BoardSecretaryDashboardResponseBaseResponse`

**Additional Data**:
- `upcomingMeetings: MeetingSummaryDto[]` - Scheduled meetings
- `meetingPreparation: MeetingPreparationDto[]` - Meeting preparation tasks
- `boardMemberActivity: BoardMemberActivityDto[]` - Member participation data
- `documentStatus: DocumentStatusDto` - Document preparation status

### 👥 Board Member Dashboard
**Endpoint**: `GET /api/Dashboard/BoardMemberDashboard`

**Description**: Returns dashboard data specific to Board Member role.

**Parameters**:
- `fromDate?: DateTime` - Start date for data filtering
- `toDate?: DateTime` - End date for data filtering
- `recentItemsLimit?: number` - Limit for recent items (default: 10)
- `includeVotingHistory?: boolean` - Include voting history (default: true)
- `includeParticipationMetrics?: boolean` - Include participation data (default: true)

**Response**: `BoardMemberDashboardResponseBaseResponse`

**Additional Data**:
- `pendingVotes: VotingSummaryDto[]` - Resolutions requiring votes
- `votingHistory: VotingHistoryDto[]` - Historical voting records
- `participationMetrics: ParticipationMetricsDto` - Engagement statistics
- `meetingAttendance: AttendanceDto[]` - Meeting attendance records

## 📊 KPI Endpoints

### 📈 Dashboard KPIs
**Endpoint**: `GET /api/Dashboard/DashboardKPIs`

**Description**: Returns key performance indicators for the dashboard.

**Parameters**:
- `fromDate?: DateTime` - Start date for metrics calculation
- `toDate?: DateTime` - End date for metrics calculation
- `roleFilter?: string` - Filter by specific role
- `includeFundMetrics?: boolean` - Include fund-related KPIs (default: true)
- `includeResolutionMetrics?: boolean` - Include resolution KPIs (default: true)
- `includeEngagementMetrics?: boolean` - Include engagement KPIs (default: true)
- `includeMeetingMetrics?: boolean` - Include meeting KPIs (default: true)
- `includeAssessmentMetrics?: boolean` - Include assessment KPIs (default: true)

**Response**: `DashboardKPIsDtoBaseResponse`

**KPI Data Structure**:
```typescript
interface DashboardKPIsDto {
  // Fund Metrics
  totalFunds?: number;
  activeFunds?: number;
  fundPerformanceAverage?: number;
  
  // Resolution Metrics
  totalResolutions?: number;
  pendingResolutions?: number;
  approvedResolutions?: number;
  rejectedResolutions?: number;
  
  // Member Metrics
  totalMembers?: number;
  activeMembers?: number;
  memberParticipationRate?: number;
  
  // Meeting Metrics
  totalMeetings?: number;
  upcomingMeetings?: number;
  averageAttendance?: number;
  
  // Assessment Metrics
  totalAssessments?: number;
  pendingAssessments?: number;
  completedAssessments?: number;
  assessmentCompletionRate?: number;
}
```

## 🔔 Notification & Activity Endpoints

### 📬 Recent Notifications
**Endpoint**: `GET /api/Dashboard/RecentNotifications`

**Description**: Returns recent notifications for the current user.

**Parameters**:
- `limit?: number` - Maximum number of notifications (default: 10)
- `unreadOnly?: boolean` - Return only unread notifications (default: false)
- `notificationTypeFilter?: number` - Filter by notification type
- `fundIdFilter?: number` - Filter by specific fund
- `fromDate?: DateTime` - Start date for filtering
- `toDate?: DateTime` - End date for filtering

**Response**: `NotificationSummaryDtoListBaseResponse`

### 📋 Recent Activities
**Endpoint**: `GET /api/Dashboard/RecentActivities`

**Description**: Returns recent system activities.

**Parameters**:
- `limit?: number` - Maximum number of activities (default: 10)
- `activityTypeFilter?: string` - Filter by activity type
- `entityTypeFilter?: string` - Filter by entity type
- `fundIdFilter?: number` - Filter by specific fund
- `fromDate?: DateTime` - Start date for filtering
- `toDate?: DateTime` - End date for filtering
- `currentUserOnly?: boolean` - Show only current user's activities (default: false)

**Response**: `ActivitySummaryDtoListBaseResponse`

## 🔐 Authentication & Authorization

### 🎫 JWT Authentication
All dashboard endpoints require valid JWT authentication:

```typescript
// HTTP headers required
{
  'Authorization': 'Bearer <jwt-token>',
  'Content-Type': 'application/json'
}
```

### 🛡️ Role-Based Access Control
- **Fund Manager**: Access to fund-related data and metrics
- **Legal Council**: Access to compliance and legal data
- **Board Secretary**: Access to meeting and administrative data
- **Board Member**: Access to voting and participation data

### 🔒 Data Filtering
- Users only see data they have permission to access
- Fund-specific data filtered by user's fund associations
- Role-based KPI visibility and calculations

## 📝 Data Models

### 🏗️ Base Dashboard Response
```typescript
interface BaseDashboardResponse {
  userName: string;
  userRole: string;
  generatedAt: DateTime;
  kpIs: DashboardKPIsDto;
  recentNotifications: NotificationSummaryDto[];
  recentActivities: ActivitySummaryDto[];
  quickActions: QuickActionDto[];
}
```

### 📊 Notification Summary
```typescript
interface NotificationSummaryDto {
  id: number;
  title: string;
  body: string;
  createdAt: DateTime;
  isRead: boolean;
  notificationType: string;
  fundId?: number;
  resolutionId?: number;
  meetingId?: number;
}
```

### 📈 Activity Summary
```typescript
interface ActivitySummaryDto {
  id: number;
  description: string;
  activityType: string;
  occurredAt: DateTime;
  userName: string;
  fundId?: number;
  resolutionId?: number;
  meetingId?: number;
}
```

## 🔄 Error Handling

### ❌ Error Response Format
```typescript
interface BaseResponse {
  statusCode: number;
  successed: boolean;
  message: string;
  errors: string[];
}
```

### 🚨 Common Error Scenarios
- **401 Unauthorized**: Invalid or expired JWT token
- **403 Forbidden**: Insufficient permissions for requested data
- **404 Not Found**: Requested resource doesn't exist
- **500 Internal Server Error**: Server-side processing error

### 🛠️ Error Handling Implementation
```typescript
private handleError(error: any): Observable<never> {
  console.error('Dashboard Service Error:', error);
  this.errorModalService.showError(error);
  throw error;
}
```

## ⚡ Performance Optimizations

### 🎯 Caching Strategy
```typescript
// ShareReplay for API response caching
return this.dashboardProxy.autoDashboard(fromDateTime, toDateTime, recentItemsLimit)
  .pipe(
    catchError(error => this.handleError(error)),
    shareReplay(1) // Cache the last emission
  );
```

### 📊 Data Pagination
- Recent items limited to prevent large payloads
- Configurable limits for different data types
- Efficient database queries with proper indexing

### 🔄 Request Optimization
- Batch multiple related requests when possible
- Use appropriate HTTP caching headers
- Implement request debouncing for user interactions

## 🧪 Testing API Integration

### 🔬 Unit Tests
```typescript
describe('DashboardService', () => {
  it('should call autoDashboard with correct parameters', () => {
    const fromDate = new Date('2023-01-01');
    const toDate = new Date('2023-12-31');
    
    service.getAutoDashboard(fromDate, toDate, 15).subscribe();
    
    expect(dashboardProxy.autoDashboard).toHaveBeenCalledWith(
      DateTime.fromJSDate(fromDate),
      DateTime.fromJSDate(toDate),
      15
    );
  });
});
```

### 🎭 Mock Data
```typescript
const mockDashboardResponse = {
  statusCode: 200,
  successed: true,
  message: 'Success',
  data: {
    userName: 'Test User',
    userRole: 'FundManager',
    generatedAt: DateTime.fromJSDate(new Date()),
    kpIs: { totalFunds: 10, activeFunds: 8 },
    recentNotifications: [],
    recentActivities: []
  }
};
```

## 🔮 Future API Enhancements

### 📡 Planned Improvements
- **GraphQL Integration**: More efficient data fetching
- **WebSocket Support**: Real-time dashboard updates
- **Batch Operations**: Multiple dashboard requests in single call
- **Advanced Filtering**: Complex query capabilities
- **API Versioning**: Backward compatibility support

### 📊 Analytics Integration
- **Usage Tracking**: Dashboard interaction analytics
- **Performance Metrics**: API response time monitoring
- **Error Tracking**: Comprehensive error logging and alerting
