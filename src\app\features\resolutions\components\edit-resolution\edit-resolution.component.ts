import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import {
  FormGroup,
  FormBuilder,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import Swal from 'sweetalert2';

// Shared Components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

// Shared Interfaces and Enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// API Generated Types
import {
  ResolutionsServiceProxy,
  EditResolutionCommand,
  SingleResolutionResponse,
  ResolutionStatusEnum,
  VotingType,
  MemberVotingResult,
  ResolutionItemDto,
  TypesServiceProxy,
  ResolutionTypeDto,
  ResolutionForEditDto,
  AddResolutionCommand,
} from '@core/api/api.generated';

// Services
import { ErrorModalService } from '@core/services/error-modal.service';

// Dialog Components
import {
  ResolutionItemDialogComponent,
  ResolutionItemDialogData,
} from './resolution-item-dialog/resolution-item-dialog.component';
import {
  ConflictMembersDialogComponent,
  ConflictMembersDialogData,
} from './conflict-members-dialog/conflict-members-dialog.component';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { DateConversionService } from '@shared/services/date.service';
import moment from 'moment';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { AttachmentModule } from '@shared/enum/AttachmentModule';
import { DateTime } from 'luxon';
import { EditResolutionPopupComponent } from './edit-resolution-popup/edit-resolution-popup.component';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-edit-resolution',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    PageHeaderComponent,
    BreadcrumbComponent,
  ],
  providers: [ResolutionsServiceProxy, TypesServiceProxy],
  templateUrl: './edit-resolution.component.html',
  styleUrl: './edit-resolution.component.scss',
})
export class EditResolutionComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  // Track if navigation came from inbox
  fromInbox: boolean = false;
  // Form Properties
  formGroup!: FormGroup;
  isFormSubmitted = false;
  isValidationFire = false;
  isLoading = false;
  isSubmitting = false;
  showCustomTypeField: boolean = false;
  showOldResolutionCodeField: boolean = true;

  // Component Data
  resolutionId!: number;
  fundId!: number;
  currentResolution!: ResolutionForEditDto;
  resolutionTypes: ResolutionTypeDto[] = [];
  resolutionItems: ResolutionItemDto[] = [];
  additionalAttachments: any[] = [];
  maxAttachments = 10;
  maxFileSize = 10485760; // 10MB

  // UI Properties
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  CanAddItems: boolean = true;
  canAddAdditionalAttachments: boolean = true;
  // Form Controls Configuration - Following create-resolution pattern
  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'resolutionCode',
      id: 'resolutionCode',
      name: 'resolutionCode',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_CODE',
      placeholder: '',
      isRequired: true,
      isReadonly: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Text,
      formControlName: 'oldResolutionCode',
      id: 'oldResolutionCode',
      name: 'oldResolutionCode',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_OLD_CODE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.OLD_RESOLUTION_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-4',
      isVisible: () => this.showOldResolutionCodeField,
    },
    {
      type: InputType.StatusBadge,
      formControlName: 'status',
      id: 'status',
      name: 'status',
      label: 'RESOLUTIONS.STATUS',
      placeholder: '',
      isRequired: true,
      isReadonly: true, // Status is display-only
      class: 'col-md-4',
    },
    {
      type: InputType.Date,
      formControlName: 'resolutionDate',
      id: 'resolutionDate',
      name: 'resolutionDate',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.DECISION_DATE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ENTER_DECISION_DATE',
      isRequired: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'typeId',
      id: 'typeId',
      name: 'typeId',
      label: 'RESOLUTIONS.TYPE',
      placeholder: 'RESOLUTIONS.TYPE_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
      options: [],
      onChange: (value: any) => this.dropdownChanged(value),
    },

    {
      type: InputType.Text,
      formControlName: 'customTypeName',
      id: 'customTypeName',
      name: 'customTypeName',
      label: 'RESOLUTIONS.CUSTOM_TYPE',
      placeholder: 'RESOLUTIONS.CUSTOM_TYPE_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-4',
      showOptional:false,
      maxLength: 100,
      isVisible: () => this.showCustomTypeField,

    },
    {
      type: InputType.Radio,
      formControlName: 'votingType',
      id: 'votingType',
      name: 'votingType',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.VOTING_METHODOLOGY',
      isRequired: true,
      class: 'col-md-4',
      options: [
        { name: 'INVESTMENT_FUNDS.RESOLUTIONS.ALL_MEMBERS', id: VotingType._1 },
        {
          name: 'INVESTMENT_FUNDS.RESOLUTIONS.MAJORITY_MEMBERS',
          id: VotingType._2,
        },
      ],
    },
    {
      type: InputType.Radio,
      formControlName: 'memberVotingResult',
      id: 'memberVotingResult',
      name: 'memberVotingResult',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.VOTING_RESULT_CALCULATION',
      isRequired: true,
      class: 'col-md-4',
      options: [
        {
          name: 'INVESTMENT_FUNDS.RESOLUTIONS.ALL_ITEMS',
          id: MemberVotingResult._1,
        },
        {
          name: 'INVESTMENT_FUNDS.RESOLUTIONS.MAJORITY_ITEMS',
          id: MemberVotingResult._2,
        },
      ],
    },
    {
      type: InputType.Textarea,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.DESCRIPTION',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ENTER_DESCRIPTION',
      isRequired: false,
      maxLength: 500,
      class: 'col-md-12',
    },
    {
      type: InputType.Custom,
      formControlName: '',
      id: '',
      name: '',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS',
      class: 'header mt-2',
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_FILE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.UPLOAD_FILE',
      isRequired: true,
      class: this.canAddAdditionalAttachments ? 'col-md-6' : 'col-md-12',
      allowedTypes: ['.pdf'],
      max: 10,
      moduleId : AttachmentModule.Resolution
    },
    {
      type: InputType.file,
      formControlName: 'attachmentIds',
      id: 'attachmentIds',
      name: 'attachmentIds',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.UPLOAD_FILE',
      isRequired: false,
      class: 'col-md-6',
      allowedTypes: ['.pdf'],
      max: 10,
      maxLength:10,
      multiple: true,
      isVisible: () => this.canAddAdditionalAttachments,
      moduleId : AttachmentModule.Resolution
    },
  ];
  maxGreg?: NgbDateStruct;
  maxHijri?: NgbDateStruct;
  title: string = 'INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION';
  draftButtonTitle: string = '';

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private resolutionsProxy: ResolutionsServiceProxy,
    private typesProxy: TypesServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService,
    private dialog: MatDialog,
    private DateConversionService: DateConversionService,
    private tokenService: TokenService,
    private breadcrumbService: BreadcrumbService
  ) {
    this.initForm();
    // Initialize breadcrumb with default values
    this.updateBreadcrumbWithFallback();
  }

  ngOnInit(): void {
     // Check if navigation came from inbox
    const fromInboxValue = localStorage.getItem('fromInbox');
    this.fromInbox = fromInboxValue === 'true';
    localStorage.removeItem('fromInbox');

    if (this.tokenService.hasRole('fundmanager')
      || this.tokenService.hasRole('associatedfundmanager')
      || this.tokenService.hasRole('financecontroller')
      || this.tokenService.hasRole('headofrealestate'))
      this.draftButtonTitle = this.translateService.instant(
        'INVESTMENT_FUNDS.RESOLUTIONS.SAVE_AS_DRAFT'
      );
    else this.draftButtonTitle = this.translateService.instant('COMMON.SAVE');

    this.getRouteParams();
    this.loadResolutionTypes();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onValueChange(e: any, item: IControlOption) {
    const value = e.event.target.value;
    e.event.target.value = value.replace(/[^a-zA-Z\u0600-\u06FF\s]/g, '');
    this.formGroup.get(item.formControlName)?.setValue(e.event.target.value);
  }

  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      status: [''], // Status field for display
      resolutionCode: [''],
      oldResolutionCode: [''],
      typeId: ['', Validators.required],
      resolutionDate: ['', Validators.required],
      customTypeName: [''],
      description: [''],
      votingType: ['', Validators.required],
      memberVotingResult: ['', Validators.required],
      attachmentId: [0, Validators.required],
      attachmentIds: [[]],
    });
  }

  private getRouteParams(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        this.resolutionId = +params['id'];
        this.fundId = +params['fundId'];

        if (this.resolutionId && this.fundId) {
          this.loadResolutionData();
        } else {
          this.errorModalService.showError(
            'INVESTMENT_FUNDS.RESOLUTIONS.INVALID_PARAMETERS'
          );
          // this.router.navigate(['/admin/investment-funds/resolutions']);
          this.navigateAfterAction();

        }
      });
  }

  private loadResolutionData(): void {
    this.isLoading = true;

    this.resolutionsProxy
      .getResolutionForEdit(this.resolutionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.currentResolution = response.data;
            this.populateForm();
            // Update breadcrumb with fund context after data is loaded
            this.updateBreadcrumbWithFallback();
            this.setDateMaxAndMin();
            this.title = this.tokenService.hasRole('fundmanager')
            || this.tokenService.hasRole('associatedfundmanager')
            || this.tokenService.hasRole('financecontroller')
            || (this.tokenService.hasRole('headofrealestate') && this.currentResolution.status == ResolutionStatusEnum._1)
              ? 'INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION'
              : this.currentResolution.status == ResolutionStatusEnum._2 ||
                this.currentResolution.status == ResolutionStatusEnum._3
              ? 'INVESTMENT_FUNDS.RESOLUTIONS.Complete_RESOLUTION'
              : 'INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION';
          } else {
            this.errorModalService.showError(
              response.message || 'INVESTMENT_FUNDS.RESOLUTIONS.LOAD_ERROR'
            );
            // this.router.navigate(['/admin/investment-funds/resolutions']);
            this.navigateAfterAction();

          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error loading resolution:', error);
        },
      });
  }
  private populateForm(): void {
    if (!this.currentResolution) return;

    // Populate form with proper API data binding
    this.formGroup.patchValue({
      status: this.currentResolution.statusName, // Add status display value
      resolutionCode: this.currentResolution.code || '',
      oldResolutionCode: this.currentResolution.oldResolutionCode, // Will be populated from API when available
      typeId: this.currentResolution.resolutionTypeId || '',
      customTypeName: this.currentResolution.newType,
      description: this.currentResolution.description || '',
      votingType: this.currentResolution.votingType,
      memberVotingResult: this.currentResolution.memberVotingResult,
      attachmentId: this.currentResolution.attachment.id,
      attachmentIds:
        this.currentResolution.otherAttachments?.map((a) => a.id) || [],
    });

    // Populate resolution items from API response
    this.resolutionItems = this.currentResolution.resolutionItems || [];

    // Check if custom type field should be visible based on the loaded data
    this.checkIfOtherTypeSelected();

    this.setDateValues();
    const attachment = this.currentResolution.attachment;
    const otherAttachments = this.currentResolution.otherAttachments || [];

    this.formControls.find(
      (c) => c.formControlName === 'attachmentId'
    )!.initialFiles = [attachment];

    this.formControls.find(
      (c) => c.formControlName === 'attachmentIds'
    )!.initialFiles = [...otherAttachments];

    this.formGroup.get('resolutionCode')?.disable();
    this.setTheMood();
   this.setFormControlVisibility();

  }
  setFormControlVisibility() {
    this.formControls.find((c) => c.formControlName === 'attachmentId')!.class= this.canAddAdditionalAttachments ? 'col-md-6' : 'col-md-12';

  }

  setTheMood() {
    if (
     ( this.tokenService.hasRole('fundmanager')
      || this.tokenService.hasRole('associatedfundmanager')
      || this.tokenService.hasRole('financecontroller')
    || (this.tokenService.hasRole('headofrealestate') && this.currentResolution.status == ResolutionStatusEnum._1)) &&
      (this.currentResolution.status == ResolutionStatusEnum._1 ||
        this.currentResolution.status == ResolutionStatusEnum._2)
    ) {
      this.showOldResolutionCodeField = false;
      this.CanAddItems = false;
      this.canAddAdditionalAttachments = false;
      this.formControls.find((c) => c.formControlName === 'status')!.class='col-md-8';

    }
  }
  setDateMaxAndMin() {
    const today = new Date();
    const minGreg: NgbDateStruct = {
      year: this.currentResolution.fundInitiationDate.year,
      month: this.currentResolution.fundInitiationDate.month,
      day: this.currentResolution.fundInitiationDate.day,
    };
    const minHijri: NgbDateStruct =
      this.DateConversionService.convertGregorianToHijri(minGreg);

    this.maxGreg = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate(),
    };

    this.maxHijri = this.DateConversionService.convertGregorianToHijri(
      this.maxGreg
    );
    // this.initForm();
    const field = this.formControls.find(
      (f) => f.formControlName === 'resolutionDate'
    );
    if (field) {
      field.maxGreg = this.maxGreg;
      field.maxHijri = this.maxHijri;
      field.minHijri = minHijri;
      field.minGreg = minGreg;
    }
  }
  setDateValues(): void {
  const isoString:any = this.currentResolution.resolutionDate;
  if (!isoString) return;
  const luxonDate = DateTime.fromISO(isoString);
  const dateString = luxonDate.toISODate(); // This is now a string
  moment.locale('en');
  const formattedDate = moment(dateString).format('DD-MM-YYYY');
  const resolutionDate = this.DateConversionService.mapStringToSelectedDate(formattedDate);
  this.formGroup.get('resolutionDate')?.setValue(resolutionDate);
  }



  private loadResolutionTypes(): void {
    this.typesProxy
      .all()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.successed && response.data) {
            this.resolutionTypes = response.data;
            this.updateResolutionTypesOptions();

            // Re-check custom type visibility after types are loaded
            if (this.currentResolution) {
              this.checkIfOtherTypeSelected();
            }
          } else {
            console.warn('Failed to load resolution types:', response.message);
            // Fallback to mock data if API fails
          }
        },
        error: (error) => {
          console.error('Error loading resolution types:', error);
          // Fallback to mock data if API fails
        },
      });
  }

  private updateResolutionTypesOptions(): void {
    const typeControl = this.formControls.find(
      (control) => control.formControlName === 'typeId'
    );
    if (typeControl) {
      typeControl.options = this.resolutionTypes.map((type) => ({
        id: type.id,
        name: type.localizedName || type.nameEn || type.nameAr || 'Unknown',
      }));
    }
  }

  dropdownChanged(event: any): void {
    const selectedId = event?.id || event?.event?.id;
    const customTypeNameControl = this.formControls.find((control) => control.formControlName === 'customTypeName');

    if (selectedId === 10) {
      if (customTypeNameControl) {
        customTypeNameControl.isRequired = true;
      }
      this.formGroup.get('customTypeName')?.setValidators([Validators.required, Validators.maxLength(100)]);      // Show custom type field when "Other" is selected
      this.formGroup.get('customTypeName')?.updateValueAndValidity();
      this.showCustomTypeField = true;
    } else {
      if (customTypeNameControl) {
        customTypeNameControl.isRequired = false;
      }
      this.formGroup.get('customTypeName')?.clearValidators();
      this.formGroup.get('customTypeName')?.updateValueAndValidity();
      // Hide custom type field for other selections
      this.showCustomTypeField = false;
      // Clear the custom type value
      this.formGroup.get('customTypeName')?.setValue('');
    }
  }

  private checkIfOtherTypeSelected(): void {
    const currentTypeId = this.formGroup.get('typeId')?.value;

    // Find the selected type in our loaded types
    const selectedType = this.resolutionTypes.find(
      (type) => type.id === currentTypeId
    );

    // Check if the selected type is marked as "Other" or if current resolution type is "Other"
    if (
      selectedType?.isOther ||
      this.currentResolution?.resolutionTypeId == 10
    ) {
      this.showCustomTypeField = true;
      // Add required validator for custom type when "Other" is selected
      this.formGroup
        .get('customTypeName')
        ?.setValidators([Validators.required, Validators.maxLength(100)]);
    } else {
      this.showCustomTypeField = false;
      // Remove required validator when "Other" is not selected
      this.formGroup.get('customTypeName')?.clearValidators();
      this.formGroup.get('customTypeName')?.setValue('');
    }
    this.formGroup.get('customTypeName')?.updateValueAndValidity();
  }

  dateSelected(event: any): void {
    // Handle date selection from the date picker component
    const formattedDate = event.event?.formattedGregorian || event.event;
    this.formGroup.get(event.control.formControlName)?.setValue(formattedDate);
  }

  onFileUpload(event: any): void {
  const controlName = event?.control?.formControlName;

  if (!controlName) return;

  if (controlName === 'attachmentId') {
    if (!event.file) {
      // Single file removed - clear form control and reset validation
      this.formGroup.patchValue({ attachmentId: null });
      this.formGroup.get('attachmentId')?.markAsTouched();
      this.formGroup.get('attachmentId')?.updateValueAndValidity();
    } else {
      // Single file uploaded
      setTimeout(() => {
        this.formGroup.patchValue({ attachmentId: event.file.id });
        this.formGroup.get('attachmentId')?.markAsTouched();
        this.formGroup.get('attachmentId')?.updateValueAndValidity();
      }, 1000);
    }
  }

  else if (controlName === 'attachmentIds') {
    const control = this.formGroup.get('attachmentIds');
    const existingIds = control?.value || [];

    if (!event.file) {
      // All files removed - clear form control and reset validation
      this.formGroup.patchValue({ attachmentIds: [] });
      control?.markAsUntouched();
      control?.setErrors(null);
    }

    else if (Array.isArray(event.file)) {
      // Handle updated list after add/remove
      const newIds = event.file.map((file: any) => file.id).filter((id: any) => id); // Filter out null/undefined
      this.formGroup.patchValue({ attachmentIds: newIds });
      control?.markAsTouched();

      // Clear errors if files are present
      if (newIds.length > 0) {
        control?.setErrors(null);
      }
    }

    else if (event.file && event.file.id) {
      // Single file uploaded (in multi-file mode)
      const newId = event.file.id;
      const updatedIds = existingIds.includes(newId)
        ? existingIds
        : [...existingIds, newId];

      this.formGroup.patchValue({ attachmentIds: updatedIds });
      control?.markAsTouched();
    }
  }
}


  onSubmit(saveAsDraft: boolean = false): void {
    if (this.isSubmitting) return;
    this.isSubmitting = true;
    this.isFormSubmitted = true;
    this.isValidationFire = true;

    // Basic validation check
    if (!this.isFormValid(saveAsDraft)) {
      this.isSubmitting = false;
      this.formGroup.markAllAsTouched();
      return;
    }

    // Handle status-specific business logic
    this.handleStatusSpecificSubmission(saveAsDraft);
  }

  private handleStatusSpecificSubmission(saveAsDraft: boolean): void {
    if (!this.currentResolution) {
      this.isSubmitting = false;
      return;
    }
    switch (this.currentResolution.status) {
      case ResolutionStatusEnum._1: // Draft
        this.handleDraftSubmission(saveAsDraft);
        break;
      case ResolutionStatusEnum._2: // Pending
      case ResolutionStatusEnum._3: // Completing Data
        this.handlePendingSubmission(saveAsDraft);
        break;
      case ResolutionStatusEnum._4: // Waiting for confirmation
      case ResolutionStatusEnum._5: // Confirmed
      case ResolutionStatusEnum._6: // Rejected
        this.handleStandardEdit();
        break;
      case ResolutionStatusEnum._7: // Voting in progress
        this.handleVotingInProgressEdit();
        break;
      case ResolutionStatusEnum._8: // Approved
      case ResolutionStatusEnum._9: // Not approved
        this.handleApprovedNotApprovedEdit();
        break;
      default:
        this.isSubmitting = false;
        this.errorModalService.showError(
          'INVESTMENT_FUNDS.RESOLUTIONS.INVALID_STATUS'
        );
        break;
    }
  }

  private isFormValid(saveAsDraft: boolean): boolean {
    const formValue = this.formGroup.value;

    if (saveAsDraft) {
      // For draft, only date and type are required
      if (!formValue.resolutionDate) {
        this.showValidationError('INVESTMENT_FUNDS.RESOLUTIONS.DATE_REQUIRED');
        return false;
      }
      if (!formValue.typeId) {
        this.showValidationError('INVESTMENT_FUNDS.RESOLUTIONS.TYPE_REQUIRED');
        return false;
      }
      if (this.isCustomTypeRequired()) {
        if(!formValue.customTypeName?.trim()){
          this.showValidationError('INVESTMENT_FUNDS.RESOLUTIONS.CUSTOM_TYPE_REQUIRED');
          return false;
        }else if (formValue.customTypeName?.length > 100){
          this.showValidationError(`INVESTMENT_FUNDS.RESOLUTIONS.MAX_LENGTH_ERROR`);
          return false;
        }
      }



      if (!formValue.attachmentId || formValue.attachmentId === 0) {
        this.showValidationError('INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENT_REQUIRED');
        return false;
      }

      return true;
    }

    // For sending, all required fields must be filled
    const validationErrors: string[] = [];

    if (!formValue.resolutionDate) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.DATE_REQUIRED');
    }
    if (!formValue.typeId) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.TYPE_REQUIRED');
    }
    if (!formValue.votingType) {
      validationErrors.push(
        'INVESTMENT_FUNDS.RESOLUTIONS.VOTING_TYPE_REQUIRED'
      );
    }
    if (!formValue.memberVotingResult) {
      validationErrors.push(
        'INVESTMENT_FUNDS.RESOLUTIONS.VOTING_RESULT_REQUIRED'
      );
    }



    if (!formValue.attachmentId || formValue.attachmentId === 0) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENT_REQUIRED');
    }

    // Check custom type if "Other" is selected
    if (this.isCustomTypeRequired()) {
      if(!formValue.customTypeName?.trim()){
          validationErrors.push(
          'INVESTMENT_FUNDS.RESOLUTIONS.CUSTOM_TYPE_REQUIRED'
        );
      }else if (formValue.customTypeName?.length > 100){
        validationErrors.push(
          'INVESTMENT_FUNDS.RESOLUTIONS.MAX_LENGTH_ERROR'
        );
      }

    }

    if (validationErrors.length > 0) {
      // this.showValidationError(validationErrors[0]); // Show first error
      return false;
    }

    return true;
  }

  private showValidationError(messageKey: string): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.VALIDATION_ERROR'),
      text: this.translateService.instant(messageKey),
      imageUrl: 'assets/images/warning-yellow.svg',
      confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.CONFIRM'),
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
      },
    });
  }

  isCustomTypeRequired(): boolean {
    const typeId = this.formGroup.value.typeId;
    const selectedType = this.resolutionTypes.find(
      (type) => type.id === typeId
    );
    return selectedType?.isOther || typeId === 10 || false;
  }

  // Breadcrumb Methods
  private updateBreadcrumbWithFallback(): void {
    if(this.fromInbox ){
      this.breadcrumbItems = [

        {
          label: 'RESOLUTIONS.INBOX_TITLE',
          url: `/admin/investment-funds/resolutions/inbox`
        },
        { label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DETAILS', url: '', disabled: true }
   ];

    }else{
    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      {
        label: 'RESOLUTIONS.TITLE',
        url: `/admin/investment-funds/resolutions?fundId=${this.fundId}`
      },
      {
        label: this.tokenService.hasRole('fundmanager')
          ? 'INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION'
          : this.currentResolution?.status == ResolutionStatusEnum._2 ||
            this.currentResolution?.status == ResolutionStatusEnum._3
          ? 'INVESTMENT_FUNDS.RESOLUTIONS.Complete_RESOLUTION'
          : 'INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION',
        url: '',
        active: true,
        disabled: true,
      },
   ];
  }
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  onBreadcrumbClicked(event: IBreadcrumbItem): void {
    if (!event.disabled && event.url) {
      this.router.navigateByUrl(event.url);
    }
  }

  // Status-specific submission handlers
  private handleDraftSubmission(saveAsDraft: boolean): void {
    if (!saveAsDraft && this.resolutionItems.length === 0 &&( this.tokenService.hasRole('legalcouncil') || this.tokenService.hasRole('boardsecretary') ||
    ( this.tokenService.hasRole('headofrealestate') && this.currentResolution.status != ResolutionStatusEnum._1))) {
      this.showNoItemsConfirmation(() => {
        this.submitResolution(saveAsDraft, ResolutionStatusEnum._1);
      });
    } else {
      this.submitResolution(saveAsDraft, ResolutionStatusEnum._1);
    }
  }

  private handlePendingSubmission(saveAsDraft: boolean): void {
    // JDWA-509: Send (pending remains pending)
    if (!saveAsDraft && this.resolutionItems.length === 0 && (this.tokenService.hasRole('legalcouncil') ||
     ( this.tokenService.hasRole('headofrealestate') && this.currentResolution.status != ResolutionStatusEnum._1))) {
      this.showNoItemsConfirmation(() => {
        this.submitResolution(saveAsDraft, ResolutionStatusEnum._2);
      });
    } else {
      this.submitResolution(saveAsDraft, ResolutionStatusEnum._2);
    }
  }

  private handleStandardEdit(): void {
    // JDWA-567: Standard edit for waiting/confirmed/rejected
    if (this.resolutionItems.length === 0) {
      this.showNoItemsConfirmation(() => {
        this.submitResolution(false, ResolutionStatusEnum._4); // Waiting for confirmation
      });
    } else {
      this.submitResolution(false, ResolutionStatusEnum._4); // Waiting for confirmation
    }
  }

  private handleVotingInProgressEdit(): void {
    // JDWA-567: Voting in progress - show vote suspension warning
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM'),
      text: this.translateService.instant(
        'INVESTMENT_FUNDS.RESOLUTIONS.VOTE_SUSPENSION_WARNING'
      ),
      imageUrl: 'assets/images/confirmation-green.svg',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
        cancelButton: 'btn outline-btn',
      },
      confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.CONFIRM'),
      cancelButtonText: `<img src="assets/images/back-icon.png" alt="back" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.BACK')
    }).then((result) => {
      if (result.isConfirmed) {
        // Suspend voting and update resolution
        if (this.resolutionItems.length === 0) {
          this.showNoItemsConfirmation(() => {
            this.submitResolution(false, ResolutionStatusEnum._4); // Waiting for confirmation
          });
        } else {
          this.submitResolution(false, ResolutionStatusEnum._4); // Waiting for confirmation
        }
      } else {
        this.isSubmitting = false;
      }
    });
  }

  private handleApprovedNotApprovedEdit(): void {
    // JDWA-567: Approved/Not approved - create referral resolution
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM'),
      text: this.translateService.instant(
        'INVESTMENT_FUNDS.RESOLUTIONS.REFERRAL_RESOLUTION_WARNING'
      ),
      imageUrl: 'assets/images/confirmation-green.svg',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
        cancelButton: 'btn outline-btn',
      },
      confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.CONFIRM'),
      cancelButtonText: `<img src="assets/images/back-icon.png" alt="back" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.BACK')
    }).then((result) => {
      if (result.isConfirmed) {
        // Create referral resolution
        this.submitReferralResolution();
      } else {
        this.isSubmitting = false;
      }
    });
  }

  private showNoItemsConfirmation(callback: () => void): void {
    // MSG010: No items confirmation for legal council
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM'),
      text: this.translateService.instant(
        'INVESTMENT_FUNDS.RESOLUTIONS.NO_ITEMS_CONFIRMATION'
      ),
      imageUrl: 'assets/images/confirmation-green.svg',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
        cancelButton: 'btn outline-btn',
      },
      confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.CONFIRM'),
      cancelButtonText: `<img src="assets/images/back-icon.png" alt="back" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.BACK')
    }).then((result) => {
      if (result.isConfirmed) {
        callback();
      } else {
        this.isSubmitting = false;
      }
    });
  }

  private submitResolution(
    saveAsDraft: boolean,
    targetStatus: ResolutionStatusEnum
  ): void {
    const command = this.buildEditResolutionCommand(saveAsDraft, targetStatus);

    this.resolutionsProxy
      .editResolution(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isSubmitting = false;
          if (response.successed) {
            this.handleSubmissionSuccess(saveAsDraft, targetStatus);
          } else {
            this.handleSubmissionError(
              response.message || 'INVESTMENT_FUNDS.RESOLUTIONS.UPDATE_ERROR'
            );
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          console.error('Error updating resolution:', error);
          // this.handleSubmissionError(
          //   'INVESTMENT_FUNDS.RESOLUTIONS.UPDATE_ERROR'
          // );
        },
      });
  }

  private submitReferralResolution(): void {
    const command = this.buildCreateResolutionCommand(
      false,
      ResolutionStatusEnum._8,
      true
    );

    this.resolutionsProxy
      .addResolution(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isSubmitting = false;
          if (response.successed) {
            this.handleReferralResolutionSuccess();
          } else {
            this.handleSubmissionError(
              response.message || 'INVESTMENT_FUNDS.RESOLUTIONS.REFERRAL_ERROR'
            );
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          console.error('Error creating referral resolution:', error);
          // this.handleSubmissionError(
          //   'INVESTMENT_FUNDS.RESOLUTIONS.REFERRAL_ERROR'
          // );
        },
      });
  }

  onCancel(): void {
         this.dialog.closeAll();
            const dialogRef = this.dialog.open(EditResolutionPopupComponent, {
              width: '500px',
              data: this.fundId,
  });
   dialogRef.afterClosed().subscribe(result => {

      if (result) {
        // Refresh the members list if a member was successfully added
        this.navigateAfterAction();
      }
    });

  }

  // Getter methods for template
  get canSaveAsDraft(): boolean {
    // Only draft , pending , compeltingData resolutions can be saved as draft (JDWA-509)
    return (
      this.currentResolution?.canSaveAsDraft
    );
  }

  get canSend(): boolean {
    // All editable statuses can be sent/submitted (JDWA-509, JDWA-567)
    return [
      ResolutionStatusEnum._1, // Draft
      ResolutionStatusEnum._2, // Pending
      ResolutionStatusEnum._3, // Completing Data
      ResolutionStatusEnum._4, // Waiting for confirmation
      ResolutionStatusEnum._5, // Confirmed
      ResolutionStatusEnum._6, // Rejected
      ResolutionStatusEnum._7, // Voting in Progress
      ResolutionStatusEnum._8, // Approved  (creates referral)
      ResolutionStatusEnum._9, // Not approved (creates referral)
    ].includes(this.currentResolution?.status);
  }

  get isReferralCreation(): boolean {
    // Check if this edit will create a referral resolution
    return [
      ResolutionStatusEnum._8, // Approved
      ResolutionStatusEnum._9, // Not approved
    ].includes(this.currentResolution?.status);
  }

  get showVotingSuspensionWarning(): boolean {
    // Show warning for voting in progress status
    return this.currentResolution?.status === ResolutionStatusEnum._7;
  }

  // Resolution Items Management Methods
  addResolutionItem(): void {
    const dialogData: ResolutionItemDialogData = {
      fundId: this.fundId,
      resolutionId: this.resolutionId,
      existingItems: this.resolutionItems,
      isEdit: false,
    };

    const dialogRef = this.dialog.open(ResolutionItemDialogComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.resolutionItems.push(result);
        this.reorderItems();
      }
    });
  }

  editResolutionItem(item: ResolutionItemDto, index: number): void {
    const dialogData: ResolutionItemDialogData = {
      fundId: this.fundId,
      resolutionId: this.resolutionId,
      item: item,
      existingItems: this.resolutionItems,
      isEdit: true,
    };

    const dialogRef = this.dialog.open(ResolutionItemDialogComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.resolutionItems[index] = result;
      }
    });
  }

  deleteResolutionItem(item: ResolutionItemDto, index: number): void {
     Swal.fire({
          title: this.translateService.instant('COMMON.CONFIRM_DELETE'),
          text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.DELETE_ITEM_CONFIRM'),
          imageUrl: 'assets/images/confirmation-green.svg',
          showCancelButton: true,
          customClass: {
            confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
            cancelButton: 'btn outline-btn',
          },
          confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.CONFIRM'),
          cancelButtonText: `<img src="assets/images/back-icon.png" alt="back" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.BACK')
        }).then((result) => {
          if (result.isConfirmed) {
             this.resolutionItems.splice(index, 1);
            this.reorderItems();
          }
        });
  }

  viewConflictMembers(item: ResolutionItemDto): void {
    if (!item.conflictMembers || item.conflictMembers.length === 0) {
      return;
    }

    const dialogData: ConflictMembersDialogData = {
      itemTitle: item.title || 'Resolution Item',
      conflictMembers: item.conflictMembers,
    };

    const dialogRef = this.dialog.open(ConflictMembersDialogComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
    });

    dialogRef.afterClosed().subscribe(() => {
      // Dialog closed, no action needed
    });
  }

  private reorderItems(): void {
    // Update display order for all items
    this.resolutionItems.forEach((item, index) => {
      item.displayOrder = index + 1;
      // Update title to reflect new order
      item.title =
        this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.ITEM') +
        ' ' +
        (index + 1);
    });
  }

  // API Integration Methods
  private buildEditResolutionCommand(
    saveAsDraft: boolean,
    targetStatus: ResolutionStatusEnum,
    isReferral: boolean = false
  ): EditResolutionCommand {
    const formValue = this.formGroup.value;

    // Prepare attachment IDs - ensure proper cleanup
    const attachmentIds = Array.isArray(formValue.attachmentIds)
      ? formValue.attachmentIds.filter((id: any) => id && id !== 0)
      : [];

    const command = new EditResolutionCommand({
      id: this.resolutionId,
      code: this.currentResolution?.code,
      resolutionDate: this.parseFormDate(formValue.resolutionDate),
      description: formValue.description || undefined,
      resolutionTypeId: formValue.typeId,
      newType: formValue.customTypeName,
      attachmentId: formValue.attachmentId || 0,
      votingType: formValue.votingType || VotingType._1,
      memberVotingResult: formValue.memberVotingResult || MemberVotingResult._1,
      status: targetStatus,
      fundId: this.fundId,
      saveAsDraft: saveAsDraft,
      resolutionItems: this.resolutionItems.length > 0 ? this.resolutionItems : undefined,
      attachmentIds: attachmentIds.length > 0 ? attachmentIds : undefined,
      parentResolutionId: this.currentResolution.parentResolutionId != null ? this.currentResolution.parentResolutionId : isReferral ? this.currentResolution?.id : undefined,
      oldResolutionCode: formValue.oldResolutionCode,
    });

    return command;
  }
  private buildCreateResolutionCommand(
    saveAsDraft: boolean,
    targetStatus: ResolutionStatusEnum,
    isReferral: boolean = false
  ): AddResolutionCommand {
    const formValue = this.formGroup.value;
    // Prepare attachment IDs - ensure proper cleanup
    const attachmentIds = Array.isArray(formValue.attachmentIds) ? formValue.attachmentIds.filter((id: any) => id && id !== 0) : [];
    const command = new AddResolutionCommand({
      id: this.resolutionId,
      code: this.currentResolution?.code,
      resolutionDate: this.parseFormDate(formValue.resolutionDate),
      description: formValue.description || undefined,
      resolutionTypeId: formValue.typeId,
      newType: formValue.customTypeName,
      attachmentId: formValue.attachmentId || 0,
      votingType: formValue.votingType || VotingType._1,
      memberVotingResult: formValue.memberVotingResult || MemberVotingResult._1,
      status: targetStatus,
      fundId: this.fundId,
      saveAsDraft: saveAsDraft,
      parentResolutionId: isReferral ? this.currentResolution?.id : undefined,
      oldResolutionCode: formValue.oldResolutionCode,
      originalResolutionId: this.currentResolution.id,
      resolutionItems: this.resolutionItems.length > 0 ? this.resolutionItems : undefined,
      attachmentIds: attachmentIds.length > 0 ? attachmentIds : undefined,
    });

    return command;
  }
  private parseFormDate(dateString: string): any {
    if (!dateString) return undefined;

    // Convert YYYY-MM-DD format to DateTime
    try {
      const date = new Date(dateString);
      return date.toISOString();
    } catch (error) {
      console.error('Error parsing date:', error);
      return undefined;
    }
  }

  private handleSubmissionSuccess(
    saveAsDraft: boolean,
    targetStatus: ResolutionStatusEnum
  ): void {
    let messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.UPDATE_SUCCESS';

    if (saveAsDraft) {
      messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.SUCCESS_SAVED';
    } else {
      switch (targetStatus) {
        case ResolutionStatusEnum._2: // Pending
          messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.SUCCESS_SAVED';
          break;
        case ResolutionStatusEnum._4: // Waiting for confirmation
          messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.SUCCESS_SAVED';
          break;
        default:
          messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.SUCCESS_SAVED';
          break;
      }
    }

    this.errorModalService.showSuccess(this.translateService.instant(messageKey));
      //  this.navigateToResolutionsList();
      this.navigateAfterAction();

  }

  private handleReferralResolutionSuccess(): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.SUCCESS'),
      text: this.translateService.instant(
        'INVESTMENT_FUNDS.RESOLUTIONS.REFERRAL_CREATED'
      ),
      imageUrl: 'assets/images/confirmation-green.svg',
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
      },
      confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.CONFIRM'),
    }).then(() => {
      // this.navigateToResolutionsList();
      this.navigateAfterAction();

    });
  }

  private handleSubmissionError(message: string): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.ERROR'),
      text: this.translateService.instant(message),
      imageUrl: 'assets/images/warning-yellow.svg',
      customClass: {
        confirmButton: 'btn btn-primary ' + (this.translateService.currentLang == 'en' ? 'ms-3' : 'me-3'),
      },
      confirmButtonText:`<img src="assets/images/approve-white.png" alt="verify" class="${this.translateService.currentLang == 'ar' ? 'ms-2' : 'me-2'}"/>` + this.translateService.instant('RESOLUTIONS.CONFIRM'),
    });
  }

  private navigateToResolutionsList(): void {
    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.fundId },
    });
  }
  onReferralCodeClick(): void {
  if (this.currentResolution?.parentResolutionId && this.fundId) {
    this.router.navigate(['admin/investment-funds/resolutions/details/'+this.currentResolution?.parentResolutionId ], {
      queryParams: {
        fundId: this.fundId
      }
    });
  }
}
  /**
   * Navigate to appropriate page based on navigation source and clean up localStorage
   */
  private navigateAfterAction(): void {

    if (this.fromInbox) {
      // Navigate back to resolution inbox
      console.log('Navigating back to resolution inbox');
      this.router.navigate(['/admin/investment-funds/resolutions/inbox']);
    } else {
      // Navigate to regular resolutions list
      console.log('Navigating to resolutions list with fundId:', this.fundId);
      this.router.navigate(['/admin/investment-funds/resolutions'], {
        queryParams: { fundId: this.fundId },
      });
    }
  }

}
