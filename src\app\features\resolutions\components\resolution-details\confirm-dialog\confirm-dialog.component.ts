import { Component } from '@angular/core';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";
import { CommonModule } from '@angular/common';
import { MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { TranslateModule } from '@ngx-translate/core';
@Component({
  selector: 'app-confirm-dialog',
  standalone: true,
  imports: [CommonModule,
    TranslateModule, CustomButtonComponent],
  templateUrl: './confirm-dialog.component.html',
  styleUrl: './confirm-dialog.component.scss'
})
export class ConfirmDialogComponent {
  IconEnum = IconEnum;
   buttonEnum = ButtonTypeEnum;
  constructor( public dialogRef: MatDialogRef<ConfirmDialogComponent>,public router:Router) {

  }
  onSubmit(){
    this.dialogRef.close(true);

  }

  onClose(): void {
    this.dialogRef.close(false);
  }
}
