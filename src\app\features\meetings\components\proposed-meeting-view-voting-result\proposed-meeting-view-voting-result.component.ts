import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";
import {
  MeetingsProposalServiceProxy,
  MeetingsProposalDetailsDto,
  MeetingProposalDatesResultDto
} from '@core/api/api.generated';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { GeorgianDatePipe } from '@shared/pipes/georgian-date/georgian-date.pipe';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';
import { MeetingStatusResult } from '@shared/enum/meeting-enums';
@Component({
  selector: 'app-proposed-meeting-view-voting-result',
  standalone: true,
  imports: [BreadcrumbComponent, TranslateModule, CommonModule, CustomButtonComponent, MatProgressSpinnerModule, GeorgianDatePipe, DateHijriConverterPipe],
  templateUrl: './proposed-meeting-view-voting-result.component.html',
  styleUrl: './proposed-meeting-view-voting-result.component.scss'
})
export class ProposedMeetingViewVotingResultComponent implements OnInit, OnDestroy {
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  // Data properties
  proposalData: MeetingsProposalDetailsDto | null = null;
  meetingProposalVotesResult: MeetingProposalDatesResultDto[] = [];

  // State management
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Route parameters
  proposalId = 0;
  fundId = 0;

  // Lifecycle management
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private meetingsProposalServiceProxy: MeetingsProposalServiceProxy,
    private translateService: TranslateService,
    private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((queryParams) => {
      this.proposalId = +queryParams['proposalId'] || 0;
      this.fundId = +queryParams['fundId'] || 0;

      if (this.proposalId > 0) {
        this.loadProposalDetails();
      } else {
        this.hasError = true;
        this.errorMessage = 'INVESTMENT_FUNDS.MEETING.INVALID_PROPOSAL_ID';
      }
      this.initializeBreadcrumbs();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onBreadcrumbClicked(event: IBreadcrumbItem): void {
    if (!event.disabled && event.url) {
      this.router.navigateByUrl(event.url);
    }
  }

  private initializeBreadcrumbs(): void {

    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      { label: 'INVESTMENT_FUNDS.MEETING.MEETINGSTITLE', url:  `/admin/investment-funds/meetings?fundId=${this.fundId} ` },
      { label: 'INVESTMENT_FUNDS.MEETING.VOTING_RESULT_ON_PROPOSED_DATES', url: '', disabled: true },

   ];
   this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
 }

  /**
   * Load proposal details from API
   */
  loadProposalDetails(): void {
    this.isLoading = true;
    this.hasError = false;

    this.meetingsProposalServiceProxy.getProposalsDetails(this.proposalId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.proposalData = response.data;
            this.meetingProposalVotesResult = response.data.meetingProposalVotesResult || [];
            debugger
          } else {
            this.hasError = true;
            this.errorMessage = response.message || 'INVESTMENT_FUNDS.MEETING.LOAD_ERROR';
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.hasError = true;
        }
      });
  }

  /**
   * Get CSS class for vote status based on isWinner flag
   */
  getStatusVoteClass(isWinner: boolean): string {
    return isWinner ? 'status-green' : '';
  }



  formatTime(time: string): string {
    if (!time) return '';

    try {
      // Parse time (assuming format like "10:00:00" or "10:00")
      const timeParts = time.split(':');
      let hours = parseInt(timeParts[0]);
      const minutes = timeParts[1] || '00';

      // Get localized AM/PM strings
      const localizedTime = this.getLocalizedAmPm();

      // Determine AM/PM
      let period = '';
      if (hours === 0) {
        hours = 12;
        period = localizedTime.am;
      } else if (hours < 12) {
        period = localizedTime.am;
      } else if (hours === 12) {
        period = localizedTime.pm;
      } else {
        hours = hours - 12;
        period = localizedTime.pm;
      }

      return `${hours}:${minutes} ${period}`;
    } catch (error) {
      return time;
    }
  }

  private getLocalizedAmPm(): { am: string; pm: string } {
    const currentLang = this.translateService.currentLang || 'ar';

    if (currentLang === 'ar') {
      return { am: 'صباحاً', pm: 'مساءً' };
    } else {
      return { am: 'AM', pm: 'PM' };
    }
  }
  goBack(){
    this.router.navigate(['/admin/investment-funds/meetings'], {
      queryParams: { fundId: this.fundId }
    });
  }

  getStatusClass(statusId: number): string {
     switch (statusId) {
       case MeetingStatusResult.UnderVoting: // UnderVoting
         return 'under-vote';
       case MeetingStatusResult.Completed: // Completed
         return 'complete';
       default:
         return '';
     }
   }
}

