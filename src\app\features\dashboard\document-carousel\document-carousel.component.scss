// Document Carousel Component Styles - Pixel Perfect Design
// Based on reference mockup with exact measurements and colors

.document-carousel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  // Loading State
  .carousel-loading {
    .spinner-border {
      width: 2rem;
      height: 2rem;
      color: #007bff;
    }
  }

  // Empty State
  .carousel-empty {
    i {
      opacity: 0.5;
      color: #6c757d;
    }

    h6, p {
      margin: 0;
      color: #6c757d;
    }
  }

  // Pixel Perfect Document Carousel Wrapper
  .document-carousel-wrapper {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;

    // Header Section
    .carousel-header {
      margin-bottom: 24px;

      .carousel-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        line-height: 1.2;
      }

      .document-count {
        font-size: 14px;
        color: #6c757d;
        margin: 0;
        line-height: 1.4;
      }
    }

    // Document Carousel Container
    .document-carousel-container {
      position: relative;
      width: 100%;

      // Navigation Buttons
      .carousel-nav-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        border: none;
        background: transparent;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: none;
        z-index: 10;

        &:hover:not(:disabled) {
          background: #ffffff;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-50%) scale(1.05);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          background: rgba(255, 255, 255, 0.6);
        }

        i {
          font-size: 16px;
          color: #007bff;
        }

        &.carousel-nav-prev {
          left: -20px;
        }

        &.carousel-nav-next {
          right: -20px;
        }
      }

      // Document Card Container
      .document-card-container {
      //  padding:23px 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        max-height: 230px;

        .document-card {
          width: 280px;
          max-height: 150px;
        //  background: #ffffff;
      //    border: 1px solid #e9ecef;
          border-radius: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          padding:0 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          //box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

          // &:hover {
          //   border-color: #007bff;
          //   box-shadow: 0 4px 16px rgba(0, 123, 255, 0.15);
          //   transform: translateY(-2px);
          // }

          // Document Icon Container
          .document-icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 16px;

            .document-icon-wrapper {
              position: relative;
              display: flex;
              flex-direction: column;
              align-items: center;

              .document-icon {
                font-size: 48px;
                color: #007bff;
                margin-bottom: 8px;
              }

              .document-type-label {
                font-size: 12px;
                font-weight: 600;
                color: #ffffff;
                background: #007bff;
                padding: 2px 8px;
                border-radius: 4px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }
            }
          }

          // Document Content
          .document-content {
            text-align: center;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-bottom: 16px;

            .document-title {
              font-size: 16px;
              font-weight: 400;
              color: #05004E;
              margin-bottom: 8px;
              line-height: 22px;
              max-width: 250px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              cursor: pointer;
            }

            .document-subtitle {
              font-size: 14px;
              color: #6c757d;
              margin: 0;
              line-height: 1.4;
            }
          }

          // Document Action
          .document-action {
            width: 100%;

            .view-btn {
              width: 100%;
              height: 36px;
              background: #007bff;
              color: #ffffff;
              border: none;
              border-radius: 6px;
              font-size: 14px;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover:not(:disabled) {
                background: #0056b3;
                transform: translateY(-1px);
              }

              &:active:not(:disabled) {
                transform: translateY(0);
              }

              &:disabled {
                background: #6c757d;
                color: #ffffff;
                cursor: not-allowed;
                opacity: 0.6;

                &:hover {
                  transform: none;
                }
              }
            }
            .view2-btn {
                padding: 7px 11.2px 11px !important;
              //  height: 34px;
                font-size: 10px;
                font-weight: 400;
                line-height: 2px;
              }
          }
        }
      }
    }
  }

  // Carousel Container
  .carousel-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    // Carousel Header
    .carousel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 12px;
      padding: 0 8px;
      margin-bottom: 16px;

      .carousel-title {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        flex: 1;
        min-width: 150px;
      }

      .carousel-controls {
        display: flex;
        align-items: center;
        gap: 16px;

        .document-count {
          font-size: 0.85rem;
          color: #6c757d;
          white-space: nowrap;
        }

        .navigation-buttons {
          display: flex;
          gap: 4px;

          .nav-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #dee2e6;
            background: #ffffff;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #6c757d;

            &:hover:not(:disabled) {
              background: #f8f9fa;
              border-color: #adb5bd;
              color: #495057;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            i {
              font-size: 0.8rem;
            }
          }
        }
      }
    }

    // Bootstrap Carousel Wrapper
    .carousel {
      margin-bottom: 16px;

      // Carousel Indicators
      .carousel-indicators {
        margin-bottom: 0;

        [data-bs-target] {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: none;
          background-color: rgba(0, 0, 0, 0.3);

          &.active {
            background-color: #007bff;
          }
        }
      }

      // Carousel Controls
      .carousel-control-prev,
      .carousel-control-next {
        width: 5%;
        color: #007bff;

        &:hover {
          color: #0056b3;
        }

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          background-color: rgba(0, 123, 255, 0.8);
          border-radius: 50%;
          width: 2rem;
          height: 2rem;
        }
      }

      // Document Item (Bootstrap Card)
      .document-item {
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;

        &:hover {
          border-color: #007bff;
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
          transform: translateY(-2px);

          .hover-overlay {
            opacity: 1;
          }

          .document-actions {
            .btn {
              opacity: 1;
            }
          }
        }

        // Document Icon
        .document-icon {
          position: relative;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 3rem;
            transition: transform 0.2s ease;
          }

          .file-type-badge {
            position: absolute;
            bottom: 0;
            right: 0;
            background: #6c757d;
            color: white;
            font-size: 0.6rem;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 3px;
            text-transform: uppercase;
          }
        }

        // Document Info
        .document-info {
          .document-name {
            font-size: 0.95rem;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 0.5rem;
          }

          .document-meta {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .file-size,
            .upload-date {
              display: flex;
              align-items: center;
              font-size: 0.8rem;
              color: #6c757d;
            }
          }
        }

        // Action Buttons
        .document-actions {
          .btn {
            opacity: 0.8;
            transition: all 0.2s ease;
            font-size: 0.8rem;

            &:hover {
              opacity: 1;
              transform: translateY(-1px);
            }
          }
        }

        // Hover Overlay
        .hover-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 123, 255, 0.9);
          border-radius: 0.375rem;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          color: white;

          .overlay-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            text-align: center;

            i {
              font-size: 1.5rem;
            }

            span {
              font-size: 0.85rem;
              font-weight: 500;
            }
          }
        }
      }
    }

    // Carousel Indicators
    .carousel-indicators {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;

      .indicators-track {
        display: flex;
        gap: 6px;

        .indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #dee2e6;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #adb5bd;
          }

          &.active {
            background: #007bff;
            transform: scale(1.2);
          }
        }
      }
    }

    // Document Summary
    .document-summary {
      padding-top: 16px;
      border-top: 1px solid #e9ecef;

      .stat-item {
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
          background: #e9ecef;
          transform: translateY(-1px);
        }

        i {
          font-size: 1.5rem;
        }

        .stat-value {
          font-size: 1.2rem;
          font-weight: 700;
          color: #2c3e50;
          margin-bottom: 0;
        }

        .stat-label {
          font-size: 0.8rem;
          color: #6c757d;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .document-carousel {
    .carousel-container {
      .carousel-header {
        flex-direction: column;
        align-items: flex-start;

        .carousel-controls {
          align-self: stretch;
          justify-content: space-between;
        }
      }

      .carousel {
        .carousel-control-prev,
        .carousel-control-next {
          width: 8%;

          .carousel-control-prev-icon,
          .carousel-control-next-icon {
            width: 1.5rem;
            height: 1.5rem;
          }
        }
      }

      .document-item {
        .document-icon {
          height: 60px;

          i {
            font-size: 2.5rem;
          }
        }

        .document-info {
          .document-name {
            font-size: 0.9rem;
          }

          .document-meta {
            .file-size,
            .upload-date {
              font-size: 0.75rem;
            }
          }
        }

        .document-actions {
          .btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
          }
        }
      }

      .document-summary {
        .stat-item {
          padding: 8px;

          .stat-value {
            font-size: 1rem;
          }

          .stat-label {
            font-size: 0.75rem;
          }
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .document-carousel {
    .carousel-container {
      .carousel-header {
        .carousel-controls {
          flex-direction: row-reverse;
        }
      }

      .carousel {
        .carousel-control-prev {
          left: auto;
          right: 0;
        }

        .carousel-control-next {
          right: auto;
          left: 0;
        }
      }

      .document-item {
        text-align: right;

        .document-info {
          .document-name {
            text-align: right;
          }

          .document-meta {
            .file-size,
            .upload-date {
              flex-direction: row-reverse;
            }
          }
        }

        .document-actions {
          .btn-group {
            flex-direction: row-reverse;
          }
        }
      }

      .document-summary {
        .stat-item {
          flex-direction: row-reverse;
        }
      }
    }
  }
}

// Responsive Design for Pixel Perfect Carousel

@media (max-width: 1024px) {
  .document-carousel {
    .document-carousel-wrapper {
      max-width: 280px;
    }
  }
}

@media (max-width: 768px) {
  .document-carousel {
    .document-carousel-wrapper {
      max-width: 300px;

      .carousel-header {
        margin-bottom: 20px;

        .carousel-title {
          font-size: 16px;
        }

        .document-count {
          font-size: 13px;
        }
      }

      .document-carousel-container {
        .carousel-nav-btn {
          width: 36px;
          height: 36px;

          &.carousel-nav-prev {
            left: -18px;
          }

          &.carousel-nav-next {
            right: -18px;
          }

          i {
            font-size: 14px;
          }
        }

        .document-card-container {
        max-height: 172px;

          .document-card {
            width: 260px;
            height: 220px;
            padding: 20px 16px;

            .document-icon-container {
              margin-bottom: 14px;

              .document-icon-wrapper {
                .document-icon {
                  font-size: 42px;
                }

                .document-type-label {
                  font-size: 11px;
                  padding: 2px 6px;
                }
              }
            }

            .document-content {
              margin-bottom: 14px;

              .document-title {
                font-size: 15px;
              }

              .document-subtitle {
                font-size: 13px;
              }
            }

            .document-action {
              .view2-btn {
                padding: 0px 10.2px 4px !important;
                height: 34px;
                font-size: 10px;
                font-weight: 400;
                line-height: 2px;
              }
            }
          }
        }
      }
    }
  }
}


@media (max-width: 420px) {
  .document-carousel {
    .document-carousel-wrapper {
      max-width: 280px;
    }
  }
}


// RTL Support for Arabic Layout
[dir="rtl"] {
  .document-carousel {
    .document-carousel-wrapper {
      .document-carousel-container {
        .carousel-nav-btn {
          &.carousel-nav-prev {
            left: auto;
            right: -20px;

            i {
              transform: scaleX(-1);
            }
          }

          &.carousel-nav-next {
            right: auto;
            left: -20px;

            i {
              transform: scaleX(-1);
            }
          }
        }

        .document-card-container {
          .document-card {
            .document-content {
              .document-title,
              .document-subtitle {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
}

// Print Styles
@media print {
  .document-carousel {
    .document-carousel-wrapper {
      .document-carousel-container {
        .carousel-nav-btn {
          display: none !important;
        }

        .document-card-container {
          .document-card {
            box-shadow: none;
            border: 1px solid #dee2e6;

            &:hover {
              transform: none;
              box-shadow: none;
            }
          }
        }
      }
    }
  }
}
