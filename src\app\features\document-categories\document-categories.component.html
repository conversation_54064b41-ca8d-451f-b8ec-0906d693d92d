<div class="document-categories">
  <!-- <app-breadcrumb  [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium" divider=">"></app-breadcrumb> -->
    <app-page-header
      [title]="'Document_Categories.Document_Categories' | translate"
      [showCreateButton]="tokenService.hasPermission('DocumentCategory.Create')"
      createButtonText="Document_Categories.ADD_Document_Categories"
      (create)="onCreateNewCategory()" [showSearch]="false" [showFilter]="false"></app-page-header>

  <div class="table-container mb-5 mt-3"  *ngIf="tableDataSource">

    <app-table [columns]="tableColumns"
      [displayedColumns]="displayedColumns"
      [dataSource]="tableDataSource"
      (onClickAction)="onClickAction($event)"
      (switchToggleEvent)="onSwitchToggle($event)"
      (textLinkClick)="onTextLinkClick($event)"
      (toggleAllRows)="toggleAllRows()"
      (toggleRow)="toggleRow($event)"
      [sortingType]="sortingType"
      (sortChanged)="onSortChanged($event)"
      [paginationType]=""
      [showPaginator]="false"
      (pageChange)="onPageChange($event)"
      [totalItems]="totalCount"></app-table>
  </div>
