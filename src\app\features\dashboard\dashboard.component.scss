// new style

.dashboard-page {
	.cards-container {

		margin-bottom: 24px;

		.vote-card {
			border-radius: 16px;
			border: 1px solid #D7D7D7;
			background: #F0F2F5;
			padding: 16px 43px;

			@media(max-width:1024px) {
				padding: 9.7px 12px;
			}

			@media(max-width:768px) {
				margin-bottom: 10px;
			}

			.title {
				color: #000;
				font-size: 14px;
				font-weight: 400;
				line-height: 20px;
				text-align: center;
				margin-bottom: 8px;
			}

			.sub-title {
				color: #1C1C1C;
				font-size: 32px;
				font-weight: 600;
				line-height: 36px;
				text-align: center;
				margin-bottom: 0;

			}
		}

		.fund-activ-card {
			border-radius: 16px;
			background: rgba(206, 226, 253, 0.60);
			padding: 16px 43px;

			@media(max-width:1024px) {
				padding: 10.5px 12px;
			}

			@media(max-width:768px) {
				margin-bottom: 10px;
			}

			.title {
				color: #000;
				font-size: 14px;
				font-weight: 400;
				line-height: 20px;
				text-align: center;
				margin-bottom: 8px;

			}

			.sub-title {
				color: #1C1C1C;
				font-size: 32px;
				font-weight: 600;
				line-height: 36px;
				text-align: center;
				margin-bottom: 0;


			}
		}

		.exit-card {
			border-radius: 16px;
			background: rgba(189, 189, 189, 0.60);
			padding: 16px 43px;

			@media(max-width:1024px) {
				padding: 20.5px 12px;
			}

			@media(max-width:768px) {
				margin-bottom: 10px;
			}

			.title {
				color: #000;
				font-size: 14px;
				font-weight: 400;
				line-height: 20px;
				text-align: center;
				margin-bottom: 8px;

			}

			.sub-title {
				color: #1C1C1C;
				font-size: 32px;
				font-weight: 600;
				line-height: 36px;
				text-align: center;
				margin-bottom: 0;


			}
		}

		.box-card {
			border-radius: 16px;
			background: rgba(197, 185, 151, 0.60);
			padding: 16px 43px;

			@media(max-width:1024px) {
				padding: 20.5px 12px;
			}

			@media(max-width:768px) {
				margin-bottom: 10px;
			}

			.title {
				color: #000;
				font-size: 14px;
				font-weight: 400;
				line-height: 20px;
				text-align: center;
				margin-bottom: 8px;

			}

			.sub-title {
				color: #1C1C1C;
				font-size: 32px;
				font-weight: 600;
				line-height: 36px;
				text-align: center;
				margin-bottom: 0;

			}
		}

	}

	.dropdown-container {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 7px;

		@media(max-width:425px) {
			flex-direction: column;
		}

		.box-text {
			color: #05004E;
			font-size: 16px;
			font-weight: 700;
			line-height: 22px;
			margin-bottom: 0;
		}
	}

	.resolution-container {
		//    height: 475px;
		background: #FFFFFF;
		border-radius: 16px;
		border: 1px solid #EAEEF1;
		margin-bottom: 24px;

		.resolution-container-top {
			background: #F8FAFC;
			padding: 16px 16px 9px;
			border-radius: 16px 16px 0 0;

			.resolution-text {
				color: #00205A;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				margin-bottom: 2px;
			}

			.no-resolution-text {
				color: #4B5662;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				margin-bottom: 0;

			}

			.see-all {
        cursor: pointer;
				color: #00205A;
				font-size: 12px;
				font-weight: 400;
				line-height: 16px;
				text-decoration-line: underline;
				text-decoration-style: solid;
				text-decoration-skip-ink: none;
				text-decoration-thickness: auto;
				text-underline-offset: auto;
			}
		}

		.resolution-chart-container {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 12px 0 35px;
			 height: 409px;

       	@media (max-width:768px) {
			 height: 429px;
			}

			@media (max-width:1024px) {
				flex-direction: column;
				justify-content: center;
			}




			.resolution-data {
				@media (max-width:1024px) {

					display: flex;
					flex-wrap: wrap;
				}

				.circle-container {

					//	margin-bottom: 16px;

					display: flex;
					align-items: center;
					margin-bottom: 16px;

					.circle {
						width: 12px;
						height: 12px;
						border-radius: 50%;
					}

					.status-text {
						color: #000;
						font-size: 14px;
						font-weight: 400;
						line-height: 14px;
					}
				}
			}
		}
     i{
      opacity: 0.5;
    }

    	.resolution-chart-container-board {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 12px 0 35px;
			 height: 194px;

			@media (max-width:1024px) {
        height:192px !important;
			}


			.resolution-data {
				@media (max-width:1024px) {

					display: flex;
					flex-wrap: wrap;
				}

				.circle-container {

					//	margin-bottom: 16px;

					display: flex;
					align-items: center;
					margin-bottom: 16px;

					.circle {
						width: 12px;
						height: 12px;
						border-radius: 50%;
					}

					.status-text {
						color: #000;
						font-size: 14px;
						font-weight: 400;
						line-height: 14px;

            @media (max-width:1024px) {
		      	font-size: 12px;
			}
					}
				}
			}
		}
	}

	.document-container {
		background: #FFFFFF;
		border-radius: 16px;
		border: 1px solid #EAEEF1;
		margin-bottom: 24px;

		.document-container-top {
			background: #F8FAFC;
			padding: 16px 16px 9px;
			border-radius: 16px 16px 0 0;

			.document-text {
				color: #00205A;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				margin-bottom: 2px;
			}

			.no-document-text {
				color: #4B5662;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				margin-bottom: 0;

			}

			.see-all {
        cursor: pointer;
				color: #00205A;
				font-size: 12px;
				font-weight: 400;
				line-height: 16px;
				text-decoration-line: underline;
				text-decoration-style: solid;
				text-decoration-skip-ink: none;
				text-decoration-thickness: auto;
				text-underline-offset: auto;
			}
		}

		.document-chart-container {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 20px 0 35px;

			.document-data {
				.circle-container {

					margin-bottom: 16px;

					.circle {
						width: 12px;
						height: 12px;
						border-radius: 50%;
					}

					.status-text {
						color: #000;
						font-size: 14px;
						font-weight: 400;
						line-height: 14px;
                 @media (max-width:1024px) {
		      	font-size: 12px;
			}
					}
				}
			}
		}
	}

	.voting-container {
		background: #FFFFFF;
		border-radius: 16px;
		border: 1px solid #EAEEF1;
		margin-bottom: 24px;

		.voting-container-top {
			background: #F8FAFC;
			padding: 16px 16px 0;
			border-radius: 16px 16px 0 0;


			.voting-text {
				color: #00205A;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				margin-bottom: 2px;
			}

			.no-voting-text {
				color: #4B5662;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				margin-bottom: 0;

			}


			.see-all {
        cursor: pointer;
				color: #00205A;
				font-size: 12px;
				font-weight: 400;
				line-height: 16px;
				text-decoration-line: underline;
				text-decoration-style: solid;
				text-decoration-skip-ink: none;
				text-decoration-thickness: auto;
				text-underline-offset: auto;
			}
		}

		.voting-chart-container {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 12px 0 35px;

    @media (max-width: 420px) {
    padding: 0 12px 0 0;
  }

			.voting-data {
				.circle-container {

					margin-bottom: 16px;

					.circle {
						width: 12px;
						height: 12px;
						border-radius: 50%;
					}

					.status-text {
						color: #000;
						font-size: 14px;
						font-weight: 400;
						line-height: 14px;
                 @media (max-width:1024px) {
		      	font-size: 12px;
			}
					}
				}
			}
		}

     i{
      opacity: 0.5;
    }
	}

	.assessment-container {
		background: #FFFFFF;
		border-radius: 16px;
		border: 1px solid #EAEEF1;
		margin-bottom: 24px;

		.assessment-container-top {
			background: #F8FAFC;
			padding: 16px 16px 9px;
			border-radius: 16px 16px 0 0;


			.assessment-text {
				color: #00205A;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				margin-bottom: 2px;
			}

			.no-assessment-text {
				color: #4B5662;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				margin-bottom: 0;

			}


			.see-all {
        cursor: pointer;
				color: #00205A;
				font-size: 12px;
				font-weight: 400;
				line-height: 16px;
				text-decoration-line: underline;
				text-decoration-style: solid;
				text-decoration-skip-ink: none;
				text-decoration-thickness: auto;
				text-underline-offset: auto;
			}
		}

		.assessment-chart-container {
			display: flex;
			justify-content: space-between;
			align-items: center;
		//	padding: 0 12px 0 35px;

			.assessment-data {
				.circle-container {
					margin-bottom: 16px;

					.circle {
						width: 12px;
						height: 12px;
						border-radius: 50%;
					}

					.status-text {
						color: #000;
						font-size: 14px;
						font-weight: 400;
						line-height: 14px;
                 @media (max-width:1024px) {
		      	font-size: 12px;
			}
					}
				}

				.circle-container:last-child {
					margin-bottom: 0;
				}
			}
		}

    i{
      opacity: 0.5;
    }
	}

	.meeting-container {
		background: #FFFFFF;
		border-radius: 16px;
		border: 1px solid #EAEEF1;
		margin-bottom: 24px;

		.meeting-container-top {
			background: #F8FAFC;
			padding: 16px 16px 9px;
			border-radius: 16px 16px 0 0;


			.meeting-text {
				color: #00205A;
				font-size: 16px;
				font-weight: 400;
				line-height: 22px;
				margin-bottom: 2px;
			}

			.no-meeting-text {
				color: #4B5662;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				margin-bottom: 0;

			}


			.see-all {
        cursor: pointer;
				color: #00205A;
				font-size: 12px;
				font-weight: 400;
				line-height: 16px;
				text-decoration-line: underline;
				text-decoration-style: solid;
				text-decoration-skip-ink: none;
				text-decoration-thickness: auto;
				text-underline-offset: auto;
			}
		}

		.meeting-chart-container {

			padding: 6px 20px 6px 35px;

			.meeting-data {
				.circle-container {

					margin-bottom: 16px;

					.circle {
						width: 12px;
						height: 12px;
						border-radius: 50%;
					}

					.status-text {
						color: #000;
						font-size: 14px;
						font-weight: 400;
						line-height: 14px;
                 @media (max-width:1024px) {
		      	font-size: 12px;
			}
					}
				}
			}
		}
	}


}

::ng-deep .ng-select.ng-select-single .ng-select-container {
	height: 20px !important;
}


::ng-deep .ng-select .ng-select-container {
	min-height: 21px !important;
}

