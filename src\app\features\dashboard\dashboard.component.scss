.page-header-section {
	margin-bottom: 30px;
}

.absolute-image {
	position: absolute;
	bottom: 7px;
	left: 0px;
	width: 163px;
	height: 160px;
	object-fit: fill;
}
.box ,.box2{
	width: 8px;
	height: 20px;
}

.circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

// .box3 {
// 	width: 100%;
// 	// height: 8px;
// }
// .box4 {
// 	width: 8px;
// 	height: 40px;
// }
// .box5 {
// 	width: 8px;
// 	height: 24px;
// }
// .box6 {
// 	width: 8px;
// 	height: 8px;
// 	background: #03045E;
// 	border-radius: 20px;
// }
// .box7 {
// 	width: 8px;
// 	height: 8px;
// 	background: #B4A085;
// 	border-radius: 20px;
// }
// .box8 {
// 	width: 8px;
// 	height: 8px;
// 	background: #C50F1F;
// 	border-radius: 20px;
// }
// .box9 {
// 	width: 8px;
// 	height: 8px;
// 	border-radius: 20px;
// }
// .box10 {
// 	width: 8px;
// 	height: 8px;
// 	background: #105EA3;
// 	border-radius: 20px;
// }
// .box11 {
// 	width: 8px;
// 	height: 8px;
// 	background: #828282;
// 	border-radius: 20px;
// }
// .box12 {
// 	width: 8px;
// 	height: 8px;
// 	background: #606060;
// 	border-radius: 20px;
// }
// .box13 {
// 	width: 38px;
// 	align-self: stretch;
// }
// .box14 {
// 	width: 17px;
// 	align-self: stretch;
// }
// .box15 {
// 	width: 45px;
// 	align-self: stretch;
// }
// .box16 {
// 	width: 8px;
// 	height: 8px;
// 	background: #F2F2F2;
// 	border-radius: 21px;
// }
// .box17 {
// 	width: 8px;
// 	height: 8px;
// 	background: #878378;
// 	border-radius: 21px;
// }
// .box18 {
// 	width: 8px;
// 	height: 8px;
// 	background: #F2F2F2;
// 	border-radius: 21px;
// 	margin-top: 12px;
// }
// .box19 {
// 	width: 8px;
// 	height: 8px;
// 	background: #00205A;
// 	border-radius: 21px;
// 	// margin-top: 12px;
// }
// .box20 {
// 	width: 8px;
// 	height: 31px;
// 	margin-bottom: 1px;
// }
.button {
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #00205A;
	border-radius: 4px;
	border: none;
	padding: 6px 16px;
	text-align: left;
}
.column {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #F8FAFC;
	border-radius: 16px;
	border: 1px solid #EAEEF1;
	padding:  20px;
}
.column2 {
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #C4B89799;
	border-radius: 16px;
  padding:12.5px 130.5px 18.5px;
	gap: 8px;
  @media (max-width: 1440px) {
      padding:12.5px 100.5px 18.5px;

  }


  @media (max-width: 320px) {
      padding:12.5px 85px 18.5px;

  }
}

.col-size-6{
  @media (max-width: 320px) {
      padding:0;
  }
}
.column3 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #BDBDBD99;
	border-radius: 16px;
	padding: 11.5px 5px 20px;
	gap: 8px;
}
.column4 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #CDE2FC99;
	border-radius: 16px;
	padding: 11.5px 5px 20px;
	gap: 8px;
}
.column5 {
	flex: 1;
	display: flex;
	flex-direction: column;
	background: #F8FAFC;
	border-radius: 16px;
	border: 1px solid #EAEEF1;
	padding-top: 8px;
	padding-bottom: 8px;
	gap: 12px;
}
.column6 {
	align-self: stretch;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 10px 79px;
	margin-left: 6px;
	margin-right: 6px;
	gap: 14px;
}
.column7 {
	align-self: stretch;
	display: flex;
	flex-direction: column;
}
.column8 {
	align-self: stretch;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8px 22px;
	margin-bottom: 7px;
	gap: 4px;
}
.column9 {
	align-self: stretch;
	align-items: flex-start;
}
.column10 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #FFFFFF;
	border-radius: 16px;
	border: 1px solid #EAEEF1;
}
.column11 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	margin-right: 74px;
	gap: 4px;
}
.column12 {
	flex-shrink: 0;
	display: flex;
	align-items: center;
  justify-content: space-between;
  width: 100%;
    flex-direction: row-reverse;
    .vote-text{
      display: flex;
      flex-direction: column;
    }
        .see-all{
display: flex;
flex-direction: column;
height: 2rem;
    align-items: center;
    a{
      color: #00205A;
      font-size:  12px;
      font-weight: 400;
      line-height:  16px;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-decoration-skip-ink: none;
      text-decoration-thickness: auto;
      text-underline-offset: auto;

    }
    .dropdown-container{width:70%;
       height: 42px;
}
  }
}
.column13 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	padding-top: 50px;
	padding-bottom: 51px;
	padding-left: 16px;
	gap: 16px;
}
.column14 {
	flex: 1;
	background: #FFFFFF;
	border-radius: 16px;
	border: 1px solid #EAEEF1;

}
.column15 {
	align-self: stretch;
	background: #F8FAFC;
	padding-top: 9px;
	padding-bottom: 9px;
    border-radius:16px 16px 0 0;

}
.vote-container  {

  a{
      text-align: end;
     padding: 7px 20px 0 0;
     color: #00205A;
      font-size:  12px;
      font-weight: 400;
      line-height:  16px;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-decoration-skip-ink: none;
      text-decoration-thickness: auto;
      text-underline-offset: auto;

    }
  .dropdown-container2{
    width: 43%;

  @media (max-width:1440px) {
    width: 60%;

  }
  }


}
::ng-deep .ng-select.ng-select-focused .ng-select-container {
  border-color: transparent !important; /* remove blue border */
  box-shadow: none !important;          /* remove focus shadow */
}

//   ::ng-deep html[dir="rtl"] .first-sec,.see-all {
//   flex-direction: row-reverse;
// }
.column16 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 7px;
	padding-bottom: 7px;
	position: relative;
}
.column17 {
	flex: 1;
	display: flex;
	flex-direction: column;
//	align-items: end;
	padding-top: 16px;
	gap: 12px;
  padding: 16px;
}
.column18 {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	background: #F8FAFC;
	padding-top: 9px;
	padding-bottom: 9px;
}
.column19 {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	padding-top: 20px;
	padding-bottom: 20px;
	gap: 12px;
}
.column20 {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-left: 2px;
	padding-right: 2px;
	margin-left: 10px;
	margin-right: 10px;
}
.column21 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #FFFFFF;
	border-radius: 16px;
	border: 1px solid #EAEEF1;
	margin-right: 1px;
}
.column22 {
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #F8FAFC;
	padding: 9px 16px;
}
.column23 {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 12px;
	padding-bottom: 12px;
	gap: 7px;
}
.column24 {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.column25 {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 33px 19px;
	background-size: cover;
	background-position: center;
}
.column26 {
	align-self: stretch;
	display: flex;
	flex-direction: column;
	padding-top: 12px;
	padding-bottom: 12px;
	gap: 7px;
}
.column27 {
	align-self: stretch;
	display: flex;
	flex-direction: column;
	    margin: auto;
}
.column28 {
	align-self: stretch;
	display: flex;
	flex-direction: column;
	background-size: cover;
	background-position: center;
}
.image {
	width: 55px;
	height: 55px;
	object-fit: fill;
}
.image2 {
	width: 10px;
	height: 10px;
	object-fit: fill;
}
.image3 {
	width: 124px;
	height: 123px;
	margin: 14px 20px;
	object-fit: fill;
}
.image4 {
	width: 163px;
	height: 163px;
	object-fit: fill;
}
.image5 {
	width: 17px;
	height: 11px;
	object-fit: fill;
}
.image6 {
	width: 29px;
	height: 38px;
	margin-right: 45px;
	object-fit: fill;
}
.image7 {
	width: 10px;
	height: 10px;
	margin-right: 2px;
	object-fit: fill;
}
.image8 {
	width: 96px;
	height: 96px;
	object-fit: fill;
}
.image9 {
	height: 96px;
	align-self: stretch;
	object-fit: fill;
}
.row-view {
	align-self: stretch;
	display: flex;
	align-items: flex-start;
}
.row-view-assesment {
  align-self: stretch;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
.row-view2 {
	display: flex;
	align-items: center;
}
.row-view3 {
	align-self: stretch;
	display: flex;
	align-items: center;
	padding-left: 24px;
	padding-right: 24px;
	margin-left: 21px;
	margin-right: 21px;
}
.row-view4 {
	align-self: stretch;
	display: flex;
	align-items: center;
}
.row-view5 {
	align-self: stretch;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-left: 8px;
	padding-right: 8px;

  @media (max-width:768px) {
    flex-direction: column;
    align-items: flex-start;

  }

}

// ::ng-deep html[dir="ltr"] .row-view5 {
//   flex-direction: row-reverse;
// }
.row-view6 {
	flex-shrink: 0;
	display: flex;
	align-items: center;
	padding-right: 2px;
	gap: 5px;
}
.row-view7 {
	display: flex;
	align-items: flex-start;
	background: #F8FAFC;
	padding: 9px 16px;
}
.row-view8 {
	display: flex;
	align-items: center;
	padding-right: 3px;
	gap: 2px;
}
.row-view9 {
	display: flex;
	align-items: center;
	padding-left: 19px;
	padding-right: 19px;
  justify-content: space-between;
  width: 100%;
}
.row-view10 {
	display: flex;
	align-items: center;
	gap: 4px;
}
.row-view11 {
	display: flex;
	align-items: center;
	gap: 5px;
}
.row-view12 {
	align-self: stretch;
	display: flex;
	align-items: flex-start;
	margin-left: 37px;
	margin-right: 37px;
  justify-content: space-between;
}
.row-view13 {
	display: flex;
	align-items: center;
	margin-left: 70px;
	gap: 5px;
}
.row-view14 {
	display: flex;
	align-items: center;
	margin-left: 50px;
	gap: 6px;
}
.row-view15 {
	align-self: stretch;
	display: flex;
	align-items: center;
	margin-left: 13px;
	margin-right: 13px;
}
.row-view16 {
	display: flex;
	align-items: center;
	margin-right: 15px;
	gap: 6px;
}
.row-view17 {
	display: flex;
	align-items: center;
	margin-left: 83px;
	gap: 5px;
}
.row-view18 {
	display: flex;
	align-items: center;
	margin-left: 16px;
	margin-right: 16px;
}
.row-view19 {
	display: flex;
	align-items: center;
	margin-left: 35px;
	margin-right: 35px;
}
.row-view20 {
	display: flex;
	align-items: flex-start;
	padding-left: 48px;
	padding-right: 48px;
	gap: 8px;
}
.row-view21 {
	flex-shrink: 0;
	display: flex;
	align-items: center;
	padding-top: 1px;
	padding-bottom: 1px;
	gap: 8px;
}
.row-view22 {
	flex-shrink: 0;
	display: flex;
	align-items: center;
	gap: 8px;
}
.row-view23 {
	align-self: stretch;
//	display: flex;
//	justify-content: space-between;
//s	align-items: center;
	margin-left: 16px;
	margin-right: 16px;
     a{
      color: #00205A;
      font-size:  12px;
      font-weight: 400;
      line-height:  16px;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-decoration-skip-ink: none;
      text-decoration-thickness: auto;
      text-underline-offset: auto;

    }


}
//   ::ng-deep html[dir="rtl"] .resolution-container {
//   flex-direction: row-reverse;
// }
.row-view24 {
	align-self: stretch;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	gap: 16px;
}
.row-view25 {
	flex-shrink: 0;
	display: flex;
	align-items: flex-start;
	padding-top: 1px;
	padding-bottom: 1px;
	gap: 7px;
  align-items: center;
    justify-content: center;
}
.scroll-view {
	align-items: flex-start;
}
.text {
	color: #000000;
	font-size: 14px;
    @media (max-width: 480px) {
      	font-size: 11px;

    }
}
.text2 {
	color: #1C1C1C;
	font-size: 32px;
	font-weight: bold;
}
.text3 {
	color: #333333;
	text-align: right;
	flex: 1;
}
.text4 {
	color: #002447;
	font-size: 16px;
	font-weight: bold;
}
.text5 {
	color: #4B5662;
	font-size: 12px;
	text-align: right;
	flex: 1;
}
.text6 {
	color: #4F4F4F;
	font-size: 20px;
	font-weight: bold;
	text-align: center;
	margin-top: 4px;
	margin-bottom: 4px;
}
.text7 {
	color: #FFFFFF;
	font-size: 16px;
}
.text8 {
	color: #4B5662;
	font-size: 12px;
}
.text9 {
	color: #05004E;
	font-size: 16px;
	font-weight: bold;
  @media (max-width:768px) {
        margin-bottom: 20px;
  }


}
.text10 {
	color: #00205A;
	font-size: 12px;
}
.text11 {
	color: #4B5662;
	font-size: 10px;
}
.text12 {
	color: #00205A;
	font-size: 16px;
//	margin-left: 85px;
	margin-right: 2px;
}
.text13 {
	color: #00205A;
	font-size: 16px;
	margin-right: 2px;
}
.text14 {
	color: #4B5662;
	font-size: 10px;
//	margin-right: 36px;
}
.text15 {
	color: #000000;
	font-size: 14px;
	flex: 1;
}
.text16 {
	color: #00205A;
	font-size: 10px;
}
.text17 {
	color: #00205A;
	font-size: 16px;
}
a{
  cursor: pointer;
}
.text18 {
	color: #4B5662;
	font-size: 10px;

}
.text19 {
	color: #05004E;
	font-size: 16px;
	margin-bottom: 4px;
}
.text20 {
	color: #4B5662;
	font-size: 12px;
	margin-bottom: 7px;
}
.text21 {
	color: #FFFFFF;
	font-size: 10px;
	width: 17px;
}
.text22 {
	color: #4B5662;
	font-size: 10px;
	margin-right: 56px;
}
.text23 {
	color: #00205A;
	font-size: 14px;
}
.text24 {
	color: #828282;
	font-size: 10px;
}
.text25 {
	color: #00205A;
	font-size: 14px;
	margin-left: 32px;
	margin-right: 32px;
}
.text26 {
	color: #828282;
	font-size: 10px;
	text-align: center;
	margin-left: 19px;
	margin-right: 19px;
}
.view {
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: 8px;
}
.view2 {
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: 8px;
	padding-left: 46px;
	padding-right: 46px;
}
.view3 {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 2px;
}
.view4 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 4px;
	margin-right: 96px;
}
.view5 {
//	flex: 1;
  //   display: flex;
  // //  flex-direction: row-reverse;
  //   align-items: center;
  //   justify-content: space-between;

    a{
      color: #00205A;
      font-size:  12px;
      font-weight: 400;
      line-height:  16px;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-decoration-skip-ink: none;
      text-decoration-thickness: auto;
      text-underline-offset: auto;

    }

}
.view6-resolution {
	align-self: stretch;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}
.view6 {
	align-self: stretch;
	display: flex;
// 	flex-direction: column;
// 	align-items: flex-end;
}
.view7 {
	flex-shrink: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 1px;
}
.view8 {
	flex-shrink: 0;
	display: flex;
  // flex-direction: row-reverse;
  //   align-items: center;
    justify-content: space-between;
    width: 100%;
    a{  color: #00205A;
      font-size:  12px;
      font-weight: 400;
      line-height:  16px;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-decoration-skip-ink: none;
      text-decoration-thickness: auto;
      text-underline-offset: auto;}
}

.view9 {
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #00205A;
	border-radius: 4px;
	padding: 6px 16px;
}


@media (min-width: 1440px) and (max-width: 1440px) {
  .col-custom-350 {
    flex: 0 0 380px;
    max-width: 380px;
  }

   .col-custom-372 {
    flex: 0 0 382px;
    max-width: 382px;
  }


}
