import { Component, Inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { HttpClient, HttpHeaders } from '@angular/common/http';

// Shared components
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// API and services
import {
  DocumentServiceProxy,
  MinIOFileServiceProxy,
  DocumentDto,
  MinIOPreviewCommand,
  ModuleEnum
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { FilePreviewService } from '@core/services/file-preview.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { environment } from 'src/environments/environment';

export interface DocumentViewerData {
  document: DocumentDto;
  bucketName: string;
}

@Component({
  selector: 'app-document-viewer',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    CustomButtonComponent
    ],
  templateUrl: './document-viewer.component.html',
  styleUrls: ['./document-viewer.component.scss']
})
export class DocumentViewerComponent implements OnInit, OnDestroy {
  isLoading = true;
  previewUrl: SafeResourceUrl | null = null;
  errorMessage = '';
  private objectUrl: string | null = null;

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private dialogRef: MatDialogRef<DocumentViewerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentViewerData,
    private documentProxy: DocumentServiceProxy,
    private minioProxy: MinIOFileServiceProxy,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService,
    private sanitizer: DomSanitizer,
    private http: HttpClient,
    private filePreviewService: FilePreviewService
  ) {}

  ngOnInit(): void {
    console.log('DocumentViewerComponent initialized with data:', this.data);
    this.loadDocumentPreview();
  }

  ngOnDestroy(): void {
    // Clean up object URL to prevent memory leaks
    if (this.objectUrl) {
      URL.revokeObjectURL(this.objectUrl);
      this.objectUrl = null;
    }
  }

  loadDocumentPreview(): void {
    console.log('Loading document preview for:', this.data.document);

    if (!this.data.document || !this.data.bucketName) {
      console.error('Missing document or bucket name:', { document: this.data.document, bucketName: this.data.bucketName });
      this.errorMessage = 'DOCUMENTS.PREVIEW_FAILED';
      this.isLoading = false;
      return;
    }

    // Use document's previewUrl if available, otherwise use FilePreviewService
    if (this.data.document.previewUrl) {
      console.log('Using existing preview URL:', this.data.document.previewUrl);
      this.previewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.data.document.previewUrl);
      this.isLoading = false;
    } else {
      console.log('Generating preview URL via FilePreviewService...');
      this.loadPreviewViaService();
    }
  }

  private loadPreviewViaService(): void {
    // Use the FilePreviewService for consistent preview handling
    this.filePreviewService.previewFileById(
      this.data.document.attachmentId!,
      this.data.document.name || ''
    );

    // Since the service handles preview internally, we set loading to false
    // The preview should open in a new window/tab
    this.isLoading = false;
  }

  onDownload(): void {
    console.log('Starting preview/download for document:', this.data.document);

    // Try preview first using FilePreviewService, fallback to download
    this.filePreviewService.previewFileById(
      this.data.document.attachmentId!,
      this.data.document.name || '',
      () => {
        // Fallback to download if preview fails
        this.fallbackToDownload();
      }
    );
  }

  private fallbackToDownload(): void {
    console.log('Falling back to download for document:', this.data.document);

    // Use document's downloadUrl if available, otherwise use MinIO download
    if (this.data.document.downloadUrl) {
      console.log('Using existing download URL:', this.data.document.downloadUrl);
      this.downloadFromUrl(this.data.document.downloadUrl);
    } else {
      console.log('Using MinIO download service...');
      this.downloadViaMinIO();
    }
  }

  private downloadFromUrl(url: string): void {
    const link = document.createElement('a');
    link.href = url;
    link.download = this.data.document.name || 'document';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  private downloadViaMinIO(): void {
    // Try using the NSwag-generated proxy first
    this.minioProxy.downloadFile(this.data.document.id, this.data.bucketName).subscribe({
      next: (response: any) => {
        console.log('MinIO download response:', response);
        if (response instanceof Blob) {
          this.downloadBlob(response);
        } else {
          console.warn('Invalid response from MinIO download, trying direct HTTP call');
          this.downloadViaDirectHttp();
        }
      },
      error: (error: any) => {
        console.error('MinIO proxy download error:', error);
        console.log('Falling back to direct HTTP call...');
        this.downloadViaDirectHttp();
      }
    });
  }

  private downloadViaDirectHttp(): void {
    const url = `${environment.apiUrl}/api/MinIO/MinIOFile/DownloadFile/${this.data.document.id}?bucketName=${encodeURIComponent(this.data.bucketName)}`;

    console.log('Making direct HTTP download call to:', url);

    this.http.get(url, {
      responseType: 'blob'
    }).subscribe({
      next: (blob: Blob) => {
        console.log('Direct HTTP download response blob:', blob);
        this.downloadBlob(blob);
      },
      error: (error: any) => {
        console.error('Direct HTTP download error:', error);
        this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.DOWNLOAD_FAILED'));
      }
    });
  }

  private downloadBlob(blob: Blob): void {
    try {
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = this.data.document.name || 'document';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the object URL
      setTimeout(() => URL.revokeObjectURL(url), 100);
      console.log('Download initiated successfully');
    } catch (error) {
      console.error('Error downloading blob:', error);
      this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.DOWNLOAD_FAILED'));
    }
  }

  onClose(): void {
    this.dialogRef.close();
  }

  getDocumentDisplayName(): string {
    if (this.data?.document?.name) {
      return this.data.document.name;
    }
    return this.translateService.instant('DOCUMENTS.UNKNOWN_FILE');
  }

  getFileExtension(): string {
    if (!this.data.document?.name) return '';
    return this.data.document.name.split('.').pop()?.toLowerCase() || '';
  }

  isPreviewSupported(): boolean {
    const extension = this.getFileExtension();
    return ['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(extension);
  }

  getFileIcon(): string {
    const extension = this.getFileExtension();

    switch (extension) {
      case 'pdf':
        return 'picture_as_pdf';
      case 'doc':
      case 'docx':
        return 'description';
      case 'xls':
      case 'xlsx':
        return 'table_chart';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image';
      default:
        return 'insert_drive_file';
    }
  }

  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
