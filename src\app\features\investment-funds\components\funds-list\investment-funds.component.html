<div class="investment-funds">
    <div class="mb-4">

        <app-page-header [title]="'INVESTMENT_FUNDS.TITLE'"  [showCreateButton]="isHasPermissionAdd  && !isNoData" [showSearch]="true" [showFilter]="true"
            createButtonText="INVESTMENT_FUNDS.CREATE_NEW_FUND" searchPlaceholder="HEADER.SEARCH_PLACEHOLDER"
            (create)="onCreateNewFund()" (search)="onSearch($event)" (filter)="openFilter()"></app-page-header>

    </div>
    <!-- Funds Accordion -->
    <mat-accordion class="funds-accordion" multi *ngIf="!isNoData">
        <mat-expansion-panel *ngFor="let group of fundGroups ; let i=index" [expanded]="group.expanded"
            (opened)="handlePanelOpen(i)" (afterExpand)="group.expanded = true"
            (afterCollapse)="group.expanded = false">
            <mat-expansion-panel-header [collapsedHeight]="'auto'" [expandedHeight]="'auto'">
                <mat-panel-title class="m-0">
                    <div class="d-flex gap-3 align-items-center w-100 h-48 collaps-header">
                        <div class="d-flex justify-content-start align-items-center h-48 gap-3 group-title">
                            <img [src]="'assets/images/' + (group.expanded ? 'accrdion_up.png' : 'accrdion_down.png')"
                                class="accordion-icon" alt="toggle" />
                            {{ group.title }}
    
                        </div>
                        <span class="group-count-badge gap-1" *ngIf="group.count">
                            <span class="red-dot" *ngIf="group.hasNotification"></span>
                            <span *ngIf="group.count>2">
                                {{ group.count }}
                            </span>
                            {{ getFundLabel(group.count) |translate}}
                        </span>
                    </div>
                </mat-panel-title>
            </mat-expansion-panel-header>

            <!-- Funds Grid -->
            <div class="funds-grid">
                <div class="row">
                    <!-- Fund Card Template -->
                    <div class="col-12 col-md-6 col-lg-4 col-xl-3 p-2" *ngFor="let fund of group.funds">
                        <div class="fund-card hover-shadow" (click)="navigateToUpdate(fund)">
                            <div class="card-header">
                                <div class="status-section">
                                    <div class="status-badge" [ngClass]="getStatusClass(fund.statusId)">
                                        <span class="dot"></span>
                                        {{fund.status }}
                                    </div>
                                    <div class="d-flex gap-3">
                                        <div class="bill-icon-wrapper position-relative">
                                            <img src="assets/images/bill.png" alt="bill" />
                                            <span class="notification-badge"
                                                *ngIf="fund.notificationTotalCount && fund.notificationTotalCount > 0">
                                                {{fund.notificationTotalCount}}
                                            </span>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="row m-0 h-100">
                                <div class="card-body col-12">
                                    <h3 class="fund-name">{{ fund.name}}</h3>
                                    <div class="row flex-nowrap justify-content-start">
                                        <div class="info-row col" *ngIf="fund.initiationDate">
                                            <span class="label">{{ 'INVESTMENT_FUNDS.DATES.CREATION_DATE' | translate
                                                }}</span>
                                            <span class="value">{{ fund.initiationDate.toJSDate() | date:'dd / M / yyyy'
                                                }}</span>
                                            <div class="hijri-value">{{formatDateToString(fund.initiationDate)|
                                                dateHijriConverter:'hijri' }}</div>
                                        </div>
                                        <div *ngIf="fund.exitDate" class="info-row col">
                                            <span class="label">{{ 'INVESTMENT_FUNDS.DATES.EXIT_DATE' | translate }}</span>
                                            <span class="value">{{ fund.exitDate.toJSDate() | date:'dd / M / yyyy' }}</span>
                                            <div class="hijri-value">{{ formatDateToString(fund.exitDate)|
                                                dateHijriConverter:'hijri' }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer  d-flex  justify-content-end p-0">
                                    <div class="rotate-icon">
                                        <!-- <img src="assets/images/Arrow_Icon.png" alt="Arrow" class="arrow-icon rotate-icon" /> -->
                                         <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                                            <path d="M8.2765 12.3145L4.96289 9.00084M4.96289 9.00084L8.2765 5.68724M4.96289 9.00084L12.5373 9.00084" stroke="#00205A" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8.75 16.5C12.8921 16.5 16.25 13.1421 16.25 9C16.25 4.85787 12.8921 1.5 8.75 1.5C4.60787 1.5 1.25 4.85786 1.25 9C1.25 13.1421 4.60786 16.5 8.75 16.5Z" stroke="#00205A"/>
                                            <path d="M8.75037 1.32934C12.9871 1.32934 16.4216 4.76388 16.4216 9.00061C16.4216 13.2373 12.9871 16.6719 8.75037 16.6719C4.51365 16.6719 1.0791 13.2373 1.0791 9.00061C1.0791 4.76388 4.51365 1.32934 8.75037 1.32934Z" stroke="#00205A"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </mat-expansion-panel>
    </mat-accordion>

    <div *ngIf="isNoData"
        class="d-flex  flex-column gap-4 justify-content-center align-items-center">
        <img src="assets/images/nodata.png" width="350" alt="No Data" />

        <ng-container>
            <p  class="text-center header fs-20">{{'INVESTMENT_FUNDS.NO_DATA' | translate}}</p>
            <app-custom-button *ngIf="isHasPermissionAdd" [btnName]="'INVESTMENT_FUNDS.CREATE_NEW_FUND' | translate"
                [iconName]="createButtonIcon.plus" (click)="onCreateNewFund()">
            </app-custom-button>
        </ng-container>
    </div>
</div>
