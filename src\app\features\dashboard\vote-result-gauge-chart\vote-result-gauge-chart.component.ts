import { VoteResult } from './../../../core/api/api.generated';
import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { NgxChartsModule, Color, ScaleType } from '@swimlane/ngx-charts';

// API Types
import { ResolutionStatusBreakdownDto, ResolutionVotesBreakdownDto } from '@core/api/api.generated';

export interface GaugeChartData {
  name: string;
  value: number;
  extra?: {
    code: string;
    color: string;
  };
}

@Component({
  selector: 'app-vote-result-gauge-chart',
  standalone: true,
  imports: [CommonModule, TranslateModule, NgxChartsModule],
  templateUrl: './vote-result-gauge-chart.component.html',
  styleUrl: './vote-result-gauge-chart.component.scss'
})
export class VoteResultGaugeChartComponent implements OnChanges {
  @Input() voteResultData: ResolutionVotesBreakdownDto[] = [];
  @Input() loading: boolean = false;
  @Input() totalVotes: number = 0;
  // Chart configuration
  chartData: GaugeChartData[] = [];

  // Chart options for gauge
  view: [number, number] = [170, 170];
  colorScheme: Color = {
    name: 'vote-results',
    selectable: true,
    group: ScaleType.Ordinal,
   domain: ['#28a745', '#dc3545', '#ffc107']
  };
  // Gauge specific options
  showLegend: boolean = true;
  showLabels: boolean = true;
  animations: boolean = true;
  angleSpan: number = 240;
  startAngle: number = -120;
  units: string = '%';
  bigSegments: number = 10;
  smallSegments: number = 5;

  // Vote result color mapping
  private voteColors: { [key: string]: string } = {
    '2': '#28a745',
    '3': '#dc3545',
    '1': '#ffc107',
  };

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['voteResultData'] || changes['totalVotes']) {
      this.updateChartData();
    }
  }

  /**
   * Transform API data to chart format and calculate percentages
   */
  private updateChartData(): void {
    if (!this.voteResultData || this.voteResultData.length === 0) {
      this.resetChartData();
      return;
    }


    // Transform data for chart display
    this.chartData = this.voteResultData.map(item => {
      const voteKey = item.voteResult.toString() || '0';
      const color = this.voteColors[voteKey] || '#ffc107';
      return {
        name: item.voteResultDisplay || 'Unknown',
        value: item.count || 0,
        extra: {
          code: voteKey,
          color: color,
        }
      };
    });
    this.colorScheme = {
      name: 'vote-results',
      selectable: true,
      group: ScaleType.Ordinal,
      domain: this.chartData.map(item => item.extra?.color || '#ffc107')
    };
  }



  /**
   * Reset chart data when no data available
   */
  private resetChartData(): void {
    this.chartData = [];

  }

  /**
   * Check if chart has data
   */
  get hasData(): boolean {
    return this.chartData.length > 0;
  }



  /**
   * TrackBy function for chart data
   */
  trackByName(_index: number, item: GaugeChartData): string {
    return item.name;
  }

   get centerPercentage(): number {
  const total = this.chartData.reduce((sum, item) => sum + item.value, 0);
  const first = this.chartData[0]?.value || 0;
  return total ? Math.round((first / total) * 100) : 0;
}
}
