@import "../../../../../assets/scss/variables";

.create-assessment-page {
  padding: 20px;
  min-height: 100vh;

  .breadcrumb-section {
    margin-bottom: 20px;
  }

  .page-header-section {
    margin-bottom: 30px;
  }

  .main-content {
    .create-form-container {
      background: #F8FAFC;
      border-radius: 16px;
      border: 0.5px solid #DCE0E3;
      padding: 16px;

      .form-section {
        margin-bottom: 20px;

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

        }
        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          h6 {
            font-size: 16px;
            font-weight: 600;
            color: #00205a;
            margin: 0;
          }

          .items-num{
            display: inline-flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            padding: 8px;
            border-radius: 14px;
            background: rgba(38, 86, 135, 0.12);
            color: #000;
            font-size: 16px;
            font-weight: 400;
            line-height: 18px;
          }

          .add-item-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            // border: 2px solid #007bff;
            background: white;
            color: $navy-blue;
            border-radius: 8px;
            font-weight: 400;
            transition: all 0.2s ease;

            // &:hover {
            //   background: #007bff;
            //   color: white;
            //   transform: translateY(-1px);
            //   box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            // }

            &:disabled {
              opacity: 0.3;
              cursor: not-allowed;
              transform: none;
              box-shadow: none;
            }
          }
        }

        // Form builder header styling
        .header {
          font-size: 16px;
          font-weight: 600;
          color: #00205a;
          margin-bottom: 10px;
        }

      }
    }
  }

  // Assessment Questions Styles - Following resolution items pattern
  .items-container {
    margin-bottom: 20px;

    .assessment-question-card {
      padding: 16px;
      border-radius: 8px;
      background-color: white;
      margin-bottom: 16px;

      .item-header {
        .item-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .item-number {
            font-size: 20px;
            font-weight: 500;
            // line-height: 32px;
            color: #00205a;

            .question-type-badge {
              display: inline-block;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: 600;
              letter-spacing: 0.5px;
              background: #e3f2fd;
              color: #1976d2;
              margin-inline-start: 10px;
            }
          }

          .item-actions {
            display: flex;
            gap: 8px;

            .btn {
              padding: 8px;
              &:hover{
                background: unset;
                border: unset;
                color: unset;
              }
            }
          }
        }
      }

      .item-body {
        margin-top: 12px;

        .item-description {
          font-size: 16px;
          color: $light-dark;
          margin-bottom: 8px;
          line-height: 1.4;

          &:lang(ar){
            &::after {
              content: "؟";
            }
          }
          &:lang(en){
            &::after {
              content: "?";
            }
          }
        }

        .question-type-info {
          display: flex;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;

          .question-options {
            color: $light-dark;
            width: 100%;
            .option-number {
              background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
              color: #374151;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: 600;
              border: 2px solid #E5E7EB;
            }

            .options-header{
              margin-bottom: 16px;
              .options-title {
                font-size: 14px;
                font-weight: 600;
                color: $navy-blue;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 8px;
  
                .options-icon {
                  color: #6B7280;
                  font-size: 13px;
                }

                .stats-item {
                  display: flex;
                  align-items: center;
                  gap: 6px;
                  padding: 6px 12px;
                  background: #F3F4F6;
                  border-radius: 16px;
                  font-size: 12px;
                  color: #6B7280;
                }
              }
            }

            .options-list {
              .option-item {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                padding: 12px 0;
                border-bottom: 1px solid #F3F4F6;
                transition: all 0.2s ease;

                &:last-child {
                  border-bottom: none;
                }

                .option-indicator {
                  .option-number {
                    background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
                    color: #374151;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    font-weight: 600;
                    flex-shrink: 0;
                    border: 2px solid #E5E7EB;
                  }
                }

                .option-content {
                  flex: 1;

                  .option-text {
                    font-size: 14px;
                    color: #374151;
                    line-height: 1.5;
                    font-weight: 400;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .empty-items {
    padding: 40px 20px;
    text-align: center;

    .add-item-btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      // border: 2px solid #007bff;
      background: white;
      color: $navy-blue;
      border-radius: 8px;
      font-weight: 400;
      transition: all 0.2s ease;

      // &:hover {
      //   background: #007bff;
      //   color: white;
      //   transform: translateY(-1px);
      //   box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
      // }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }

      // Form Actions
      .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e0e0e0;

        .actions-container {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
        }
      }
    }
  


// Responsive Design
@media (max-width: 768px) {
  .create-assessment-page {
    padding: 0px;

    .main-content {
      .create-form-container {
        padding: 15px;

        .form-actions {
          .actions-container {
            flex-direction: column;

            app-custom-button {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
