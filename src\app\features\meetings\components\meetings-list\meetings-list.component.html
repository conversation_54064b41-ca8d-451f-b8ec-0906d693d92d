<div class="meetings-page">
  <!-- Breadcrumb -->
  <!-- <div class="breadcrumb-section">
    <app-breadcrumb (onClickEvent)="onBreadcrumbClicked($event)" [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium" divider=">">
    </app-breadcrumb>
  </div> -->
  <!-- Page Header -->
  <div class="page-header-section">
    <app-page-header [title]="'MEETINGS.TITLE' | translate" [showSearch]="true" [showFilter]="true"
      [showCreateButton]="true" [showBackButton]="true"
      [searchPlaceholder]="translateService.instant('MEETINGS.SEARCH_PLACEHOLDER')"
      [restrictSearchToNumbers]="false" createButtonText="MEETINGS.ADD" (search)="onSearch($event)"
      (filter)="openFilter()" (create)="addMeeting()" *ngIf="isHasPermissionAdd">
    </app-page-header>
  </div>

  <!-- Main Content -->
  <div class="content-section">
    <!-- Loading State -->
    <!-- <div *ngIf="isLoading" class="d-flex justify-content-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
      </div>
    </div> -->

    <!-- Error State -->

    <div *ngIf="hasError && !isLoading" class="d-flex flex-column gap-4 justify-content-center my-5 align-items-center">
    <img src="assets/images/nodata.png" width="350">

    <ng-container >
        <p  class="text-center mt-3 header fs-20">{{'INVESTMENT_FUNDS.NO_DATA' | translate}}</p>
        <!-- <app-custom-button [btnName]="'COMMON.RETRY' | translate" class="w-auto" (click)="loadMeetings()">
      </app-custom-button> -->
    </ng-container>
</div>



    <!-- Meetings Grid -->
    <div *ngIf="!isLoading && !hasError && meetings.length > 0" class="meetings-grid">
      <!-- Meeting Card -->
      <div *ngFor="let meeting of meetings" class="meeting-card">
        <ng-container *ngIf="meeting.canViewDetails">
          <div class="card-header">
            <div class="status">
              <span class="status" [ngClass]="getStatusClass(meeting.meetingStatusId)">
                {{ meeting.meetingStatusLocalized }}
              </span>
            </div>
            <div class="card-actions">
              <!-- Meeting Minutes Button -->
              <button *ngIf="canShowMeetingMinutes(meeting)" class="action-btn minutes-btn"
                      (click)="openMeetingMinutes(meeting)" >
                <img src="assets/images/file-text.svg" alt="minutes" />
              </button>
              <!-- View Details Button -->
              <button *ngIf="meeting.canViewDetails" class="action-btn details-btn"
                      (click)="viewMeetingDetails(meeting)"
                      [title]="'COMMON.VIEW_DETAILS' | translate">
                <img src="assets/images/eye.png" alt="details" />
              </button>
              <!-- Edit Button -->
              <button *ngIf="meeting.canEdit" class="action-btn"
                      (click)="editMeeting(meeting)"
                      [title]="'COMMON.EDIT' | translate">
                <img src="assets/images/edit.png" alt="edit" />
              </button>
              <!-- Cancel Button -->
              <button *ngIf="meeting.canCancel" class="action-btn"
                      (click)="cancelMeeting(meeting)"
                      [title]="'COMMON.CANCEL' | translate">
                <img src="assets/images/x.png" class="mx-2" alt="cancel">
              </button>
            </div>
          </div>
          <div class="card-content clickable-card" (click)="viewMeetingDetails(meeting)">
            <h2 class="meeting-title mt-lg-3">
              {{ meeting.subject || ('MEETINGS.NO_SUBJECT' | translate) }}
            </h2>

            <!-- <p class="title">{{ 'MEETINGS.TYPE' | translate }}</p>
            <p class="meeting-type">{{ meeting.meetingTypeName || ('MEETINGS.NO_TYPE' | translate) }}</p> -->

            <p class="title">{{ 'MEETINGS.PLACE' | translate }}</p>
            <p class="meeting-type">
              {{ meeting.meetingRoomDetails || meeting.onlineMeetingLink || ('MEETINGS.NO_LOCATION' | translate) }}
            </p>

            <p class="title">{{ 'MEETINGS.ATTENDEES' | translate }}</p>
            <p class="meeting-type">{{ meeting.attendeeCount }} {{ 'MEETINGS.ATTENDEE' | translate }}</p>

            <p class="title">{{ 'MEETINGS.DESCRIPTION' | translate }}</p>
            <p class="meeting-description description-text">
              {{ meeting.description || ('MEETINGS.NO_DESCRIPTION' | translate) }}
            </p>

            <div class="meeting-meta">
              <div class="meta-item">
                <span class="meta-label">{{ 'MEETINGS.DATE' | translate }}:</span>
                <p class="gregorian mb-0">{{ meeting.meetingDate.toJSDate() | date:'dd/MM/yyyy' }}</p>
                <p class="hijri title">{{ meeting.meetingDate | dateHijriConverter }}</p>
              </div>
              <div class="meta-item" *ngIf="meeting.formattedTime">
                <span class="meta-label">{{ 'MEETINGS.TIME' | translate }}:</span>
                <p class="gregorian mb-0">{{ meeting.formattedTime }}</p>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Pagination Controls -->


    <!-- Empty State -->
    <div *ngIf="!isLoading && !hasError && meetings.length === 0"
      class="d-flex flex-column gap-4 justify-content-center my-5 align-items-center">
      <img src="assets/images/nodata.png" width="350" alt="No data">

      <ng-container>
        <p class="text-center header fs-20">{{ 'MEETINGS.NO_DATA' | translate }}</p>
        <app-custom-button *ngIf="isHasPermissionAdd"
                          [btnName]="'MEETINGS.CREATE_FIRST' | translate"
                          class="w-auto"
                          [iconName]="createButtonIcon.plus"
                          (click)="addMeeting()">
        </app-custom-button>
      </ng-container>
    </div>

    <div class="pagination-section" *ngIf="totalCount > 0">
      <!-- Pagination Controls -->
      <div class="pagination-controls">
        <!-- Previous Page Button -->
        <button class="pagination-btn prev-btn" [disabled]="!canGoPrevious()" (click)="onPreviousPage()"
          [title]="'PAGINATION.PREVIOUS' | translate">
          <!-- <i class="fas fa-angle-left"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-left2.png' : 'assets/images/arrow-right.png'" class="mx-2"
            alt="previous">

          <span class="btn-text">{{ 'PAGINATION.PREVIOUS' | translate }}</span>
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button *ngFor="let page of getPageNumbers()" class="pagination-btn page-number-btn"
            [class.active]="page === currentPage" (click)="onPageChange(page)">
            {{page}}
          </button>
        </div>

        <!-- Next Page Button -->
        <button class="pagination-btn next-btn" [disabled]="!canGoNext()" (click)="onNextPage()"
          [title]="'PAGINATION.NEXT' | translate">
          <span class="btn-text">{{ 'PAGINATION.NEXT' | translate }}</span>
          <!-- <i class="fas fa-angle-right"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-right.png' : 'assets/images/arrow-left2.png'" class="mx-2"
            alt="next">
        </button>
      </div>
    </div>
  </div>
</div>
