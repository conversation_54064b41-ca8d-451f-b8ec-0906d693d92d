.dialog-container {
  padding: 24px;
  max-width: 600px;
  min-width: 500px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  .dialog-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #00205a;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
      color: #333;
    }
  }
}

.dialog-content {
  margin-bottom: 24px;

  .assessment-info {
    .info-text {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      margin: 0;
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #dc3545;
    }
  }

  .form-section {
    ::ng-deep {
      .form-group {
        margin-bottom: 1.5rem;

        label {
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
          display: block;

          &.required::after {
            content: ' *';
            color: #dc3545;
          }
        }

        textarea {
          resize: vertical;
          min-height: 80px;
        }

        .validation-message {
          color: #dc3545;
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
  }
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;

  ::ng-deep {
    app-custom-button {
      .btn {
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.2s ease;
        min-width: 120px;
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .dialog-actions {
    justify-content: flex-start;
  }

  .assessment-info .info-text {
    border-left: none;
    border-right: 4px solid #dc3545;
  }
}
