import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';


// Shared Components
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Core Services and Interfaces
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@shared/enum/size-enum';

// API Generated Types - Following resolution component patterns
import {
  AssessmentServiceProxy,
  CreateAssessmentCommand,
  CreateAssessmentQuestionDto,
  AssessmentType,
  StringBaseResponse,
  CreateAssessmentOptionDto,
} from '@core/api/api.generated';

// Services
import { ErrorModalService } from '@core/services/error-modal.service';

// Question Dialog
import { QuestionDialogComponent, QuestionDialogData } from '../question-dialog/question-dialog.component';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-create-assessment',
  standalone: true,
  imports: [
    CommonModule,
    BreadcrumbComponent,
    PageHeaderComponent,
    FormBuilderComponent,
    CustomButtonComponent,
    TranslateModule,
  ],
  templateUrl: './create-assessment.component.html',
  styleUrls: ['./create-assessment.component.scss']
})
export class CreateAssessmentComponent implements OnInit, OnDestroy {
  // Lifecycle management
  private destroy$ = new Subject<void>();

  // Form properties
  formGroup!: FormGroup;
  formControls: IControlOption[] = [];
  isValidationFire = false;
  isFormSubmitted = false;
  isApiCallInProgress = false;

  // UI Properties - Following edit-resolution pattern
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  canAddAdditionalAttachments: boolean = true;

  // UI enums
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  assessmentType = AssessmentType;

  // Data properties
  currentFundId = 0;
  title: string = 'ASSESSMENTS.CREATE_NEW';

  // Questions management (for questionnaire type) - Following resolution component patterns
  questions: CreateAssessmentQuestionDto[] = [];
  currentQuestionIndex = 0;

  // File upload properties
  uploadedFileId: number | null = null;
  uploadedFileName: string = '';
  isFileUploading = false;
  fileUploadError: string = '';
  currentFundName = '';

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private assessmentServiceProxy: AssessmentServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService,
    private dialog: MatDialog,
    private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit(): void {
    this.currentFundName=localStorage.getItem('fundName') || '';
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Component Initialization - Following edit-resolution pattern
  private initializeComponent(): void {
    this.getFundIdFromRoute();
    this.initializeFormControls();
    this.initForm();
    this.updateBreadcrumbWithFallback();
  }

  private getFundIdFromRoute(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.currentFundId = +params['fundId'] || 0;
        if (this.currentFundId <= 0) {
          this.router.navigate(['/admin/investment-funds']);
        } else {
          this.updateBreadcrumbWithFallback();
        }
      });
  }

  // Breadcrumb Management - Following edit-resolution pattern
  private updateBreadcrumbWithFallback(): void {
    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`,
      },
      {
        label:'ASSESSMENTS.TITLE',
        url: `/admin/investment-funds/assessments?fundId=${this.currentFundId}`
      },
      { label: 'BREADCRUMB.CREATE_ASSESSMENT' , url: '', disabled: true },
    ];
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

 // Add method to handle date selection
  dateSelected(event: any): void {
    this.formGroup
      .get(event.control.formControlName)
      ?.setValue(event.event.formattedGregorian);
  }

  onBreadcrumbClicked(event: any): void {
    if (event?.url) {
      this.router.navigateByUrl(event.url);
    }
  }

  // Form Controls Configuration - Following edit-resolution pattern
  private initializeFormControls(): void {
    this.formControls = [
      {
        type: InputType.TextAndNumber,
        formControlName: 'title',
        id: 'title',
        name: 'title',
        label: 'ASSESSMENTS.ASSESSMENT_TITLE',
        placeholder: 'ASSESSMENTS.ASSESSMENT_TITLE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-12',
        maxLength: 200
      },

      {
        type: InputType.Textarea,
        formControlName: 'description',
        id: 'description',
        name: 'description',
        label: 'ASSESSMENTS.DESCRIPTION',
        placeholder: 'ASSESSMENTS.DESCRIPTION_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        maxLength: 1000
      },
      {
        type: InputType.Textarea,
        formControlName: 'instructions',
        id: 'instructions',
        name: 'instructions',
        label: 'ASSESSMENTS.INSTRUCTIONS',
        placeholder: 'ASSESSMENTS.INSTRUCTIONS_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        maxLength: 2000
      },
      {
        type: InputType.Radio,
        formControlName: 'type',
        id: 'type',
        name: 'type',
        label: 'ASSESSMENTS.ASSESSMENT_TYPE',
        isRequired: true,
        class: 'col-md-12',
        options: [
          { name: 'ASSESSMENTS.QUESTIONNAIRE', id: AssessmentType._1 },
          { name: 'ASSESSMENTS.ATTACHMENT', id: AssessmentType._2 }
        ],
        onChange: (value: any) => this.onAssessmentTypeChange(value)
      },
      {
        type: InputType.file,
        formControlName: 'attachmentId',
        id: 'attachmentId',
        name: 'attachmentId',
        label: 'ASSESSMENTS.ATTACHMENT',
        placeholder: 'ASSESSMENTS.ATTACHMENT_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        allowedTypes: ['pdf'],
        max: 10,
        isVisible: () => this.formGroup?.get('type')?.value === AssessmentType._2
      }
    ];
  }

  // Form Initialization - Following edit-resolution pattern
  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      title: ['', [Validators.required, Validators.maxLength(200)]],
      description: ['', [Validators.maxLength(1000)]],
      instructions: ['', [Validators.maxLength(2000)]],
      type: [AssessmentType._1, Validators.required],
      attachmentId: [null]
    });

    // Watch for type changes to update attachment field visibility
    this.formGroup.get('type')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.updateAttachmentFieldVisibility(value);
      });

    // Initialize with default type
    this.updateAttachmentFieldVisibility(AssessmentType._1);
  }

  private updateAttachmentFieldVisibility(assessmentType: AssessmentType): void {
    const attachmentControl = this.formControls.find(c => c.formControlName === 'attachmentId');
    if (attachmentControl) {
      attachmentControl.isVisible = () => assessmentType === AssessmentType._2;

      // Update validation based on type
      const attachmentFormControl = this.formGroup.get('attachmentId');
      if (assessmentType === AssessmentType._2) {
        attachmentFormControl?.setValidators([Validators.required]);
        attachmentControl.isRequired = true;
      } else {
        attachmentFormControl?.clearValidators();
        attachmentControl.isRequired = false;
        attachmentFormControl?.setValue(null);
      }
      attachmentFormControl?.updateValueAndValidity();
    }
  }

  // Event handlers - Following edit-resolution pattern
  onAssessmentTypeChange(value: any): void {
    this.updateAttachmentFieldVisibility(value);
  }


  onFileUpload(data: any): void {
    this.isFileUploading = true;
    this.fileUploadError = '';

    try {
      if (data.file && data.file.id) {
        this.uploadedFileId = data.file.id;
        this.uploadedFileName = data.file.name || this.translateService.instant('ASSESSMENTS.UPLOADED_FILE');
        this.formGroup.get(data.control.formControlName)?.setValue(data.file.id);

        // Clear any previous validation errors
        const attachmentControl = this.formGroup.get('attachmentId');
        if (attachmentControl?.hasError('required')) {
          attachmentControl.updateValueAndValidity();
        }
      } else {
        this.fileUploadError = 'ASSESSMENTS.VALIDATION.FILE_UPLOAD_FAILED';
      }
    } catch (error) {
      this.fileUploadError = 'ASSESSMENTS.VALIDATION.FILE_UPLOAD_ERROR';
      // TODO: Integrate with notification service
      console.error('File upload error:', error);
    } finally {
      this.isFileUploading = false;
    }
  }

  onFileRemove(): void {
    this.uploadedFileId = null;
    this.uploadedFileName = '';
    this.fileUploadError = '';
    this.formGroup.get('attachmentId')?.setValue(null);
  }

  onSubmit(): void {
    this.isValidationFire = true;

    // Validate form based on assessment type
    if (!this.validateFormForSubmission()) {
      return;
    }

    if (this.formGroup.valid && !this.isFormSubmitted) {
      this.isFormSubmitted = true;
      this.callApi();
    }
  }

  private validateFormForSubmission(): boolean {
    const assessmentType = this.formGroup.get('type')?.value;

    // Validate questionnaire type
    if (assessmentType === AssessmentType._1) {
      if (this.questions.length === 0) {
        this.showValidationError('ASSESSMENTS.VALIDATION.QUESTIONS_REQUIRED');
        return false;
      }

      
    }

    // Validate attachment type
    if (assessmentType === AssessmentType._2) {
      if (!this.uploadedFileId) {
        this.showValidationError('ASSESSMENTS.VALIDATION.ATTACHMENT_REQUIRED');
        return false;
      }
    }

    return true;
  }

  private showValidationError(messageKey: string): void {
    // Use ErrorModalService following resolution component patterns
    this.errorModalService.showError(this.translateService.instant(messageKey));
  }

  onSaveAsDraft(): void {
    this.isValidationFire = true;

    // For draft, we only require title and type
    if (!this.validateFormForDraft()) {
      return;
    }

    if (!this.isFormSubmitted) {
      this.isFormSubmitted = true;
      this.callApi(true); // Pass true for draft mode
    }
  }

  private validateFormForDraft(): boolean {
    const titleValid = this.formGroup.get('title')?.valid;
    const typeValid = this.formGroup.get('type')?.valid;

    if (!titleValid) {
      this.showValidationError('ASSESSMENTS.VALIDATION.TITLE_REQUIRED');
      return false;
    }

    if (!typeValid) {
      this.showValidationError('ASSESSMENTS.VALIDATION.TYPE_REQUIRED');
      return false;
    }

    return true;
  }

  onCancel(): void {
    this.router.navigate(['/admin/investment-funds/assessments'], {
      queryParams: { fundId: this.currentFundId }
    });
  }

  // API call - Following resolution component patterns
  private callApi(isDraft: boolean = false): void {
    this.isApiCallInProgress = true;

    const command = new CreateAssessmentCommand({
      fundId: this.currentFundId,
      title: this.formGroup.get('title')?.value ?? '',
      type: this.formGroup.get('type')?.value ?? AssessmentType._1,
      attachmentId: this.formGroup.get('attachmentId')?.value || undefined,
      questions: this.formGroup.get('type')?.value === AssessmentType._1 ? this.questions : undefined,
      saveAsDraft: isDraft,
      allowAnonymousResponses: false,
      allowResponseEditing: true,
      description: this.formGroup.get('description')?.value ?? undefined,
      instructions: this.formGroup.get('instructions')?.value ?? undefined
    });

    this.assessmentServiceProxy.create(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
        

          if (response.successed) {
            // Show success message using ErrorModalService
            const messageKey = isDraft ? 'ASSESSMENTS.SUCCESS_SAVED_DRAFT' : 'ASSESSMENTS.SUCCESS_CREATED';
            this.errorModalService.showSuccess(this.translateService.instant(messageKey));

            // Add delay before navigation to allow users to see the success message
            setTimeout(() => {
              this.router.navigate(['/admin/investment-funds/assessments'], {
                queryParams: { fundId: this.currentFundId }
              });
                this.isFormSubmitted = false;
          this.isValidationFire = false;
          this.isApiCallInProgress = false;
            }, 2000); // 2 second delay to show success message
          } else {
              this.isFormSubmitted = false;
          this.isValidationFire = false;
          this.isApiCallInProgress = false;
            // this.errorModalService.showError(
            //   response.message || this.translateService.instant('ASSESSMENTS.CREATE_ERROR')
            // );
          }
        },
        error: (error) => {
          this.isFormSubmitted = false;
          this.isValidationFire = false;
          this.isApiCallInProgress = false;
          console.error('Error creating assessment:', error);

          // Try to extract error message from API response
          let errorMessage = this.translateService.instant('ASSESSMENTS.CREATE_ERROR');
          if (error?.error?.message) {
            errorMessage = error.error.message;
          } else if (error?.message) {
            errorMessage = error.message;
          } else if (typeof error === 'string') {
            errorMessage = error;
          }

          // this.errorModalService.showError(errorMessage);
        }
      });
  }

  // Question Management Methods - Following resolution item patterns
  addAssessmentQuestion(): void {
    const dialogData: QuestionDialogData = {
      isEdit: false,
      existingQuestions: this.questions
    };

    const dialogRef = this.dialog.open(QuestionDialogComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.questions.push(result);
        this.reorderQuestions();
      }
    });
  }

  editAssessmentQuestion(question: CreateAssessmentQuestionDto, index: number): void {
    const dialogData: QuestionDialogData = {
      question: question,
      existingQuestions: this.questions,
      isEdit: true,
      questionIndex: index
    };

    const dialogRef = this.dialog.open(QuestionDialogComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.questions[index] = result;
      }
    });
  }

  // Legacy method names for backward compatibility
  openQuestionDialog(): void {
    this.addAssessmentQuestion();
  }

  editQuestion(index: number): void {
    const question = this.questions[index];
    this.editAssessmentQuestion(question, index);
  }

  removeQuestion(index: number): void {
    this.questions.splice(index, 1);
    this.reorderQuestions();
  }

  private reorderQuestions(): void {
    // Update display order for all questions - Following resolution item pattern
    this.questions.forEach((question, index) => {
      question.order = index + 1;
      // Update title to reflect new order if needed
      if (!question.questionText?.includes(`${this.translateService.instant('ASSESSMENTS.QUESTION')} ${index + 1}`)) {
        // Only update if it follows the pattern
      }
    });
  }



  // Utility methods
  isQuestionnaireType(): boolean {
    return this.formGroup?.get('type')?.value === AssessmentType._1;
  }

  isAttachmentType(): boolean {
    return this.formGroup?.get('type')?.value === AssessmentType._2;
  }
getOptions(options:CreateAssessmentOptionDto[]):CreateAssessmentOptionDto[]
{
  return options.filter(o=>!o.isOther);
}
}
