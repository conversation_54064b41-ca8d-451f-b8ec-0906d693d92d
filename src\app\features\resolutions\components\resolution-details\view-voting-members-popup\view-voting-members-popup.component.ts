import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { TranslateModule } from '@ngx-translate/core';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

@Component({
  selector: 'app-view-voting-members-popup',
  standalone: true,
  imports: [TranslateModule, CommonModule, CustomButtonComponent],
  templateUrl: './view-voting-members-popup.component.html',
  styleUrl: './view-voting-members-popup.component.scss'
})
export class ViewVotingMembersPopupComponent {
  constructor(
    public dialogRef: MatDialogRef<ViewVotingMembersPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ){}

  createButtonIcon = IconEnum;
  ButtonTypeEnum = ButtonTypeEnum;

  back(){
    this.dialogRef.close();
  }
}
