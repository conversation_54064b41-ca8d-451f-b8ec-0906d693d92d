import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { IconEnum } from '@core/enums/icon-enum';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { MeetingStatusEnum } from '@shared/enum/meeting-enums';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { MeetingFilterPopupComponent } from '../meeting-filter-popup/meeting-filter-popup.component';
import { CancelMeetingCommand, MeetingServiceProxy, SingleMeetingResponse, SingleMeetingResponsePaginatedResult, StringBaseResponse } from '@core/api/api.generated';
import { Subject, takeUntil } from 'rxjs';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { CancelMeetingDialogComponent } from '../cancel-meeting-dialog/cancel-meeting-dialog.component';
import { ErrorModalService } from '@core/services/error-modal.service';
import { MeetingMinutesDialogData, MeetingMinutesPopupComponent } from '../meeting-minutes-popup/meeting-minutes-popup.component';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';
// import { MeetingMinutesPopupComponent, MeetingMinutesDialogData } from '../meeting-minutes-popup/meeting-minutes-popup.component';

@Component({
  selector: 'app-meetings-list',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbComponent,
    PageHeaderComponent,
    DateHijriConverterPipe,
    CustomButtonComponent,
  ],
  templateUrl: './meetings-list.component.html',
  styleUrl: './meetings-list.component.scss'
})
export class MeetingsListComponent {
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
    { label: 'INVESTMENT_FUNDS.MEETING.TITLE', url: '' },
  ];
  breadcrumbSizeEnum = SizeEnum;
  createButtonIcon = IconEnum;
  meetingStatus = MeetingStatusEnum;

  // API Integration Properties
  meetings: SingleMeetingResponse[] = [];
  isLoading = false;
  hasError = false;
  errorMessage = '';
  totalCount: number = 0;
  totalPages = 0;
  isHasPermissionAdd: boolean = false;

  // Filter and search properties
  filter: any = {};
  search = '';
  fundId = 0;
  statusId?: number;
  typeId?: number;
  fromDate?: string;
  toDate?: string;
  onlyMyMeetings?: boolean;
  onlyUpcoming?: boolean;

  // Pagination properties
  currentPage = 1;
  pageSize = 12;

  // Lifecycle management
  private destroy$ = new Subject<void>();
  roleName: string | undefined;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public translateService: TranslateService,
    private dialog: MatDialog,
    private meetingServiceProxy: MeetingServiceProxy,
    private tokenService: TokenService,
    private errorModalService: ErrorModalService,
    private breadcrumbService: BreadcrumbService
  ) { }

  ngOnInit(): void {
    this.roleName = this.tokenService.getroles();
    if (this.roleName?.includes('boardsecretary') || this.roleName?.includes('legalcouncil') 
      || this.roleName?.includes('headofrealestate')) {
      this.isHasPermissionAdd = true;
    }
    // Get fundId from route parameters
    this.route.queryParams.subscribe((queryParams) => {
      this.fundId = +queryParams['fundId'] || 0;
      this.loadMeetings();
    });
    this.initializeBreadcrumbs();

    this.initializeBreadcrumbs();

  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumbs(): void {
    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label: currentFundName || 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      { label: 'INVESTMENT_FUNDS.MEETING.MEETINGSTITLE', url: '', disabled: true },
    ];
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  /**
   * Load meetings from API
   */
  loadMeetings(): void {
    if (this.fundId <= 0) {
      this.hasError = true;
      this.errorMessage = 'INVESTMENT_FUNDS.MEETING.INVALID_FUND_ID';
      return;
    }

    this.isLoading = true;
    this.hasError = false;

    this.meetingServiceProxy.meetingList(
      this.fundId,
      this.statusId == null ? undefined : this.statusId,
      this.typeId,
      undefined, // fromDate
      undefined, // toDate
      this.onlyMyMeetings,
      this.onlyUpcoming,
      undefined, // locationType
      this.currentPage,
      this.pageSize,
      this.search || undefined,
      'meetingDate desc'
    )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: SingleMeetingResponsePaginatedResult) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.meetings = response.data;
            this.totalCount = response.totalCount;
            this.totalPages = response.totalPages;
          } else {
            this.hasError = true;
            this.errorMessage = response.message || 'INVESTMENT_FUNDS.MEETING.LOAD_ERROR';
          }
        },
        error: (error: any) => {
          this.isLoading = false;
          this.hasError = true;
          this.errorMessage = 'INVESTMENT_FUNDS.MEETING.NETWORK_ERROR';
          console.error('Error loading meetings:', error);
        }
      });
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadMeetings();
    }
  }

  onPreviousPage(): void {
    if (this.canGoPrevious()) {
      this.onPageChange(this.currentPage - 1);
    }
  }

  onNextPage(): void {
    if (this.canGoNext()) {
      this.onPageChange(this.currentPage + 1);
    }
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const halfRange = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfRange);
    let endPage = Math.min(this.totalPages, this.currentPage + halfRange);

    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxPagesToShow) {
      if (startPage === 1) {
        endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);
      } else {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  isEnglish(): boolean {
    return this.translateService.currentLang === LanguageEnum.en;
  }

  /**
   * Handle search input
   */
  onSearch(searchTerm: string): void {
    this.search = searchTerm;
    this.currentPage = 1; // Reset to first page
    this.loadMeetings();
  }

  /**
   * Open advanced search/filter dialog
   */
  openFilter(): void {
    const dialogRef = this.dialog.open(MeetingFilterPopupComponent, {
      width: '390px',
      height: '599px',
      data: {
        statusId: null,
        ...this.filter
      },
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        this.statusId = result.statusId;

        this.loadMeetings();
      }
    });
  }

  /**
   * Navigate to add new meeting
   */
  addMeeting(): void {
    this.router.navigate(['/admin/investment-funds/meetings/create'], {
      queryParams: { fundId: this.fundId }
    });
  }

  /**
   * Navigate to meeting details view
   */
  viewMeetingDetails(meeting: SingleMeetingResponse): void {
    if (!meeting.id) {
      console.error('Meeting ID is required');
      return;
    }

    this.router.navigate(['/admin/investment-funds/meetings/details'], {
      queryParams: {
        id: meeting.id,
        fundId: this.fundId
      }
    });
  }

  /**
   * Navigate to edit meeting
   */
  editMeeting(meeting: SingleMeetingResponse): void {
    if (!meeting.id) {
      console.error('Meeting ID is required');
      return;
    }

    this.router.navigate(['/admin/investment-funds/meetings/edit'], {
      queryParams: {
        id: meeting.id,
        fundId: this.fundId
      }
    });
  }

  /**
   * Cancel meeting with confirmation
   */
  cancelMeeting(meeting: SingleMeetingResponse): void {
    if (!meeting.id) {
      console.error('Meeting ID is required');
      return;
    }

    // Show custom confirmation dialog
    const title = this.translateService.instant('INVESTMENT_FUNDS.MEETING.CANCEL_MEETING_TITLE');
    const message = this.translateService.instant('INVESTMENT_FUNDS.MEETING.CANCEL_CONFIRMATION', {
      subject: meeting.subject
    });

    const dialogRef = this.dialog.open(CancelMeetingDialogComponent, {
      width: '500px',
      data: {
        meetingSubject: meeting.subject,
        title: title,
        message: message
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.performCancelMeeting(meeting);
      }
    });
  }

  /**
   * Perform the actual meeting cancellation
   */
  private performCancelMeeting(meeting: SingleMeetingResponse): void {
    const command = new CancelMeetingCommand();
    command.id = meeting.id;

    this.isLoading = true;
    this.hasError = false;
    this.errorMessage = '';

    this.meetingServiceProxy.cancelMeeting(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isLoading = false;
          if (response.successed) {
            // Show success message using errorModalService
            const message = this.translateService.instant('INVESTMENT_FUNDS.MEETING.CANCEL_SUCCESS', {
              subject: meeting.subject
            });

            this.errorModalService.showSuccess(message);

            // Reload the meetings list to reflect the cancellation
            this.loadMeetings();
          } else {
            this.hasError = true;
            this.errorMessage = response.message || 'INVESTMENT_FUNDS.MEETING.CANCEL_ERROR';
          }
        },
        error: (error: any) => {
          this.isLoading = false;
          this.hasError = true;
          this.errorMessage = 'INVESTMENT_FUNDS.MEETING.CANCEL_NETWORK_ERROR';
          console.error('Error cancelling meeting:', error);
        }
      });
  }

  /**
   * Check if meeting is finished and can show minutes
   */
  canShowMeetingMinutes(meeting: SingleMeetingResponse): boolean {
    return meeting.meetingStatusId == 4;
  }

  /**
   * Open meeting minutes popup
   */
  openMeetingMinutes(meeting: SingleMeetingResponse): void {
    // TODO: Implement meeting minutes popup
    console.log('Opening meeting minutes for meeting:', meeting.id);
    if (!this.canShowMeetingMinutes(meeting)) {
      return;
    }

    const dialogData: MeetingMinutesDialogData = {
      meetingId: meeting.id,
      meetingSubject: meeting.subject || 'Meeting'
    };

    this.dialog.open(MeetingMinutesPopupComponent, {
      width: '800px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.meetingStatus.NotStartedYet:
        return 'pending';
      case this.meetingStatus.InProgress:
        return 'meeting-inProgress';
      case this.meetingStatus.Finished:
        return 'finished';
      case this.meetingStatus.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }
}
