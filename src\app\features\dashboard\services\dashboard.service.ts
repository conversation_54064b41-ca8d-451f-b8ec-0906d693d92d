import { Injectable } from '@angular/core';
import { Observable, catchError, map, shareReplay } from 'rxjs';

// Generated API Client
import {
  DashboardServiceProxy,
  FundAnalyticsResponseBaseResponse
} from '@core/api/api.generated';

// Error Handling
import { ErrorModalService } from '@core/services/error-modal.service';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor(
    private dashboardProxy: DashboardServiceProxy,
    private errorModalService: ErrorModalService
  ) {}

  /**
   * Get dashboard data for a specific fund
   */
  getDashboard(fundId?: number): Observable<FundAnalyticsResponseBaseResponse> {
    return this.dashboardProxy.dashboard(fundId)
      .pipe(
        catchError(error => this.handleError(error)),
        shareReplay(1)
      );
  }



  /**
   * Handle API errors
   */
  private handleError(error: any): Observable<never> {
    console.error('Dashboard Service Error:', error);
    this.errorModalService.showError(error);
    throw error;
  }
}
