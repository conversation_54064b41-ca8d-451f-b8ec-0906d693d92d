@import "../../../../assets/scss/variables";

.input-wrapper {
  position: relative;
  border-radius: $border-radius;
  // &:not(.disabled):not(.readonly) {
  // &::after {
  //   content: "";
  //   position: absolute;
  //   right: 50%;
  //   bottom: 0;
  //   width: 2px;
  //   height: 2px;
  //   border-bottom-left-radius: $border-radius;
  //   border-bottom-right-radius: $border-radius;
  //   background-color: $border-blue;
  //   transition: 0.5s all ease;
  //   -webkit-transition: 0.75s all ease;
  //   -moz-transition: 0.5s all ease;
  //   -ms-transition: 0.5s all ease;
  //   -o-transition: 0.5s all ease;
  //   visibility: hidden;
  // }
  // &:focus-within::after {
  //   width: 99.9%;
  //   right: 0;
  //   visibility: visible;
  // }
  // }
  .form-control {
    /*Basestylesfortheinput*/
    padding: 0.5rem;
    padding-top: 0.3rem;
    border-radius: $border-radius;
    background-color: $white !important;
    border: 1px solid $mid-gray-bg;
    height: 40px;

    &.outline {
      // border: 1px solid $border-light;
      // border-bottom-color: $border-gray;
    }
    /* Filled Darker Appearance */
    &.filled-darker {
      background-color: $neutral-background-hover;
      color: $dark-color;
      border: 1px solid $mid-gray-bg;
    }
    /* Fill Lighter Appearance */
    &.fill-lighter {
      border: 1px solid $mid-gray-bg;
    }
    /* Underline Appearance */
    &.underline {
      border: none;
      border-bottom: 2px solid $black;
      border-radius: 0px;
    }
    &:focus {
      outline: none;
      box-shadow: none;
    }
    &.is-invalid {
      border-color: $danger;
      background: none;
    }
    &:read-only {
      background-color: $inactive-bg !important;
      color:$text-grey;
      cursor: not-allowed;
    }
    &:disabled {
      background-color: $inactive-bg !important;
      color: $text-grey;
      cursor: not-allowed;
    }
  }
}
