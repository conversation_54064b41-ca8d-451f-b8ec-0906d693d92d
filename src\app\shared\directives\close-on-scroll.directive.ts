import { Directive, HostListener, Input } from '@angular/core';
import { NgSelectComponent } from '@ng-select/ng-select';

@Directive({
  selector: '[appCloseOnScroll]',
  standalone: true
})
export class CloseOnScrollDirective {

  @Input('closeOnScroll') ngSelect!: NgSelectComponent;

  @HostListener('window:scroll')
  onScroll() {
    if (this.ngSelect.isOpen) {
      this.ngSelect.close();
    }
  }

}
