# Translation Keys Needed for Meeting Minutes Feature

Add these keys to your translation files (both Arabic and English):

## English (en.json)
```json
{
  "INVESTMENT_FUNDS": {
    "MEETING": {
      "VIEW_MINUTES": "View Minutes",
      "MINUTES_TITLE": "Meeting Minutes",
      "MINUTES_STATUS_DRAFT": "Draft",
      "MINUTES_STATUS_FINAL": "Final",
      "NO_MINUTES_AVAILABLE": "No meeting minutes available for this meeting.",
      "MINUTES_LOAD_ERROR": "Failed to load meeting minutes.",
      "MINUTES_NETWORK_ERROR": "Network error while loading meeting minutes.",
      "INVALID_MEETING_ID": "Invalid meeting ID provided."
    }
  }
}
```

## Arabic (ar.json)
```json
{
  "INVESTMENT_FUNDS": {
    "MEETING": {
      "VIEW_MINUTES": "عرض المحضر",
      "MINUTES_TITLE": "محضر الاجتماع",
      "MINUTES_STATUS_DRAFT": "مسودة",
      "MINUTES_STATUS_FINAL": "نهائي",
      "NO_MINUTES_AVAILABLE": "لا يوجد محضر متاح لهذا الاجتماع.",
      "MINUTES_LOAD_ERROR": "فشل في تحميل محضر الاجتماع.",
      "MINUTES_NETWORK_ERROR": "خطأ في الشبكة أثناء تحميل محضر الاجتماع.",
      "INVALID_MEETING_ID": "معرف الاجتماع غير صحيح."
    }
  }
}
```

## Usage
The meeting minutes icon will automatically appear on meeting cards when:
1. The meeting status is "Finished" (meetingStatusId === 4)
2. The user clicks the icon to view the meeting minutes in a popup
3. The popup loads the minutes content from the API and displays it with proper formatting

## API Endpoint Used
- `GET /api/Meeting/{meetingId}/meetingMinutes` - Retrieves meeting minutes data
