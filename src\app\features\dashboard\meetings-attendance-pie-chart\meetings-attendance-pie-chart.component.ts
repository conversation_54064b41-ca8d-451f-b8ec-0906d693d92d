import { Component, Input, OnChanges, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgxChartsModule, Color, ScaleType } from '@swimlane/ngx-charts';
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';

// API Types
import {
  GetMeetingsByTypeAndFundServiceProxy,
  MeetingsByTypeAndFundResponseDto,
  MeetingsByTypeAndFundResponseDtoBaseResponse
} from '@core/api/api.generated';

export interface MeetingPieChartData {
  name: string;
  value: number;
  extra?: {
    code: string;
    color: string;
    count: number;
  };
}

export interface MeetingType {
  id: number;
  name: string;
  value: string;
}

@Component({
  selector: 'app-meetings-attendance-pie-chart',
  standalone: true,
  imports: [CommonModule, TranslateModule, NgxChartsModule, FormsModule, NgSelectModule],
  templateUrl: './meetings-attendance-pie-chart.component.html',
  styleUrl: './meetings-attendance-pie-chart.component.scss'
})
export class MeetingsAttendancePieChartComponent implements OnChanges {
  @Input() fundId: number | null = null;
  @Input() loading: boolean = false;
  @Output() meetingCount = new EventEmitter<number>();
  @Output() loadingChange = new EventEmitter<boolean>();

  // Chart configuration
  chartData: MeetingPieChartData[] = [];
  meetingData: MeetingsByTypeAndFundResponseDto | null = null;


  @Input() selectedMeetingTypeId: number | null = null;

  // Chart options for pie chart
  view: [number, number] = [170, 170];
  colorScheme: Color = {
    name: 'meeting-attendance',
    selectable: true,
    group: ScaleType.Ordinal,
    domain: ['#28a745', '#dc3545' ] // Green for attendance, Red for absent
  };

  // Pie chart specific options
  showLegend: boolean = false;
  showLabels: boolean = false;
  doughnut: boolean = true;
  explodeSlices: boolean = false;
  animations: boolean = true;
  gradient: boolean = false;
  tooltipDisabled: boolean = true;

  // Meeting attendance color mapping
  private attendanceColors: { [key: string]: string } = {
    'attendance': '#28a745',  // Green
    'absent': '#dc3545' ,      // Red
  };

  constructor(
    private meetingsService: GetMeetingsByTypeAndFundServiceProxy,
    private translateService: TranslateService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['fundId'] || changes['selectedMeetingTypeId']) {
      this.loadMeetingData();
    }
  }

  /**
   * Load meeting data from API
   */
  private loadMeetingData(): void {
    if (!this.fundId) {
      this.chartData = [];
      this.loadingChange.emit(false);
      return;
    }

    this.loadingChange.emit(true); // Emit loading start
    this.meetingsService.meetingsByTypeAndFund(
      this.fundId,
      this.selectedMeetingTypeId ==0?undefined:this.selectedMeetingTypeId ||undefined
    ).subscribe({
      next: (response: MeetingsByTypeAndFundResponseDtoBaseResponse) => {
        if (response.successed && response.data) {
          this.meetingData = response.data;
          this.meetingCount.emit(this.meetingData.totalMeetings);
          this.updateChartData();
        } else {
          this.chartData = [];
        }
        this.loadingChange.emit(false); // Emit loading complete
      },
      error: (error) => {
        console.error('Error loading meeting data:', error);
        this.chartData = [];
        this.loadingChange.emit(false); // Emit loading complete on error
      }
    });
  }

  /**
   * Handle meeting type selection change
   */
  onMeetingTypeChange(meetingTypeId: number): void {
    this.selectedMeetingTypeId = meetingTypeId;
    this.loadMeetingData();
  }

  /**
   * Transform API data to chart format
   */
  private updateChartData(): void {
    if (!this.meetingData) {
      this.chartData = [];
      return;
    }

    const attendanceCount = this.meetingData.attendanceCount || 0;
    const absentCount = this.meetingData.absentCount || 0;

    if (attendanceCount === 0 && absentCount === 0   ) {
      this.chartData = [];
      return;
    }

    this.chartData = [
      {
        name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.ATTENDANCE') ,
        value: attendanceCount,
        extra: {
          code: 'attendance',
          color: this.attendanceColors['attendance'],
          count: attendanceCount
        }
      },
      {
        name: this.translateService.instant('INVESTMENT_FUNDS.MEETING.ABSENT'),
        value: absentCount,
        extra: {
          code: 'absent',
          color: this.attendanceColors['absent'],
          count: absentCount
        }
      }
    ].filter(item => item.value > 0); // Only show items with values > 0

    // Dynamically set color scheme based on actual data
    this.updateColorScheme();
  }

  /**
   * Update color scheme based on actual chart data
   */
  private updateColorScheme(): void {
    const colors: string[] = [];

    this.chartData.forEach(item => {
      if (item.extra?.code === 'attendance') {
        colors.push('#28a745'); // Green for attendance
      } else if (item.extra?.code === 'absent') {
        colors.push('#dc3545'); // Red for absent
      }
    });

    // Update the color scheme domain with the dynamic colors
    this.colorScheme = {
      ...this.colorScheme,
      domain: colors
    };
  }

  /**
   * Get total meetings count
   */
  getTotalMeetings(): number {
    if (!this.meetingData) return 0;
    return (this.meetingData.totalMeetings)
  }

  /**
   * Check if chart has data
   */
  get hasData(): boolean {
    return this.chartData.length > 0 && this.getTotalMeetings() > 0;
  }


}
