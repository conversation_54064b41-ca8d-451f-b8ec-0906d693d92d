@import "../../../../../assets/scss/variables";

.add-attachment-dialog {
  width: 100%;
  max-width: 600px;
  min-height: 400px;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 0;
    border-bottom: 1px solid #e0e6ed;
    margin-bottom: 20px;

    .dialog-title {
      color: $navy-blue;
      font-size: 20px;
      font-weight: 700;
      margin: 0;
    }

    .close-button {
      width: 32px;
      height: 32px;
      border: none;
      background: transparent;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:hover:not(:disabled) {
        background-color: #f5f5f5;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .close-icon {
        font-size: 24px;
        color: #666;
        line-height: 1;
      }
    }
  }

  .dialog-content {
    padding: 0 24px;
    max-height: 500px;
    overflow-y: auto;

    .section-title {
      color: $navy-blue;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      margin-top: 0;
    }

    .upload-section {
      margin-bottom: 24px;
    }

    .uploaded-files-section {
      margin-bottom: 24px;

      .files-list {
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        background: #f9f9f9;
        max-height: 200px;
        overflow-y: auto;

        .file-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          border-bottom: 1px solid #e0e6ed;
          background: white;
          transition: background-color 0.2s ease;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: #f5f5f5;
          }

          .file-info {
            display: flex;
            align-items: center;
            flex: 1;

            .file-icon {
              width: 32px;
              height: 32px;
              margin-right: 12px;
              display: flex;
              align-items: center;
              justify-content: center;

              img {
                width: 24px;
                height: 24px;
              }
            }

            .file-details {
              display: flex;
              flex-direction: column;

              .file-name {
                color: $navy-blue;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 2px;
              }

              .file-size {
                color: #666;
                font-size: 12px;
              }
            }
          }

          .remove-file-btn {
            width: 28px;
            height: 28px;
            border: none;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              background: #ff3742;
              transform: scale(1.1);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
              transform: none;
            }

            .remove-icon {
              font-size: 16px;
              line-height: 1;
            }
          }
        }
      }
    }

    .loading-section {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;

      .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;

        .spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid $navy-blue;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        .loading-text {
          color: $navy-blue;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e0e6ed;
    margin-top: 20px;

    .cancel-btn,
    .save-btn {
      min-width: 120px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .add-attachment-dialog {
    max-width: 100%;
    margin: 0;

    .dialog-header {
      padding: 16px 20px 0;
    }

    .dialog-content {
      padding: 0 20px;
    }

    .dialog-actions {
      padding: 16px 20px;
      flex-direction: column;

      .cancel-btn,
      .save-btn {
        width: 100%;
        margin: 0;
      }
    }
  }
}
