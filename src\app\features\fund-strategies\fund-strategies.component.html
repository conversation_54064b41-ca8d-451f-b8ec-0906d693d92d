<div class="fund-strategies">
  <!-- <app-breadcrumb  [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium" divider=">"></app-breadcrumb> -->
    <app-page-header
      [title]="'FUND_STRATEGIES.FUND_STRATEGIES' | translate"
      [showCreateButton]="tokenService.hasPermission('Strategy.Create')"
      createButtonText="FUND_STRATEGIES.ADD_FUND_STRATEGIES"
      (create)="onCreateNewFund()" [showSearch]="false" [showFilter]="false"></app-page-header>

  <div class="table-container mb-5"  *ngIf="tableDataSource">

    <app-table [columns]="tableColumns"
      [displayedColumns]="displayedColumns"
      [dataSource]="tableDataSource"
      (onClickAction)="onClickAction($event)"
      (switchToggleEvent)="onSwitchToggle($event)"
      (textLinkClick)="onTextLinkClick($event)"
      (toggleAllRows)="toggleAllRows()"
      (toggleRow)="toggleRow($event)"
      [sortingType]="sortingType"
      (sortChanged)="onSortChanged($event)"
      [paginationType]=""
      [showPaginator]="false"
      (pageChange)="onPageChange($event)"
      [totalItems]="totalCount" [alignLastColumnEnd]="true"></app-table>
  </div>

  <!-- <div *ngIf="!tableDataSource">
    <p class="text-danger" translate="FUND_STRATEGIES.SORRY_THERE_IS_NO_RECORDED_DATA_TO_DISPLAY"></p>
  </div>
</div> -->
