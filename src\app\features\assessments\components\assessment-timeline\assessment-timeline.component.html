<div class="timeline-container" [attr.dir]="isEnglish() ? 'ltr' : 'rtl'">
  <div class="timeline-item" *ngFor="let step of steps; let last= last">
    <div class="ms-1">
      <div class="timeline-icon" [ngClass]="getStatusClass(step.status)">
        <img [src]="'assets/images/timeline-icon.svg'" >
      </div>
    </div>
    <div class="w-100">
      <div class="timeline-content">
        <div class="header">
          <strong>{{ step.displayedAction }}</strong>
          <span class="user-role">
           {{ step.displayedUserRole}}:

            <span class="user-name">{{ step.fullName}}</span>
          </span>
        </div>
        <div>
          <div class="timestamp">

            <span class=" date" [ngClass]="{'me-2': isEnglish(), 'ms-2': !isEnglish()}">{{ isEnglish() ? (step?.createdAt | date:'dd-MM-yyyy') : (step?.createdAt | date:'yyyy-MM-dd ') }}</span>
            <span class="time">{{ step?.createdAt | date:'HH:mm:ss' }} </span>
            <span class="hijri">
              {{ step?.originalCreatedAt | dateHijriConverter }}</span>
          </div>
          <div class="decision-state d-flex align-items-center">


            {{ 'ASSESSMENTS.TIMELINE.STATUS_LABEL' | translate }}:
             <p class="status-badge mb-0" [ngClass]="getStatusClass(step.status)">
              {{ step.displayedStatus }}
            </p>
          </div>

        </div>

      </div>
      <div class="reason" *ngIf="step.rejectionReason">

        {{ 'ASSESSMENTS.TIMELINE.REJECTION_REASON' | translate }}:

         {{ step.rejectionReason }}
      </div>
    </div>

    <div *ngIf="!last" class="vertical-line"></div>

  </div>
</div>
