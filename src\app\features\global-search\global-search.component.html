<app-page-header
    [title]="'GLOBAL_SEARCH.TITLE' | translate:{searchTerm}"
    [showSearch]="false"
    [showFilter]="false"
    [showCreateButton]="false">
</app-page-header>
<!-- *ngIf="totalCount > 0 && !allResolutionsHaveNoViewPermission"
  -->
<div class="pagination-section" >
      <!-- Pagination Controls -->
      <div class="pagination-controls">
        <!-- Previous Page Button -->
        <button class="pagination-btn prev-btn"
                [disabled]="!canGoPrevious()"
                (click)="onPreviousPage()"
                [title]="'PAGINATION.PREVIOUS' | translate">
          <!-- <i class="fas fa-angle-left"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-left2.png' : 'assets/images/arrow-right.png'" class="mx-2" alt="previous">

          <span class="btn-text">{{ 'PAGINATION.PREVIOUS' | translate }}</span>
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button *ngFor="let page of getPageNumbers()"
                  class="pagination-btn page-number-btn"
                  [class.active]="page === currentPage"
                  (click)="onPageChange(page)">
            {{page}}
          </button>
        </div>

        <!-- Next Page Button -->
        <button class="pagination-btn next-btn"
                [disabled]="!canGoNext()"
                (click)="onNextPage()"
                [title]="'PAGINATION.NEXT' | translate">
          <span class="btn-text">{{ 'PAGINATION.NEXT' | translate }}</span>
          <!-- <i class="fas fa-angle-right"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-right.png' : 'assets/images/arrow-left2.png'" class="mx-2" alt="next">

        </button>
      </div>
    </div>