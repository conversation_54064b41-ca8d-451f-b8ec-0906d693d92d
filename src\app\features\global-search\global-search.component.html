<app-page-header
    [title]="'GLOBAL_SEARCH.TITLE' | translate:{searchTerm}"
    [showSearch]="false"
    [showFilter]="false"
    [showCreateButton]="false">
</app-page-header>

<div class="search-result-content">
    <!-- Loading State -->
    <!-- <div *ngIf="isLoading" class="loading-state">
        <div class="d-flex flex-column align-items-center">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">{{ 'GLOBAL_SEARCH.LOADING' | translate }}</span>
            </div>
            <p class="text-muted">{{ 'GLOBAL_SEARCH.LOADING' | translate }}</p>
        </div>
    </div> -->

    <!-- Error State -->
    <!-- <div *ngIf="hasError && !isLoading" class="error-state">
        <div class="error-content text-center">
            <img src="assets/images/error.png" width="200" alt="Error" class="error-icon mb-3">
            <h4 class="error-title">{{ 'GLOBAL_SEARCH.ERROR_TITLE' | translate }}</h4>
            <p class="error-message">{{ errorMessage | translate }}</p>
            <button class="btn btn-primary" (click)="performSearch()">
                {{ 'GLOBAL_SEARCH.RETRY' | translate }}
            </button>
        </div>
    </div> -->

    <!-- Search Results List -->
    <div *ngIf="!isLoading && !hasError && searchResults.length > 0" class="search-results-list">
        <div *ngFor="let result of searchResults"
             class="search-result-item d-flex flex-column gap-1 justify-content-between align-items-start mb-3"
             (click)="onResultClick(result)"
             (keydown.enter)="onResultClick(result)"
             (keydown.space)="onResultClick(result)"
             role="button"
             tabindex="0"
             [attr.aria-label]="'GLOBAL_SEARCH.RESULT_ARIA_LABEL' | translate:{title: result.name, category: result.category}">
            <div class="result-category">
                {{ result.localizedCategory }}
            </div>
            <div class="result-title">
                {{ result.name }}
            </div>
        </div>
    </div>

    <!-- No Results Message -->
    <div *ngIf="!isLoading && !hasError && searchResults.length === 0 && searchTerm" class="no-results">
        <div class="no-results-content">
            <img src="assets/images/nodata.png" width="350" alt="No results" class="no-results-icon mb-5">
            <h4 class="no-results-title">{{ 'GLOBAL_SEARCH.NO_RESULTS' | translate }}</h4>
            <p class="no-results-message">{{ 'GLOBAL_SEARCH.NO_RESULTS_MESSAGE' | translate:{searchTerm} }}</p>
        </div>
    </div>
</div>
<!-- Pagination Section -->
<div *ngIf="!isLoading && !hasError && totalCount > 0" class="pagination-section">
      <!-- Pagination Controls -->
      <div class="pagination-controls">
        <!-- Previous Page Button -->
        <button class="pagination-btn prev-btn"
                [disabled]="!canGoPrevious()"
                (click)="onPreviousPage()"
                [title]="'PAGINATION.PREVIOUS' | translate">
          <!-- <i class="fas fa-angle-left"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-left2.png' : 'assets/images/arrow-right.png'" class="mx-2" alt="previous">

          <span class="btn-text">{{ 'PAGINATION.PREVIOUS' | translate }}</span>
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button *ngFor="let page of getPageNumbers()"
                  class="pagination-btn page-number-btn"
                  [class.active]="page === currentPage"
                  (click)="onPageChange(page)">
            {{page}}
          </button>
        </div>

        <!-- Next Page Button -->
        <button class="pagination-btn next-btn"
                [disabled]="!canGoNext()"
                (click)="onNextPage()"
                [title]="'PAGINATION.NEXT' | translate">
          <span class="btn-text">{{ 'PAGINATION.NEXT' | translate }}</span>
          <!-- <i class="fas fa-angle-right"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-right.png' : 'assets/images/arrow-left2.png'" class="mx-2" alt="next">

        </button>
      </div>
    </div>