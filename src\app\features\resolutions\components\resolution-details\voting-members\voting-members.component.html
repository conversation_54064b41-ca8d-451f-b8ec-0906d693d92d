<div class="members-section mt-3">
      <p class="title m-0">
        {{ 'RESOLUTIONS.VOTING.MEMBERS' | translate }}
        <span class="member-count">{{votingMembers?.members?.length}} {{ 'RESOLUTIONS.VOTING.ONE_MEMBER' | translate }}</span>
      </p>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="2" viewBox="0 0 278 2" fill="none">
        <path d="M0 1H278" stroke="url(#paint0_linear_15125_99505)"/>
        <defs>
        <linearGradient id="paint0_linear_15125_99505" x1="0" y1="1" x2="275.318" y2="1" gradientUnits="userSpaceOnUse">
        <stop stop-color="#E0E1E2" stop-opacity="0"/>
        <stop offset="0.5" stop-color="#E0E1E2"/>
        <stop offset="1" stop-color="#E0E1E2" stop-opacity="0.15625"/>
        </linearGradient>
        </defs>
    </svg>
      <!-- Member Cards -->
      <div class="members-list">
        <!-- Member Card 1 - موافق -->
        <div *ngFor="let member of votingMembers?.members" class="member-card">
          <div class="member-info">
            <div class="member-avatar">
              <img src="assets/images/avatar-user.png" alt="Member Avatar" class="avatar-image">
            </div>
            <div class="member-details">
              <h4 (click)="onViewMemberDetails(member)" class="member-name">{{member.fullName}}</h4>
              <p class="member-role">{{member.boardMemberTypeDisplay}}</p>
            </div>
          </div>
          <div class="voting-status">
            <div *ngIf="!member.hasRevoteRequest || (member.hasRevoteRequest && !tokenService.hasPermission('Resolution.ApproveRevoteRequest'))" class="status-badge" [ngClass]="getStatusClass(member.voteResult)">
                <span class="dot"></span>
                {{member.voteResultDisplay}}
            </div>
            <button *ngIf="member.showSendReminder && !member.hasRevoteRequest && resolutionStatus == resolutionStatusEnum.VotingInProgress" 
            (click)="sendReminder(member)" class="action-link">
              {{ 'RESOLUTIONS.VOTING.SEND_REMINDER' | translate }}</button>
            <button *ngIf="member.hasRevoteRequest && resolutionStatus == resolutionStatusEnum.VotingInProgress && tokenService.hasPermission('Resolution.ApproveRevoteRequest')" 
              (click)="onViewMemberDetails(member)" class="action-link">
            {{ 'RESOLUTIONS.VOTING.REVOTE_REQUEST' | translate }}</button>
          </div>
        </div>
      </div>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="2" viewBox="0 0 278 2" fill="none">
        <path d="M0 1H278" stroke="url(#paint0_linear_15125_99505)"/>
        <defs>
        <linearGradient id="paint0_linear_15125_99505" x1="0" y1="1" x2="275.318" y2="1" gradientUnits="userSpaceOnUse">
        <stop stop-color="#E0E1E2" stop-opacity="0"/>
        <stop offset="0.5" stop-color="#E0E1E2"/>
        <stop offset="1" stop-color="#E0E1E2" stop-opacity="0.15625"/>
        </linearGradient>
        </defs>
        </svg>
      <!-- Voting Summary -->
      <div class="voting-summary">
        <div class="summary-card d-flex flex-column approved-summary">
          <div class="summary-label">{{"RESOLUTIONS.VOTING.APPROVED" | translate}}</div>
          <div class="summary-count">{{votingMembers?.acceptedMembersCount || 0}}</div>
        </div>
        <div class="summary-card d-flex flex-column rejected-summary">
          <div class="summary-label">{{"RESOLUTIONS.VOTING.REJECTED" | translate}}</div>
          <div class="summary-count">{{votingMembers?.rejectedMembersCount || 0}}</div>
        </div>
        <div class="summary-card d-flex flex-column not-voted-summary">
          <div class="summary-label">{{"RESOLUTIONS.VOTING.NOT_VOTED" | translate}}</div>
          <div class="summary-count">{{votingMembers?.pendingMembersCount || 0}}</div>
        </div>
      </div>
    </div>