# Assessment API Integration Guide

## Overview

This document outlines the integration of NSwag-generated API endpoints for Assessment operations (delete, reject, approve) following JadwaUI architectural patterns.

## API Endpoints Integrated

### 1. Approve Assessment
- **Endpoint**: `POST /api/Assessment/approve`
- **Method**: `AssessmentServiceProxy.approve(id: number)`
- **Response**: `StringBaseResponse`

### 2. Reject Assessment
- **Endpoint**: `POST /api/Assessment/reject`
- **Method**: `AssessmentServiceProxy.reject(command: RejectAssessmentCommand)`
- **Response**: `StringBaseResponse`
- **Command Structure**:
  ```typescript
  RejectAssessmentCommand {
    id: number;
    rejectionReason: string | undefined;
    comments: string | undefined;
  }
  ```

### 3. Delete Assessment
- **Endpoint**: `DELETE /api/Assessment/Delete`
- **Method**: `AssessmentServiceProxy.delete(id: number)`
- **Response**: `StringBaseResponse`

## Implementation Patterns

### 1. Direct NSwag Proxy Usage
Following JadwaUI architectural guidelines, components inject and use NSwag-generated service proxies directly:

```typescript
constructor(
  private assessmentServiceProxy: AssessmentServiceProxy,
  // ... other dependencies
) {}

// Direct API call
this.assessmentServiceProxy.approve(assessmentId)
  .pipe(takeUntil(this.destroy$))
  .subscribe({
    next: (response: StringBaseResponse) => {
      if (response.successed) {
        // Handle success
      } else {
        // Handle API error
      }
    },
    error: (error) => {
      // Handle HTTP error
    }
  });
```

### 2. Form Validation Patterns
All submit buttons remain enabled with validation on click:

```typescript
onSubmit(): void {
  this.isValidationFire = true;
  
  if (this.formGroup.valid && !this.isFormSubmitted) {
    this.isFormSubmitted = true;
    this.callApi();
  }
}
```

### 3. Error Handling
Consistent error handling using `ErrorModalService`:

```typescript
.subscribe({
  next: (response) => {
    if (response.successed) {
      this.errorModalService.showSuccess(message);
    } else {
      this.errorModalService.showError(response.message || fallbackMessage);
    }
  },
  error: (error) => {
    console.error('Operation failed:', error);
    this.errorModalService.showError(fallbackMessage);
  }
});
```

## Components Updated

### 1. Assessment Details Component
**Location**: `src/app/features/assessments/components/assessment-details/assessment-details.component.ts`

**New Features**:
- Approve button with API integration
- Reject button with dialog integration
- Delete button with confirmation dialog
- Permission-based button visibility

**Permission Checks**:
```typescript
shouldShowApproveButton(): boolean {
  return this.canApprove &&
         this.assessment !== null &&
         this.tokenService.hasPermission('Assessment.Approve');
}

shouldShowRejectButton(): boolean {
  return this.canReject &&
         this.assessment !== null &&
         this.tokenService.hasPermission('Assessment.Reject');
}

shouldShowDeleteButton(): boolean {
  return this.assessment !== null &&
         this.tokenService.hasPermission('Assessment.Delete');
}
```

### 2. Assessment List Component
**Location**: `src/app/features/assessments/components/assessment-list/assessment-list.component.ts`

**New Features**:
- Delete functionality for assessment cards
- SweetAlert2 confirmation dialogs
- Automatic list refresh after operations

### 3. Reject Assessment Dialog
**Location**: `src/app/features/assessments/components/reject-assessment-dialog/`

**New Component Features**:
- Standalone dialog component
- Form validation using app-form-builder
- Rejection reason and comments fields
- Proper dialog data interface

## Dialog Integration Pattern

### Dialog Data Interface
```typescript
export interface RejectAssessmentDialogData {
  assessmentId: number;
  assessmentTitle: string;
}
```

### Dialog Usage
```typescript
const dialogData: RejectAssessmentDialogData = {
  assessmentId: this.currentAssessmentId,
  assessmentTitle: this.assessment.title || ''
};

const dialogRef = this.dialog.open(RejectAssessmentDialogComponent, {
  width: '600px',
  data: dialogData,
  disableClose: true
});

dialogRef.afterClosed().subscribe((command: RejectAssessmentCommand) => {
  if (command) {
    this.executeRejectAssessment(command);
  }
});
```

## Translation Keys Added

### English (`src/assets/i18n/en.json`)
```json
{
  "ASSESSMENTS": {
    "DELETE": "Delete",
    "DELETE_SUCCESS": "Assessment deleted successfully",
    "DELETE_ERROR": "Failed to delete assessment",
    "DELETE_CONFIRM": "Are you sure you want to delete the assessment '{{title}}'? This action cannot be undone.",
    "REJECT_ASSESSMENT_TITLE": "Reject Assessment: {{title}}",
    "REJECT_CONFIRMATION_MESSAGE": "Please provide a reason for rejecting this assessment. This information will be recorded for audit purposes.",
    "REJECTION_REASON": "Rejection Reason",
    "REJECTION_REASON_PLACEHOLDER": "Enter the reason for rejection...",
    "ADDITIONAL_COMMENTS": "Additional Comments",
    "ADDITIONAL_COMMENTS_PLACEHOLDER": "Enter any additional comments (optional)..."
  }
}
```

### Arabic (`src/assets/i18n/ar.json`)
```json
{
  "ASSESSMENTS": {
    "DELETE": "حذف",
    "DELETE_SUCCESS": "تم حذف التقييم بنجاح",
    "DELETE_ERROR": "فشل في حذف التقييم",
    "DELETE_CONFIRM": "هل أنت متأكد من حذف التقييم '{{title}}'؟ لا يمكن التراجع عن هذا الإجراء.",
    "REJECT_ASSESSMENT_TITLE": "رفض التقييم: {{title}}",
    "REJECT_CONFIRMATION_MESSAGE": "يرجى تقديم سبب رفض هذا التقييم. سيتم تسجيل هذه المعلومات لأغراض المراجعة.",
    "REJECTION_REASON": "سبب الرفض",
    "REJECTION_REASON_PLACEHOLDER": "أدخل سبب الرفض...",
    "ADDITIONAL_COMMENTS": "تعليقات إضافية",
    "ADDITIONAL_COMMENTS_PLACEHOLDER": "أدخل أي تعليقات إضافية (اختياري)..."
  }
}
```

## Best Practices Followed

1. **Direct API Integration**: No custom service wrappers, direct NSwag proxy usage
2. **Consistent Error Handling**: Using ErrorModalService for all operations
3. **Permission-Based Access**: Proper role and permission checking
4. **Form Validation**: Enabled buttons with validation on click
5. **Confirmation Dialogs**: SweetAlert2 for delete confirmations
6. **Reactive Forms**: Using app-form-builder for consistent form handling
7. **Internationalization**: Full Arabic/English translation support
8. **Loading States**: Proper loading indicators during API calls

## Testing Checklist

- [ ] Approve functionality works with proper permissions
- [ ] Reject dialog opens and submits correctly
- [ ] Delete confirmation works in both list and details views
- [ ] Permission checks prevent unauthorized actions
- [ ] Error messages display correctly in both languages
- [ ] Success messages show after operations
- [ ] Navigation works correctly after operations
- [ ] Loading states display during API calls

## Future Enhancements

1. Add bulk operations for multiple assessments
2. Implement assessment history tracking
3. Add email notifications for approve/reject actions
4. Enhance audit trail logging
