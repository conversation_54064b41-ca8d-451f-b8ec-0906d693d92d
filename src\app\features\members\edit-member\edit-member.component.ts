import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import {
  BoardMembersServiceProxy,
  BoardMemberResponse,
  BoardMemberType,
  EditBoardMemberCommand
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { AlertComponent } from "@shared/components/alert/alert.component";
import { AlertType } from '@core/enums/alert-type';

export interface EditMemberDialogData {
  member: BoardMemberResponse;
  fundId: number;
  hasChairman: boolean;
  independentMembersCount: number;
}

@Component({
  selector: 'app-edit-member',
  standalone: true,
  imports: [CommonModule, TranslateModule, FormBuilderComponent, FormsModule, AlertComponent],
  templateUrl: './edit-member.component.html',
  styleUrl: './edit-member.component.scss'
})
export class EditMemberComponent implements OnInit {
  formGroup!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  isFormSubmitted = false;
  isValidationFire = false;
  AlertType = AlertType

  member: BoardMemberResponse;
  fundId: number;
  hasChairman: boolean;
  independentMembersCount: number;

  formControls: IControlOption[] = [
    {
      type: InputType.Radio,
      formControlName: 'memberType',
      id: 'memberType',
      name: 'memberType',
      label: 'INVESTMENT_FUNDS.MEMBERS.TYPE_MEMBER',
      isRequired: true,
      class: 'col-md-12',
      options: [
        { name: 'INVESTMENT_FUNDS.MEMBERS.INDEPENDENT', id: BoardMemberType._1 },
        { name: 'INVESTMENT_FUNDS.MEMBERS.DEPENDENT', id: BoardMemberType._2 }
      ],
    },
    {
      type: InputType.Checkbox,
      formControlName: 'isChairman',
      id: 'isChairman',
      name: 'isChairman',
      label: '',
      isRequired: false,
      class: 'col-md-12',
      options: [{ name: 'INVESTMENT_FUNDS.MEMBERS.IS_CHAIRMAN', id: 1 }],
    },
  ];

  constructor(
    private formBuilder: FormBuilder,
    public dialogRef: MatDialogRef<EditMemberComponent>,
    @Inject(MAT_DIALOG_DATA) public data: EditMemberDialogData,
    private boardMembersService: BoardMembersServiceProxy,
    private errorModalService: ErrorModalService
  ) {
    this.member = data.member;
    this.fundId = data.fundId;
    this.hasChairman = data.hasChairman;
    this.independentMembersCount = data.independentMembersCount;
  }

  ngOnInit() {
    this.initForm();
    this.updateFormControlsBasedOnBusinessRules();
  }

  onClose() {
    this.dialogRef.close();
  }

  dropdownChanged(_data: any) {
    // Handle dropdown changes if needed
  }

  onSubmit(_event: any) {
    this.isFormSubmitted = true;
    this.isSubmitting = true;

    if (this.formGroup.valid) {
      // Validate business rules before submission
      if (!this.validateBusinessRules()) {
        this.isSubmitting = false;
        return;
      }

      this.updateBoardMember();
    } else {
      this.isSubmitting = false;
      this.isValidationFire = true;
    }
  }

  private updateBoardMember() {
    this.isLoading = true;
    const formValue = this.formGroup.value;

    const updateCommand = this.buildUpdateCommand(formValue);

    // TODO: Replace with actual API call when backend endpoints are available
    this.boardMembersService.editBoardMember(updateCommand).subscribe({
      next: (response) => {
        this.handleUpdateSuccess(response);
      },
      error: (error) => {
        this.handleUpdateError(error);
      }
    });
  }

  private buildUpdateCommand(formValue: any) :EditBoardMemberCommand{
    return {
      id: this.member.id,
      fundId: this.fundId,
      userId: this.member.userId,
      memberType: formValue.memberType || this.member.memberType,
      isChairman: formValue.isChairman,
      isActive: this.member.isActive
    } as EditBoardMemberCommand;
  }

  private handleUpdateSuccess(response: any) {
    this.isLoading = false;
    this.isSubmitting = false;

    if (response.successed) {
      this.errorModalService.showSuccess(
        'INVESTMENT_FUNDS.MEMBERS.EDIT_SUCCESS'
      );
      this.dialogRef.close(true); // Return true to indicate success
    } else {
      this.errorModalService.showError(
        response.message || 'INVESTMENT_FUNDS.MEMBERS.ERROR_UNKNOWN'
      );
    }
  }

  private handleUpdateError(error: any) {
    this.isLoading = false;
    this.isSubmitting = false;
    this.isFormSubmitted = false;
    console.error('Error updating board member:', error);

    // Handle specific error cases
    if (error.status === 400) {
      this.errorModalService.showError(
        error.error?.message || 'INVESTMENT_FUNDS.MEMBERS.ERROR_VALIDATION'
      );
    } else {
      this.errorModalService.showError(
        'INVESTMENT_FUNDS.MEMBERS.ERROR_UNKNOWN'
      );
    }
  }

  private validateBusinessRules(): boolean {
    const formValue = this.formGroup.value;

    // Validation 1: Check minimum independent members rule
    if (!this.validateMinimumIndependentMembers(formValue)) {
      return false;
    }

    // Validation 2: Check chairman conflict rule
    if (!this.validateChairmanConflict(formValue)) {
      return false;
    }

    return true;
  }

  private validateMinimumIndependentMembers(formValue: any): boolean {
    // Check if changing from Independent to Dependent when there are only 2 independent members
    const isChangingToDependent = this.member.memberType === BoardMemberType._1 &&
                                  formValue.memberType === BoardMemberType._2;

    if (isChangingToDependent && this.independentMembersCount <= 2) {
      this.errorModalService.showError('INVESTMENT_FUNDS.MEMBERS.ERROR_MIN_INDEPENDENT_MEMBERS');
      return false;
    }

    return true;
  }

  private validateChairmanConflict(formValue: any): boolean {
    // Check chairman conflict - if trying to make this member chairman when another chairman exists
    const isTryingToBecomeChairman = formValue.isChairman && !this.member.isChairman;

    if (isTryingToBecomeChairman && this.hasChairman) {
      this.errorModalService.showError('INVESTMENT_FUNDS.MEMBERS.ERROR_CHAIRMAN_EXISTS');
      return false;
    }

    return true;
  }

  private hasChanges(): boolean {
    const formValue = this.formGroup.value;

    return this.member.memberType !== formValue.memberType ||
           this.member.isChairman !== formValue.isChairman;
  }

  private initForm() {
    const formGroup: any = {};

    this.formControls.forEach(control => {
      const validators = [];
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      formGroup[control.formControlName] = ['', validators];
    });

    this.formGroup = this.formBuilder.group(formGroup);

    // Pre-populate form with existing member data
    this.formGroup.patchValue({
      memberType: this.member.memberType,
      isChairman: this.member.isChairman
    });

    // Disable chairman checkbox if there's already a chairman and this member is not the chairman
    if (this.hasChairman && !this.member.isChairman) {
      this.formGroup.get('isChairman')?.disable();
    }
  }

  private updateFormControlsBasedOnBusinessRules(): void {
    // Disable member type radio buttons if there are exactly 2 independent members
    // and this member is independent (to prevent changing to dependent)
    if (this.independentMembersCount <= 2 && this.member.memberType === BoardMemberType._1) {
      this.formGroup.get('memberType')?.disable();
    }

    // Disable chairman checkbox if there's already a chairman and this member is not the chairman
    if (this.hasChairman && !this.member.isChairman) {
      this.formGroup.get('isChairman')?.disable();
    }
  }

  get showMemberTypeWarning(): boolean {
    return this.independentMembersCount <= 2 && this.member.memberType === BoardMemberType._1;
  }

  get isMemberTypeDisabled(): boolean {
    return this.independentMembersCount <= 2 && this.member.memberType === BoardMemberType._1;
  }

  get isChairmanDisabled(): boolean {
    return this.hasChairman && !this.member.isChairman;
  }
}
