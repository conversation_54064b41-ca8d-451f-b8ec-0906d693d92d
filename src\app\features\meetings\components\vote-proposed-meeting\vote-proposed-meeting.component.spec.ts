import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { VoteProposedMeetingComponent } from './vote-proposed-meeting.component';
import { MeetingsProposalServiceProxy } from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';

describe('VoteProposedMeetingComponent', () => {
  let component: VoteProposedMeetingComponent;
  let fixture: ComponentFixture<VoteProposedMeetingComponent>;
  let mockMeetingsProposalServiceProxy: jasmine.SpyObj<MeetingsProposalServiceProxy>;
  let mockErrorModalService: jasmine.SpyObj<ErrorModalService>;

  beforeEach(async () => {
    const meetingsServiceSpy = jasmine.createSpyObj('MeetingsProposalServiceProxy', ['getProposalsForVoting', 'voteOnMeetingProposal']);
    const errorModalServiceSpy = jasmine.createSpyObj('ErrorModalService', ['showError', 'showSuccess']);

    await TestBed.configureTestingModule({
      imports: [
        VoteProposedMeetingComponent,
        RouterTestingModule,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        { provide: MeetingsProposalServiceProxy, useValue: meetingsServiceSpy },
        { provide: ErrorModalService, useValue: errorModalServiceSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(VoteProposedMeetingComponent);
    component = fixture.componentInstance;
    mockMeetingsProposalServiceProxy = TestBed.inject(MeetingsProposalServiceProxy) as jasmine.SpyObj<MeetingsProposalServiceProxy>;
    mockErrorModalService = TestBed.inject(ErrorModalService) as jasmine.SpyObj<ErrorModalService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with required validation', () => {
    expect(component.voteForm).toBeDefined();
    expect(component.voteForm.get('selectedTimeSlot')?.hasError('required')).toBeTruthy();
  });

  it('should load proposal data on init with valid proposalId', () => {
    const mockResponse = {
      successed: true,
      data: {
        id: 1,
        subject: 'Test Meeting',
        description: 'Test Description',
        meetingProposalDates: [
          { id: 1, proposedDate: '2025-08-16T00:00:00', proposedTime: '10:00:00', voteCount: 0 }
        ],
        meetingProposalVotesResult: [
          { id: 1, proposedDate: '2025-08-16T00:00:00', proposedTime: '10:00:00', voteCount: 0, voterNames: [], isWinner: false }
        ],
        canVote: true,
        currentUserVoted: false,
        votingSummary: {
          totalVotes: 2,
          totalEligibleVoters: 5,
          participationDisplay: '2/5',
          participationRatePercent: 40
        }
      }
    };

    mockMeetingsProposalServiceProxy.getProposalsForVoting.and.returnValue(of(mockResponse as any));

    component.proposalId = 1;
    component.loadProposalForVoting();

    expect(mockMeetingsProposalServiceProxy.getProposalsForVoting).toHaveBeenCalledWith(1);
    expect(component.proposalData).toEqual(mockResponse.data);
    expect(component.timeSlots.length).toBe(1);
    expect(component.votingResults.length).toBe(1);
  });

  it('should validate form before submission', () => {
    component.confirmVote();

    expect(component.voteForm.get('selectedTimeSlot')?.touched).toBeTruthy();
    expect(mockMeetingsProposalServiceProxy.voteOnMeetingProposal).not.toHaveBeenCalled();
  });
});
