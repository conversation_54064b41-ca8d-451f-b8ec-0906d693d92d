import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

// API Types - Define based on expected notification data structure
export interface NotificationItem {
  id: string;
  fundName: string;
  requestType: string;
  status: 'pending' | 'approved' | 'rejected' | 'in-progress' | 'completed';
  statusDisplay: string;
  timestamp: Date;
  isRead: boolean;
}

export interface MeetingItem {
  id: string;
  title: string;
  fundName: string;
  meetingDate: Date;
  status: 'upcoming' | 'in-progress' | 'completed' | 'cancelled';
  statusDisplay: string;
}

@Component({
  selector: 'app-dashboard-notifications',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './dashboard-notifications.component.html',
  styleUrl: './dashboard-notifications.component.scss'
})
export class DashboardNotificationsComponent implements OnChanges {
  @Input() notifications: NotificationItem[] = [];
  @Input() meetings: MeetingItem[] = [];
  @Input() loading: boolean = false;

  // Component state
  activeTab: 'requests' | 'meetings' = 'requests';
  displayItems: (NotificationItem | MeetingItem)[] = [];

  // Status color mapping
  private statusColors: { [key: string]: string } = {
    'pending': '#ffc107',      // Yellow
    'approved': '#28a745',     // Green
    'rejected': '#dc3545',     // Red
    'in-progress': '#17a2b8',  // Blue
    'completed': '#28a745',    // Green
    'upcoming': '#007bff',     // Primary blue
    'cancelled': '#6c757d'     // Gray
  };

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['notifications'] || changes['meetings']) {
      this.updateDisplayItems();
    }
  }

  /**
   * Switch between tabs
   */
  switchTab(tab: 'requests' | 'meetings'): void {
    this.activeTab = tab;
    this.updateDisplayItems();
  }

  /**
   * Update display items based on active tab
   */
  private updateDisplayItems(): void {
    if (this.activeTab === 'requests') {
      this.displayItems = this.notifications;
    } else {
      this.displayItems = this.meetings;
    }
  }

  /**
   * Get status color for item
   */
  getStatusColor(status: string): string {
    return this.statusColors[status] || '#6c757d';
  }

  /**
   * Handle item click
   */
  onItemClick(item: NotificationItem | MeetingItem): void {
    console.log('Item clicked:', item);
    // Implement navigation or action based on item type

    if (this.isNotificationItem(item)) {
      // Handle notification click
      this.handleNotificationClick(item);
    } else {
      // Handle meeting click
      this.handleMeetingClick(item);
    }
  }

  /**
   * Type guard to check if item is NotificationItem
   */
  isNotificationItem(item: NotificationItem | MeetingItem): item is NotificationItem {
    return 'requestType' in item;
  }

  /**
   * Handle notification item click
   */
  private handleNotificationClick(notification: NotificationItem): void {
    // Mark as read
    notification.isRead = true;

    // Navigate to relevant page or show details
    // This could emit an event or use router navigation
    console.log('Notification clicked:', notification.fundName, notification.requestType);
  }

  /**
   * Handle meeting item click
   */
  private handleMeetingClick(meeting: MeetingItem): void {
    // Navigate to meeting details or calendar
    console.log('Meeting clicked:', meeting.title);
  }

  /**
   * Get unread count for requests
   */
  get unreadRequestsCount(): number {
    return this.notifications.filter(item => !item.isRead).length;
  }

  /**
   * Get upcoming meetings count
   */
  get upcomingMeetingsCount(): number {
    return this.meetings.filter(item => item.status === 'upcoming').length;
  }

  /**
   * Check if there are any items to display
   */
  get hasItems(): boolean {
    return this.displayItems.length > 0;
  }

  /**
   * Get display text for empty state
   */
  get emptyStateText(): string {
    return this.activeTab === 'requests' ? 'لا توجد طلبات' : 'لا توجد اجتماعات';
  }

  /**
   * Get display text for empty state description
   */
  get emptyStateDescription(): string {
    return this.activeTab === 'requests'
      ? 'لم يتم العثور على أي طلبات حالياً'
      : 'لم يتم جدولة أي اجتماعات';
  }

  /**
   * Mark all notifications as read
   */
  markAllAsRead(): void {
    this.notifications.forEach(notification => {
      notification.isRead = true;
    });
  }

  /**
   * View all items (navigate to full list)
   */
  viewAll(): void {
    console.log('View all clicked for tab:', this.activeTab);
    // Implement navigation to full notifications/meetings page
    // Could emit an event or use router navigation
  }

  /**
   * TrackBy function for items
   */
  trackByItemId(_index: number, item: NotificationItem | MeetingItem): string {
    return item.id;
  }
}
