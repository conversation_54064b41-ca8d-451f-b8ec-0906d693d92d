import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';

export interface MeetingItemData {
  isEdit: boolean;
  agendaItem?: any; // For editing existing item
  existingItems: any[]; // For validation
}

@Component({
  selector: 'app-meeting-agenda-popup',
  standalone: true,
  imports: [CommonModule, TranslateModule, FormBuilderComponent, ReactiveFormsModule],
  templateUrl: './meeting-agenda-popup.component.html',
  styleUrl: './meeting-agenda-popup.component.scss'
})
export class MeetingAgendaPopupComponent {
  isLoading: any;
  isSubmitting: any;
  isEdit: boolean = false;
  currentItem: any;
  existingItems: any = [];
  formGroup!: FormGroup;
  formControls: IControlOption[] = [
    {
      type: InputType.Mixed,
      formControlName: 'subject',
      id: 'subject',
      name: 'subject',
      label: 'INVESTMENT_FUNDS.MEETING.AGENDA_ITEM',
      placeholder: 'INVESTMENT_FUNDS.MEETING.AGENDA_ITEM_PLACEHOLDER',
      isRequired: true,
      showOptional: false,
      class: 'col-md-12 pt-0',
    },
    {
      type: InputType.Textarea,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'INVESTMENT_FUNDS.MEETING.AGENDA_ITEM_DESCRIPTION',
      placeholder: 'INVESTMENT_FUNDS.MEETING.AGENDA_ITEM_DESCRIPTION_PLACEHOLDER',
      isRequired: false,
      showOptional: true,
      class: 'col-md-12',
    }
  ];
  isValidationFire: boolean | undefined;

  constructor(
    private formBuilder: FormBuilder,
    private translateService: TranslateService,
    public dialogRef: MatDialogRef<MeetingAgendaPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MeetingItemData
  ) {
    this.isEdit = data?.isEdit || false;
    this.currentItem = data?.agendaItem || null;
    this.existingItems = data?.existingItems || [];
  }
  
  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    // Initialize form with existing data if editing
    const initialSubject = this.isEdit && this.currentItem ? this.currentItem.itemSubject || '' : '';
    const initialDescription = this.isEdit && this.currentItem ? this.currentItem.itemDescription || '' : '';

    this.formGroup = this.formBuilder.group({
      subject: [initialSubject, [Validators.required]],
      description: [initialDescription],
    });
  }

  get itemTitle(): string {
    if (this.isEdit && this.currentItem) {
      return this.translateService.instant('INVESTMENT_FUNDS.MEETING.EDIT_AGENDA_ITEM');
    } else {
      const nextItemNumber = this.existingItems.length + 1;
      return this.translateService.instant('INVESTMENT_FUNDS.MEETING.ITEM') + ' ' + nextItemNumber;
    }
  }

  private isFormValid(): boolean {
    // Check if subject is provided (required field)
    const subject = this.formGroup.get('subject')?.value;
    if (!subject || subject.trim().length === 0) {
      return false;
    }

    // Check for duplicate subjects (if not editing the same item)
    const existingSubjects = this.existingItems.map((item: any) =>
      item.itemSubject?.toLowerCase().trim()
    ).filter(Boolean);

    if (existingSubjects.includes(subject.toLowerCase().trim())) {
      return false;
    }

    return true;
  }

  onSubmit(): void {
      console.log('Form submitted');
       this.isSubmitting = true;
      this.isValidationFire = true;
      this.formGroup.markAllAsTouched();

      if (!this.isFormValid()) {
        this.isSubmitting = false;
        return;
      }

      const formValue = this.formGroup.value;

      // Return the form data to the parent component
      this.dialogRef.close({
        subject: formValue.subject?.trim(),
        description: formValue.description?.trim() || ''
      });
    }
  onCancel(): void {
    this.dialogRef.close();
  }
}
