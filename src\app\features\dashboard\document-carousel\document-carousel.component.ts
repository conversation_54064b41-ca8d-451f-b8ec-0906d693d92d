import { Component, Input, OnChang<PERSON>, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

// API Types
import { FundDocumentDto } from '@core/api/api.generated';

export interface DocumentDisplayItem {
  id: string;
  fileName: string;
  fileType: string;
  contentType: string;
  fileSize?: number;
  uploadDate?: Date;
  downloadUrl?: string;
  previewUrl?: string;
  icon: string;
  iconColor: string;
}

@Component({
  selector: 'app-document-carousel',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './document-carousel.component.html',
  styleUrl: './document-carousel.component.scss'
})
export class DocumentCarouselComponent implements OnChanges {
  @Input() documents: FundDocumentDto[] = [];
  @Input() loading: boolean = false;
  @Input() itemsPerView: number = 4;

  @ViewChild('carouselContainer', { static: false }) carouselContainer!: ElementRef;

  // Component state
  displayDocuments: DocumentDisplayItem[] = [];
  currentIndex: number = 0;
  currentSlideIndex: number = 0;
  canScrollLeft: boolean = false;
  canScrollRight: boolean = false;

  // File type to icon mapping
  private fileTypeIcons: { [key: string]: { icon: string; color: string } } = {
    // PDF Documents
    'application/pdf': { icon: 'fas fa-file-pdf', color: '#dc3545' },
    'pdf': { icon: 'fas fa-file-pdf', color: '#dc3545' },

    // Microsoft Word
    'application/msword': { icon: 'fas fa-file-word', color: '#2b579a' },
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { icon: 'fas fa-file-word', color: '#2b579a' },
    'doc': { icon: 'fas fa-file-word', color: '#2b579a' },
    'docx': { icon: 'fas fa-file-word', color: '#2b579a' },

    // Microsoft Excel
    'application/vnd.ms-excel': { icon: 'fas fa-file-excel', color: '#217346' },
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { icon: 'fas fa-file-excel', color: '#217346' },
    'xls': { icon: 'fas fa-file-excel', color: '#217346' },
    'xlsx': { icon: 'fas fa-file-excel', color: '#217346' },

    // Microsoft PowerPoint
    'application/vnd.ms-powerpoint': { icon: 'fas fa-file-powerpoint', color: '#d24726' },
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': { icon: 'fas fa-file-powerpoint', color: '#d24726' },
    'ppt': { icon: 'fas fa-file-powerpoint', color: '#d24726' },
    'pptx': { icon: 'fas fa-file-powerpoint', color: '#d24726' },

    // Images
    'image/jpeg': { icon: 'fas fa-file-image', color: '#fd7e14' },
    'image/jpg': { icon: 'fas fa-file-image', color: '#fd7e14' },
    'image/png': { icon: 'fas fa-file-image', color: '#fd7e14' },
    'image/gif': { icon: 'fas fa-file-image', color: '#fd7e14' },
    'image/bmp': { icon: 'fas fa-file-image', color: '#fd7e14' },
    'image/svg+xml': { icon: 'fas fa-file-image', color: '#fd7e14' },

    // Text Files
    'text/plain': { icon: 'fas fa-file-alt', color: '#6c757d' },
    'text/csv': { icon: 'fas fa-file-csv', color: '#28a745' },
    'txt': { icon: 'fas fa-file-alt', color: '#6c757d' },
    'csv': { icon: 'fas fa-file-csv', color: '#28a745' },

    // Archive Files
    'application/zip': { icon: 'fas fa-file-archive', color: '#ffc107' },
    'application/x-rar-compressed': { icon: 'fas fa-file-archive', color: '#ffc107' },
    'application/x-7z-compressed': { icon: 'fas fa-file-archive', color: '#ffc107' },
    'zip': { icon: 'fas fa-file-archive', color: '#ffc107' },
    'rar': { icon: 'fas fa-file-archive', color: '#ffc107' },
    '7z': { icon: 'fas fa-file-archive', color: '#ffc107' },

    // Video Files
    'video/mp4': { icon: 'fas fa-file-video', color: '#e83e8c' },
    'video/avi': { icon: 'fas fa-file-video', color: '#e83e8c' },
    'video/mov': { icon: 'fas fa-file-video', color: '#e83e8c' },

    // Audio Files
    'audio/mp3': { icon: 'fas fa-file-audio', color: '#20c997' },
    'audio/wav': { icon: 'fas fa-file-audio', color: '#20c997' },
    'audio/ogg': { icon: 'fas fa-file-audio', color: '#20c997' },

    // Default
    'default': { icon: 'fas fa-file', color: '#6c757d' }
  };

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['documents']) {
      this.updateDisplayDocuments();
    }
  }

  /**
   * Transform API documents to display format
   */
  private updateDisplayDocuments(): void {
    if (!this.documents || this.documents.length === 0) {
      this.displayDocuments = [];
      this.updateScrollButtons();
      return;
    }

    this.displayDocuments = this.documents.map((doc, index) => {
      const fileInfo = this.getFileTypeInfo(doc);

      // Clean up document name for better display
      const cleanFileName = this.cleanDocumentName(doc.name || 'وثيقة غير محددة');

      return {
        id: index.toString(), // Use index as ID since FundDocumentDto doesn't have id
        fileName: cleanFileName,
        fileType: fileInfo.extension,
        contentType: fileInfo.contentType,
        fileSize: doc.fileSize,
        uploadDate: undefined, // FundDocumentDto doesn't have uploadDate
        downloadUrl: doc.fileUrl,
        previewUrl: doc.fileUrl, // Use fileUrl for both download and preview
        icon: fileInfo.icon,
        iconColor: fileInfo.color
      };
    });

    this.currentIndex = 0;
    this.updateScrollButtons();
  }

  /**
   * Clean document name for better display
   */
  private cleanDocumentName(fileName: string): string {
    if (!fileName) return 'وثيقة غير محددة';

    // Remove file extension for display
    let cleanName = fileName.replace(/\.[^/.]+$/, '');

    // Replace underscores and hyphens with spaces
    cleanName = cleanName.replace(/[_-]/g, ' ');

    // Capitalize first letter of each word for English text
    cleanName = cleanName.replace(/\b\w/g, l => l.toUpperCase());

    // Handle common document name patterns
    if (cleanName.toLowerCase().includes('terms') && cleanName.toLowerCase().includes('conditions')) {
      return 'الشروط والأحكام';
    }
    if (cleanName.toLowerCase().includes('fund') && cleanName.toLowerCase().includes('document')) {
      return 'وثيقة الصندوق';
    }
    if (cleanName.toLowerCase().includes('reit')) {
      return 'صندوق جدوى ريت السعودي';
    }

    return cleanName;
  }

  /**
   * Get file type information from document
   */
  private getFileTypeInfo(doc: FundDocumentDto): { extension: string; icon: string; color: string; contentType: string } {
    // Try file extension from fileExtension property
    if (doc.fileExtension) {
      const extension = doc.fileExtension.replace('.', ''); // Remove dot if present
      const typeInfo = this.fileTypeIcons[extension.toLowerCase()];
      if (typeInfo) {
        return {
          extension: extension.toUpperCase(),
          icon: typeInfo.icon,
          color: typeInfo.color,
          contentType: this.getContentTypeFromExtension(extension)
        };
      }
    }

    // Try file extension from name
    if (doc.name) {
      const extension = this.getFileExtension(doc.name);
      const typeInfo = this.fileTypeIcons[extension.toLowerCase()];
      if (typeInfo) {
        return {
          extension: extension.toUpperCase(),
          icon: typeInfo.icon,
          color: typeInfo.color,
          contentType: this.getContentTypeFromExtension(extension)
        };
      }
    }

    // Default fallback
    const defaultInfo = this.fileTypeIcons['default'];
    return {
      extension: 'FILE',
      icon: defaultInfo.icon,
      color: defaultInfo.color,
      contentType: 'application/octet-stream'
    };
  }

  /**
   * Extract file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot > 0 ? fileName.substring(lastDot + 1) : '';
  }

  /**
   * Get extension from content type
   */
  private getExtensionFromContentType(contentType: string): string {
    const typeMap: { [key: string]: string } = {
      'application/pdf': 'PDF',
      'application/msword': 'DOC',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
      'application/vnd.ms-excel': 'XLS',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
      'application/vnd.ms-powerpoint': 'PPT',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PPTX',
      'text/plain': 'TXT',
      'text/csv': 'CSV',
      'application/zip': 'ZIP',
      'image/jpeg': 'JPG',
      'image/png': 'PNG'
    };

    return typeMap[contentType.toLowerCase()] || 'FILE';
  }

  /**
   * Get content type from file extension
   */
  private getContentTypeFromExtension(extension: string): string {
    const extensionMap: { [key: string]: string } = {
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'txt': 'text/plain',
      'csv': 'text/csv',
      'zip': 'application/zip',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'bmp': 'image/bmp'
    };

    return extensionMap[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Scroll carousel left
   */
  scrollLeft(): void {
    if (this.canScrollLeft) {
      this.currentIndex = Math.max(0, this.currentIndex - 1);
      this.updateScrollButtons();
      this.scrollToIndex();
    }
  }

  /**
   * Scroll carousel right
   */
  scrollRight(): void {
    if (this.canScrollRight) {
      const maxIndex = Math.max(0, this.displayDocuments.length - this.itemsPerView);
      this.currentIndex = Math.min(maxIndex, this.currentIndex + 1);
      this.updateScrollButtons();
      this.scrollToIndex();
    }
  }



  /**
   * Handle document click
   */
  onDocumentClick(document: DocumentDisplayItem): void {
    console.log('Document clicked:', document);

    // Check if document has a valid URL
    const documentUrl = document.previewUrl || document.downloadUrl;

    if (documentUrl) {
      try {
        // Open document in new tab
        window.open(documentUrl, '_blank', 'noopener,noreferrer');
      } catch (error) {
        console.error('Error opening document:', error);
        // Fallback: try to download
        this.downloadDocument(documentUrl, document.fileName);
      }
    } else {
      console.warn('No URL available for document:', document.fileName);
      // Could show a toast notification here
      alert('عذراً، هذه الوثيقة غير متاحة حالياً');
    }
  }

  /**
   * Download document fallback
   */
  private downloadDocument(url: string, fileName: string): void {
    try {
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading document:', error);
    }
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes?: number): string {
    if (!bytes) return '';

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  /**
   * Check if carousel has documents
   */
  get hasDocuments(): boolean {
    return this.displayDocuments.length > 0;
  }

  /**
   * Get visible documents for current view
   */
  get visibleDocuments(): DocumentDisplayItem[] {
    const start = this.currentIndex;
    const end = Math.min(start + this.itemsPerView, this.displayDocuments.length);
    return this.displayDocuments.slice(start, end);
  }

  /**
   * Get count of documents by type
   */
  getDocumentTypeCount(type: string): number {
    switch (type.toLowerCase()) {
      case 'pdf':
        return this.displayDocuments.filter(doc =>
          doc.contentType.includes('pdf') || doc.fileType.toLowerCase() === 'pdf'
        ).length;
      case 'word':
        return this.displayDocuments.filter(doc =>
          doc.contentType.includes('word') || ['doc', 'docx'].includes(doc.fileType.toLowerCase())
        ).length;
      case 'excel':
        return this.displayDocuments.filter(doc =>
          doc.contentType.includes('excel') || doc.contentType.includes('spreadsheet') ||
          ['xls', 'xlsx'].includes(doc.fileType.toLowerCase())
        ).length;
      default:
        return 0;
    }
  }

  /**
   * Update scroll buttons (make public for template access)
   */
  updateScrollButtons(): void {
    this.canScrollLeft = this.currentIndex > 0;
    this.canScrollRight = this.currentIndex < Math.max(0, this.displayDocuments.length - this.itemsPerView);
  }

  /**
   * Scroll to index (make public for template access)
   */
  scrollToIndex(): void {
    if (this.carouselContainer) {
      const container = this.carouselContainer.nativeElement;
      const itemWidth = container.scrollWidth / this.displayDocuments.length;
      const scrollPosition = this.currentIndex * itemWidth;

      container.scrollTo({
        left: scrollPosition,
        behavior: 'smooth'
      });
    }
  }

  /**
   * Get number of indicator pages
   */
  get indicatorCount(): number {
    return Math.ceil(this.displayDocuments.length / this.itemsPerView);
  }

  /**
   * Get current indicator page
   */
  get currentIndicatorPage(): number {
    return Math.floor(this.currentIndex / this.itemsPerView);
  }

  /**
   * Navigate to indicator page
   */
  goToIndicatorPage(pageIndex: number): void {
    this.currentIndex = pageIndex * this.itemsPerView;
    this.updateScrollButtons();
    this.scrollToIndex();
  }

  /**
   * Get carousel slides with documents grouped by itemsPerView
   */
  getCarouselSlides(): DocumentDisplayItem[][] {
    const slides: DocumentDisplayItem[][] = [];

    for (let i = 0; i < this.displayDocuments.length; i += this.itemsPerView) {
      slides.push(this.displayDocuments.slice(i, i + this.itemsPerView));
    }

    return slides;
  }

  /**
   * Navigate to previous slide
   */
  previousSlide(): void {
    if (this.currentSlideIndex > 0) {
      this.currentSlideIndex--;
    }
  }

  /**
   * Navigate to next slide
   */
  nextSlide(): void {
    const maxSlideIndex = this.getCarouselSlides().length - 1;
    if (this.currentSlideIndex < maxSlideIndex) {
      this.currentSlideIndex++;
    }
  }

  /**
   * Get documents for current slide
   */
  getCurrentSlideDocuments(): DocumentDisplayItem[] {
    const slides = this.getCarouselSlides();
    if (slides.length === 0 || this.currentSlideIndex >= slides.length) {
      return [];
    }
    return slides[this.currentSlideIndex];
  }

  /**
   * Get document subtitle based on available data
   */
  getDocumentSubtitle(document: DocumentDisplayItem): string {
    // Try to extract meaningful subtitle from filename or other properties
    if (document.fileName) {
      // If filename contains common patterns, extract meaningful parts
      const fileName = document.fileName.toLowerCase();

      // Check for common document patterns
      if (fileName.includes('terms') || fileName.includes('شروط') || fileName.includes('أحكام')) {
        return 'الشروط والأحكام';
      }
      if (fileName.includes('contract') || fileName.includes('عقد')) {
        return 'وثيقة تعاقدية';
      }
      if (fileName.includes('report') || fileName.includes('تقرير')) {
        return 'تقرير';
      }
      if (fileName.includes('statement') || fileName.includes('بيان')) {
        return 'بيان مالي';
      }
      if (fileName.includes('policy') || fileName.includes('سياسة')) {
        return 'وثيقة سياسة';
      }
      if (fileName.includes('fund') || fileName.includes('صندوق')) {
        return 'وثيقة الصندوق';
      }
      if (fileName.includes('reit') || fileName.includes('ريت')) {
        return 'صندوق جدوى ريت السعودي';
      }
    }

    return '';
  }

  /**
   * Get document type description in Arabic
   */
  getDocumentTypeDescription(fileType: string): string {
    const typeDescriptions: { [key: string]: string } = {
      'PDF': 'وثيقة PDF',
      'DOC': 'مستند Word',
      'DOCX': 'مستند Word',
      'XLS': 'جدول بيانات Excel',
      'XLSX': 'جدول بيانات Excel',
      'PPT': 'عرض تقديمي PowerPoint',
      'PPTX': 'عرض تقديمي PowerPoint',
      'TXT': 'ملف نصي',
      'CSV': 'ملف بيانات CSV',
      'ZIP': 'ملف مضغوط',
      'JPG': 'صورة',
      'JPEG': 'صورة',
      'PNG': 'صورة',
      'GIF': 'صورة متحركة',
      'MP4': 'ملف فيديو',
      'MP3': 'ملف صوتي',
      'FILE': 'وثيقة'
    };

    return typeDescriptions[fileType?.toUpperCase()] || 'وثيقة';
  }

  /**
   * TrackBy function for documents
   */
  trackByDocumentId(_index: number, document: DocumentDisplayItem): string {
    return document.id;
  }
}
