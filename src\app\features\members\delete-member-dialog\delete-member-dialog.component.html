<div class="delete-dialog-container">
   <div class="">
      <div class="mb-3">
            <img src="assets/images/confirmation-green.svg" class="m-auto d-flex" alt="">
      </div>
          <h2 class=" confirm mb-2 text-center">
        {{'COMMON.CONFIRM' | translate}}
         </h2>
    </div>

  <div class="dialog-content">
    <!-- <div class="member-info">
      <p class="member-name">
        <strong>{{member.memberName}}</strong>
      </p>
      <p class="member-details">
        {{memberTypeDisplay | translate}}
        <span *ngIf="member.isChairman"> - {{'INVESTMENT_FUNDS.MEMBERS.CHAIRMAN' | translate}}</span>
      </p>
    </div> -->

    <!-- Validation Error Message -->
    <div *ngIf="showValidationError" class="error-message">
      <p>{{'INVESTMENT_FUNDS.MEMBERS.ERROR_DELETE_MIN_INDEPENDENT' | translate}}</p>
    </div>

    <!-- Normal Confirmation Message -->
    <div *ngIf="showConfirmationDialog" class="confirmation-message">
      <p  class="sub-title" >
    {{'INVESTMENT_FUNDS.MEMBERS.DELETE_CONFIRMATION' | translate: { member: member.memberName } }}</p>
    </div>
  </div>



  <!-- Error Dialog Actions (Close only) -->
  <!-- <div *ngIf="showValidationError" class="dialog-actions single-action">
    <button
      class="btn close-btn"
      (click)="onCancel()"
      type="button">
      {{'COMMON.CLOSE' | translate}}
    </button>
  </div> -->

  <!-- Normal Dialog Actions (Cancel + Delete) -->
    <div *ngIf="showConfirmationDialog" class="dialog-actions">

    <button class="btn outline-btn w-50" (click)="onCancel()" [disabled]="isDeleting">
      <img src="assets/images/back-icon.png" class="mx-2" alt="verify">
      {{ 'INVESTMENT_FUNDS.RESOLUTIONS.BACK' | translate }}
    </button>
    <button class="btn primary-btn w-50" (click)="onConfirm()"    [disabled]="isDeleting">
      <img src="assets/icons/verify-icon.png" class="mx-2" alt="verify">
<span *ngIf="!isDeleting">{{'INVESTMENT_FUNDS.MEMBERS.DELETE_CONFIRM' | translate}}</span>
      <span *ngIf="isDeleting">{{'COMMON.DELETING' | translate}}</span>
    </button>
  </div>

  <!-- <div *ngIf="showConfirmationDialog" class="dialog-actions">
    <button
      class="btn cancel-btn"
      (click)="onCancel()"
      type="button"
      [disabled]="isDeleting">
      {{'INVESTMENT_FUNDS.MEMBERS.CANCEL' | translate}}
    </button>

    <button
      class="btn delete-btn"
      (click)="onConfirm()"
      type="button"
      [disabled]="isDeleting">
      <span *ngIf="!isDeleting">{{'INVESTMENT_FUNDS.MEMBERS.DELETE_CONFIRM' | translate}}</span>
      <span *ngIf="isDeleting">{{'COMMON.DELETING' | translate}}</span>
    </button>
  </div> -->
</div>





