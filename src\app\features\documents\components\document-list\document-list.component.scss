@import "../../../../../assets/scss/variables";

.document-list-container {
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        gap: 16px;

        p {
            color: #718096;
            margin: 0;
        }
    }

    .empty-state {
        text-align: center;
        padding: 48px 24px;
        color: #718096;

        .empty-icon {
            margin-bottom: 16px;

            mat-icon {
                font-size: 48px;
                width: 48px;
                height: 48px;
                color: #cbd5e0;
            }
        }

        h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #4a5568;
        }

        p {
            margin: 0;
            font-size: 14px;
        }
    }

    .documents-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #E6E6E6;
        margin: 12px;
        box-shadow: 0 1px 15px rgb(47 47 47 / 10%);

        // overflow: hidden;
        // padding: 16px; /* Added padding around the entire table */

        ::ng-deep {
            .mat-mdc-header-cell{
                text-align: start !important;
            }
        }
    }
}

// RTL Support
[dir="rtl"] {
    .document-list-container {
        .empty-state {
            text-align: center;
        }
    }
}
::ng-deep{
  .swal2-popup{
    border-radius: 8px;
    .swal2-title{
      color: $navy-blue;
      font-size: 20px;
      font-weight: 400;
      line-height: 32px;
      padding: 0px;
      margin: 0px;
    }
    .swal2-html-container{
      color: $dark;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      padding: 0px 10px;
      margin-top: 10px;
    }
  }
}
