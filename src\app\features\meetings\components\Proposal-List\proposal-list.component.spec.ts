import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule } from '@angular/material/dialog';
import { of } from 'rxjs';

import { ProposalListComponent } from './proposal-list.component';
import { MeetingsServiceProxy } from '@core/api/api.generated';
import { DateTime } from 'luxon';

describe('MeetingsComponent', () => {
  let component: ProposalListComponent;
  let fixture: ComponentFixture<ProposalListComponent>;
  let mockMeetingsServiceProxy: jasmine.SpyObj<MeetingsServiceProxy>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('MeetingsServiceProxy', ['getProposalsList']);

    await TestBed.configureTestingModule({
      imports: [
        ProposalListComponent,
        RouterTestingModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        MatDialogModule
      ],
      providers: [
        { provide: MeetingsServiceProxy, useValue: spy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ProposalListComponent);
    component = fixture.componentInstance;
    mockMeetingsServiceProxy = TestBed.inject(MeetingsServiceProxy) as jasmine.SpyObj<MeetingsServiceProxy>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load meetings proposals on init', () => {
    const mockResponse = {
      statusCode: 200,
      successed: true,
      message: 'Success',
      data: [
        {
          id: 1,
          fundId: 1,
          subject: 'Test Meeting',
          description: 'Test Description',
          statusId: 1,
          status: {
            id: 1,
            nameAr: 'مسودة',
            nameEn: 'Draft',
            localizedName: 'Draft'
          },
          totalVotes: 5,
          proposedDates: 3,
          canVote: true,
          canViewDetails: true,
          createdDate: DateTime.now()
        }
      ],
      errors: [],
      currentPage: 1,
      totalCount: 1,
      totalPages: 1,
      pageSize: 10,
      hasPreviousPage: false,
      hasNextPage: false
    } as any;

    mockMeetingsServiceProxy.getProposalsList.and.returnValue(of(mockResponse));
    component.fundId = 1;

    component.loadMeetingsProposals();

    expect(mockMeetingsServiceProxy.getProposalsList).toHaveBeenCalledWith(
      undefined, // statusId
      1, // fundId
      undefined, // search
      1, // currentPage
      10, // pageSize
      'id desc' // orderBy
    );
    expect(component.MeetingsProposals.length).toBe(1);
    expect(component.totalCount).toBe(1);
  });

  it('should handle search', () => {
    spyOn(component, 'loadMeetingsProposals');

    component.onSearch('test search');

    expect(component.search).toBe('test search');
    expect(component.currentPage).toBe(1);
    expect(component.loadMeetingsProposals).toHaveBeenCalled();
  });

  it('should navigate to meeting details', () => {
    spyOn(component['router'], 'navigate');
    component.fundId = 1;

    component.viewMeetingDetails(123);

    expect(component['router'].navigate).toHaveBeenCalledWith(
      ['/admin/investment-funds/meetings/details'],
      {
        queryParams: {
          id: 123,
          fundId: 1
        }
      }
    );
  });
});
