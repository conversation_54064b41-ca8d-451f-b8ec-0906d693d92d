<div class="dashboard-page">

  <div class="page-header-section">
    <app-page-header [title]="'DASHBOARD.DASHBOARD'" [showCreateButton]="false" [showSearch]="false" [showFilter]="false">
    </app-page-header>
  </div>
  <!-- Dashboard Content -->


  <div class="row dashboard-content">
    <!--  -->
    <div class="col-lg-8 col-md-12">
      <div class="row">
        <div class="col-lg-6  col-custom-350 mb-3">
          <app-voting-card [title]="'DASHBOARD.FUND_STATISTICS'" [isAdministrator]="isAdministrator" [defaultFunds]="defaultFunds">
          </app-voting-card>
        </div>
        <div class="col-lg-6  col-custom-350 mb-3">
          <div class="box3">
            <div class="column">
              <div class="column2">
                <div class="view">
                  <span class="text">

                    {{ 'DASHBOARD_PAGE.TOTAL_FUNDS' | translate }}

                  </span>
                </div>
                <div class="view">
                  <span class="text2">
                    {{fundStatistics?.totalFunds }}
                  </span>
                </div>
              </div>
              <div class="box">
              </div>
              <div class="row">

                <div class="col-6">

                  <div class="column4">
                    <div class="view">
                      <span class="text">

                        {{ 'DASHBOARD_PAGE.ACTIVE_FUNDS' | translate }}

                      </span>
                    </div>
                    <div class="view2">
                      <span class="text2">
                        {{fundStatistics?.activeFunds || 0 }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="column3">
                    <div class="view">
                      <span class="text">

                        {{ 'DASHBOARD_PAGE.INACTIVE_FUNDS' | translate }}

                      </span>
                    </div>
                    <div class="view2">
                      <span class="text2">
                        {{fundStatistics?.inactiveFunds || 0 }}
                      </span>
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-4">
        <div class="col-lg-12 col-md-12">
          <div class="row-view5 mb-4">
            <span class="text9">

              {{ 'DASHBOARD_PAGE.FUND_DETAILS' | translate }}

            </span>
            <div class="row-view6">

              <!-- Fund Dropdown -->
              <app-fund-dropdown [funds]="defaultFunds" [selectedFundId]="selectedFundId" [loading]="loading"
                [disabled]="false" (fundChanged)="onFundChanged($event)">
              </app-fund-dropdown>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-lg-6 col-size-6 mb-3">
            <div class="column14">
              <div class="column15">
                <div class="row-view23">
                  <div class="resolution-container d-flex justify-content-between align-items-center">
                    <div>
                      <p class="text17 mb-0 pb-0">
                        {{ 'DASHBOARD_PAGE.RESOLUTIONS' | translate }}
                      </p>
                      <p class="text18">

                        {{ 'DASHBOARD_PAGE.TOTAL_RESOLUTIONS' | translate }} ({{resolutionAnalytics?.totalResolutions ||
                        0}})

                      </p>
                    </div>
                    <div>
                      <a (click)="goToResolution()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
                    </div>
                  </div>





                </div>

              </div>



              <!-- <div class="column15">
                    <div class="row-view12">
                       <div class="view5">
                          <a>{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
                      </div>
                    <div class="view6-resolution">
                       <p class="text13">
                            {{ 'DASHBOARD_PAGE.RESOLUTIONS' | translate }}
                       </p>
                      <p class="text14">
                          {{ 'DASHBOARD_PAGE.TOTAL_RESOLUTIONS' | translate }} ({{resolutionAnalytics?.totalResolutions || 0}})
                      </p>
                    </div>

                    </div>

                  </div> -->
              <div class="row-view">
                  <div class="column17">
                  <div class="d-flex justify-content-start align-items-center"
                    *ngFor="let item of resolutionAnalytics?.resolutionStatusDistribution|| [];">
                     <div class="box9 circle mx-2" [ngStyle]="{ 'background-color': statusResolutionsColors[item.statusId] }">
                    </div>
                    <span class="text">
                      {{item.status}} ({{item.count}})
                    </span>

                  </div>
                </div>
                <div class="column16">
                  <!-- DONUT CHART FOR RESOLUTION DISTRIBUTION -->
                  <app-resolution-status-donut-chart
                    [resolutionStatusData]="resolutionAnalytics?.resolutionStatusDistribution || []"
                    [totalResolutions]="resolutionAnalytics?.totalResolutions || 0" [loading]="loading">
                  </app-resolution-status-donut-chart>
                </div>


              </div>
            </div>
          </div>
          <div class="col-lg-6 col-size-6 mb-3">
            <div class="column10">
              <div class=" column15">

                <div class="vote-container  px-2">
                  <div class="first-sec d-flex justify-content-between">
                    <p class="text12 mb-0 ">
                      {{ 'DASHBOARD_PAGE.VOTING' | translate }}

                    </p>
                    <a (click)="goToResolutionDetails()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
                  </div>
                  <div class="see-all d-flex justify-content-between align-items-center" style="height: 2rem;">
                    <p class="text11">
                      {{ 'DASHBOARD_PAGE.TOTAL_VOTES' | translate }} ({{resolutionAnalytics?.defaultResolutionTotalVotes || 0}})
                    </p>
                    <div class="dropdown-container2">
                      <ng-select
                      class="form-select fund-dropdown"
                      [(ngModel)]="selectedResolutionId"
                      (change)="onResolutionChangedForVoting($event)"
                      [disabled]="loading"
                      style="width: 200px; padding: 10px; font-size: 16px; border: 2px solid transparent; border-radius: 4px; background: transparent">
                      <ng-option *ngFor="let item of fundResolutionForVoting" [value]="item.id">
                        {{item.code}}
                      </ng-option>
                    </ng-select>
                    </div>
                  </div>

                </div>

                <div class="column12">
                  <!-- <div class="vote-text">
                      <span class="text12">
                          {{ 'DASHBOARD_PAGE.VOTING' | translate }}


                      </span>
                      <span class="text11">
                          {{ 'DASHBOARD_PAGE.TOTAL_VOTES' | translate }} ({{resolutionAnalytics?.totalVotes || 0}})

                      </span>
                      </div>
                      <div class="see-all">
                      <a>{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
                     <div class="dropdown-container">
                    <app-fund-dropdown
                    [funds]="defaultFunds"
                    [selectedFundId]="selectedFundId"
                    [loading]="loading"
                    [disabled]="false"
                    (fundChanged)="onFundChanged($event)">
                  </app-fund-dropdown>
                  </div>
                      </div> -->
                </div>
              </div>
              <div class="row-view9">
                   <div class="column13">
                  <div class="d-flex justify-content-center align-items-center" *ngFor="let item of resolutionAnalytics?.resolutionVoteResultDistribution|| [];">
                    <div class="box9 circle mx-2"
                      [ngStyle]="{ 'background-color': voteColors[item.voteResult] }">
                    </div>
                    <span class="text">
                      {{item.voteResultDisplay}} ({{item.count}})
                    </span>

                  </div>

                </div>
                <app-vote-result-gauge-chart
                  [voteResultData]="resolutionAnalytics?.resolutionVoteResultDistribution || []" [loading]="loading"
                  [totalVotes]="resolutionAnalytics?.defaultResolutionTotalVotes || 0">
                </app-vote-result-gauge-chart>


              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
    <div class="col-lg-4 col-custom-372 col-md-12">

      <div class="column10 mb-3">
        <div class="column15">
          <div class="row-view18">
            <div class="view7">
              <span class="text16">

              </span>
            </div>
            <div class="box13">
            </div>
            <div class="view8">
              <span class="text17">

                {{ 'DASHBOARD_PAGE.DOCUMENTS' | translate }}

              </span>
              <a (click)="goToDocument()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
            </div>
          </div>
          <div class="view6">
            <span class="text18 mx-3">
              {{ 'DASHBOARD_PAGE.TOTAL_DOCUMENTS' | translate }} ({{documentAnalytics?.totalDocuments || 0}})

            </span>
          </div>
        </div>
        <div class="column19">
          <!-- DOCUMENT CAROUSEL FOR DOCUMENT ANALYTICS -->
          <app-document-carousel [documents]="documentAnalytics?.allDocuments || []" [loading]="loading"
            [itemsPerView]="1">
          </app-document-carousel>
        </div>
      </div>

      <div class="column14 mb-3">
        <div class="column15">
          <div class="row-view23">
            <div class="view7">
              <span class="text10">
              </span>
            </div>
            <div class="view8">
              <span class="text17">
                {{ 'DASHBOARD_PAGE.MEETING_ATTENDANCE' | translate }}
              </span>
              <a (click)="goToMeeting()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>

            </div>
            <div class="see-all d-flex justify-content-between align-items-center" style="height: 2rem;">
              <p class="text11">
                {{ 'DASHBOARD_PAGE.TOTAL_ATTENDANCE' | translate }} ({{meetingCount || 0 }})
              </p>
              <div class="dropdown-container2">
                <ng-select
                class="form-select fund-dropdown"
                [(ngModel)]="selectedMeetingTypeId"
                (change)="onMeetingTypeChanged($event)"
                [disabled]="loading"
                style="width: 200px; padding: 10px; font-size: 16px; border: 2px solid transparent; border-radius: 4px; background: transparent; ">
                <ng-option *ngFor="let item of meetingTypes" [value]="item.id">
                  {{item.name | translate}}
                </ng-option>
              </ng-select>
              </div>
            </div>
          </div>
        </div>
        <div class="column26">
          <div class="column27">
            <div class="column28">
              <!-- PIE CHART FOR MEETINGS ATTENDANCE -->
              <app-meetings-attendance-pie-chart
                [fundId]="selectedFundId"
                [selectedMeetingTypeId]="selectedMeetingTypeId"
                [loading]="meetingsLoading"
                (meetingCount)="getMeetingCount($event)"
                (loadingChange)="onMeetingsLoadingChange($event)">
              </app-meetings-attendance-pie-chart>
            </div>
          </div>

        </div>
      </div>

       <div class="column14 mb-3">
        <div class="column15">
          <div class="row-view23">
            <div class="view7">
              <span class="text10">
              </span>
            </div>
            <div class="view8">
              <span class="text17">
                {{ 'DASHBOARD_PAGE.ASSESSMENTS' | translate }}
              </span>
              <a (click)="goToAssesment()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
            </div>
          </div>
          <div class="view6">
            <span class="text18 mx-3">

              {{ 'DASHBOARD_PAGE.TOTAL_ASSESSMENTS' | translate }} ({{assessmentAnalytics?.totalAssessments || 0 }})

            </span>
          </div>
        </div>
        <div class="column26">
          <div class="column27">
            <div class="column28">
              <!-- PIE CHART FOR ASSESSMENT STATUS DISTRIBUTION -->
              <app-assessment-status-pie-chart
                [assessmentStatusData]="assessmentAnalytics?.assessmentStatusDistribution || []"
                [totalAssessments]="getTotalAssessments()" [loading]="loading">
              </app-assessment-status-pie-chart>
            </div>
          </div>
          <div class="row-view-assesment">
            <div class="row-view24">
              <div class="row-view25" *ngFor="let item of assessmentAnalytics?.assessmentStatusDistribution|| [];">
                <div class="box19 circle mx-2" [ngStyle]="{ 'background-color': statusAssessmentColors[item.statusId] }">
                </div>
                <span class="text">
                  {{item.status}} ({{item.count}})
                </span>

              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
