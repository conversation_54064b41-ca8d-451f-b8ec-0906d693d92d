<div class="dashboard-page">
   <div class="page-header-section">
      <app-page-header [title]="'DASHBOARD.DASHBOARD'" [showCreateButton]="false" [showSearch]="false" [showFilter]="false">
      </app-page-header>
   </div>
   <!-- Dashboard Content -->
   <!-- new dashboard -->
   <div class="cards-container">
      <div class="row">
         <div class="col-lg-3 col-12">
            <div class="persoal-info">
               <app-voting-card [title]="'DASHBOARD.FUND_STATISTICS'" [isAdministrator]="isAdministrator" [defaultFunds]="defaultFunds">
               </app-voting-card>
            </div>
         </div>
         <div class="col-lg-9 col-12">
            <div class="row">
               <div class="col-lg-3 col-12">
                  <div class="vote-card" (click)="goToResolutionInbox()">
                     <p class="title">
                        {{ 'DASHBOARD_PAGE.VOTING_REQUIRED' | translate }}
                     </p>
                     <p class="sub-title">
                        {{fundStatistics?.totalPendingVotes || 0 }}
                     </p>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="fund-activ-card ">
                     <p class="title">
                        {{ 'DASHBOARD_PAGE.ACTIVE_FUNDS' | translate }}
                     </p>
                     <p class="sub-title">
                        {{fundStatistics?.activeFunds || 0 }}
                     </p>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="exit-card ">
                     <p class="title">
                        {{ 'DASHBOARD_PAGE.INACTIVE_FUNDS' | translate }}
                     </p>
                     <p class="sub-title">
                        {{fundStatistics?.inactiveFunds || 0 }}
                     </p>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="box-card">
                     <p class="title">
                        {{ 'DASHBOARD_PAGE.TOTAL_FUNDS' | translate }}
                     </p>
                     <p class="sub-title">
                        {{fundStatistics?.totalFunds }}
                     </p>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="dropdown-container">
      <p class="box-text">
         {{ 'DASHBOARD_PAGE.FUND_DETAILS' | translate }}
      </p>
      <div class="dropdown">
         <!-- Fund Dropdown -->
         <app-fund-dropdown [funds]="defaultFunds" [selectedFundId]="selectedFundId" [loading]="loading"
         [disabled]="false" (fundChanged)="onFundChanged($event)">
         </app-fund-dropdown>
      </div>
   </div>

   <!-- board member -->

  <div *ngIf="tokenService.hasRole('boardmember')">
     <div class="row">
      <div class="col-md-6">
         <div class="resolution-container">
            <div class="resolution-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="resolution-text">
                     {{ 'DASHBOARD_PAGE.RESOLUTIONS' | translate }}
                  </p>
                  <a class="see-all" (click)="goToResolution()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <p class="no-resolution-text">
                  {{ 'DASHBOARD_PAGE.TOTAL_RESOLUTIONS' | translate }} ({{resolutionAnalytics?.totalResolutions || 0}})
               </p>
            </div>
            <div class="resolution-chart-container-board" >
               <div class="resolution-data">
                  <div class="circle-container d-flex justify-content-start align-items-center" *ngFor="let item of resolutionAnalytics?.resolutionStatusDistribution|| [];">
                     <div class="circle mx-2" [ngStyle]="{ 'background-color': statusResolutionsColors[item.statusId] }">
                     </div>
                     <span class="status-text">
                     {{item.status}} ({{item.count}})
                     </span>
                  </div>
               </div>
               <div class="chart d-flex justify-content-center align-items-center">
                  <!-- DONUT CHART FOR RESOLUTION DISTRIBUTION -->
                  <app-resolution-status-donut-chart [isBoradMember]="true"
                  [resolutionStatusData]="resolutionAnalytics?.resolutionStatusDistribution || []"
                  [totalResolutions]="resolutionAnalytics?.totalResolutions || 0" [loading]="loading">
                  </app-resolution-status-donut-chart>
               </div>
            </div>
         </div>

       <div class="document-container">
            <div class="document-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="document-text">
                     {{ 'DASHBOARD_PAGE.DOCUMENTS' | translate }}
                  </p>
                  <a class="see-all" (click)="goToDocument()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <p class="no-document-text">
                  {{ 'DASHBOARD_PAGE.TOTAL_DOCUMENTS' | translate }} ({{documentAnalytics?.totalDocuments || 0}})
               </p>
            </div>
            <div class="document-chart">
               <!-- DOCUMENT CAROUSEL FOR DOCUMENT ANALYTICS -->
               <app-document-carousel [documents]="documentAnalytics?.allDocuments || []" [loading]="loading"
               [itemsPerView]="1" [paddingStyle]="'23px 20px'">
               </app-document-carousel>
            </div>
         </div>
      </div>
      <div class="col-md-6">
          <div class="meeting-container">
            <div class="meeting-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="meeting-text">
                     {{ 'DASHBOARD_PAGE.MEETING_ATTENDANCE' | translate }}
                  </p>
                  <a class="see-all" (click)="goToMeeting()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <div class="d-flex justify-content-between align-items-center">
                  <p class="no-meeting-text">
                     {{ 'DASHBOARD_PAGE.TOTAL_ATTENDANCE' | translate }} ({{meetingCount || 0 }})
                  </p>
                  <div class="dropdown-container2">
                     <ng-select
                     class="form-select fund-dropdown"
                     [(ngModel)]="selectedMeetingTypeId"
                     [clearable]="false"
                     (change)="onMeetingTypeChanged($event)"
                     [disabled]="loading"
                     style="width: 125px; padding: 0; font-size: 10px; border: 2px solid transparent; border-radius: 4px; background: transparent; ">
                     <ng-option *ngFor="let item of meetingTypes" [value]="item.id">
                     {{item.name | translate}}
                     </ng-option>
                     </ng-select>
                  </div>
               </div>
            </div>
            <div class="meeting-chart-container" [ngStyle]="{'justify-content' : meetingsLoading ? ' space-between':'center'}">
               <div class="meeting-chart">
                  <app-meetings-attendance-pie-chart
                  [fundId]="selectedFundId"
                  [selectedMeetingTypeId]="selectedMeetingTypeId"
                  [loading]="meetingsLoading"
                  (meetingCount)="getMeetingCount($event)"
                  (loadingChange)="onMeetingsLoadingChange($event)">
                  </app-meetings-attendance-pie-chart>
               </div>
            </div>
         </div>
            <div class="assessment-container">
            <div class="assessment-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="assessment-text">
                     {{ 'DASHBOARD_PAGE.ASSESSMENTS' | translate }}
                  </p>
                  <a class="see-all" (click)="goToAssesment()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <p class="no-assessment-text">
                  {{ 'DASHBOARD_PAGE.TOTAL_ASSESSMENTS' | translate }} ({{assessmentAnalytics?.totalAssessments || 0 }})
               </p>
            </div>
            <div class="assessment-chart-container" [ngStyle]="{'padding':'0 12px 0 35px'}">
               <div class="assessment-data">
                  <div class="circle-container d-flex justify-content-start align-items-center" *ngFor="let item of assessmentAnalytics?.assessmentStatusDistribution|| [];">
                     <div class="circle mx-2" [ngStyle]="{ 'background-color':  statusAssessmentColors[item.statusId] }">
                     </div>
                     <span class="status-text">
                     {{item.status}} ({{item.count}})
                     </span>
                  </div>
               </div>
               <div class="assessment-chart">
                  <app-assessment-status-pie-chart
                  [assessmentStatusData]="assessmentAnalytics?.assessmentStatusDistribution || []"
                  [totalAssessments]="getTotalAssessments()" [loading]="loading">
                  </app-assessment-status-pie-chart>
               </div>
            </div>
         </div>
      </div>
     </div>

   </div>

   <!-- msh board member -->

 <div *ngIf="!tokenService.hasRole('boardmember')">
     <div class="row">
      <div class="col-md-6">
         <div class="resolution-container">
            <div class="resolution-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="resolution-text">
                     {{ 'DASHBOARD_PAGE.RESOLUTIONS' | translate }}
                  </p>
                  <a class="see-all" (click)="goToResolution()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <p class="no-resolution-text">
                  {{ 'DASHBOARD_PAGE.TOTAL_RESOLUTIONS' | translate }} ({{resolutionAnalytics?.totalResolutions || 0}})
               </p>
            </div>
            <div class="resolution-chart-container">
               <div class="resolution-data">
                  <div class="circle-container d-flex justify-content-start align-items-center" *ngFor="let item of resolutionAnalytics?.resolutionStatusDistribution|| [];">
                     <div class="circle mx-2" [ngStyle]="{ 'background-color': statusResolutionsColors[item.statusId] }">
                     </div>
                     <span class="status-text">
                     {{item.status}} ({{item.count}})
                     </span>
                  </div>
               </div>
               <div class="chart d-flex justify-content-center align-items-center">
                  <!-- DONUT CHART FOR RESOLUTION DISTRIBUTION -->
                  <app-resolution-status-donut-chart  [isBoradMember]="false"
                  [resolutionStatusData]="resolutionAnalytics?.resolutionStatusDistribution || []"
                  [totalResolutions]="resolutionAnalytics?.totalResolutions || 0" [loading]="loading">
                  </app-resolution-status-donut-chart>
               </div>
            </div>
         </div>
      </div>
      <div class="col-md-6">
         <div class="document-container">
            <div class="document-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="document-text">
                     {{ 'DASHBOARD_PAGE.DOCUMENTS' | translate }}
                  </p>
                  <a class="see-all" (click)="goToDocument()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <p class="no-document-text">
                  {{ 'DASHBOARD_PAGE.TOTAL_DOCUMENTS' | translate }} ({{documentAnalytics?.totalDocuments || 0}})
               </p>
            </div>
            <div class="document-chart">
               <!-- DOCUMENT CAROUSEL FOR DOCUMENT ANALYTICS -->
               <app-document-carousel [documents]="documentAnalytics?.allDocuments || []" [loading]="loading"
               [itemsPerView]="1" [paddingStyle]="'14px 20px'">
               </app-document-carousel>
            </div>
         </div>
         <div class="voting-container">
            <div class="voting-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="voting-text">
                     {{ 'DASHBOARD_PAGE.VOTING' | translate }}
                  </p>
                  <a class="see-all" (click)="goToResolutionDetails()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <div class="d-flex justify-content-between align-items-center">
                  <p class="no-voting-text">
                     {{ 'DASHBOARD_PAGE.TOTAL_VOTES' | translate }} ({{resolutionAnalytics?.defaultResolutionTotalVotes || 0}})
                  </p>
                  <div class="dropdown-container2">
                     <ng-select
                     class="form-select fund-dropdown"
                     [(ngModel)]="selectedResolutionId"
                     [clearable]="false"
                     (change)="onResolutionChangedForVoting($event)"
                     [disabled]="loading"
                     style="width: 125px; padding:0; font-size: 10px; border: 2px solid transparent; border-radius: 4px; background: transparent">
                     <ng-option *ngFor="let item of fundResolutionForVoting" [value]="item.id">
                     {{item.code}}
                     </ng-option>
                     </ng-select>
                  </div>
               </div>
            </div>
            <div class="voting-chart-container">
               <div class="voting-data">
                  <div class="circle-container d-flex justify-content-start align-items-center" *ngFor="let item of resolutionAnalytics?.resolutionVoteResultDistribution|| [];">
                     <div class="circle mx-2" [ngStyle]="{ 'background-color': voteColors[item.voteResult] }">
                     </div>
                     <span class="status-text">
                     {{item.voteResultDisplay}} ({{item.count}})
                     </span>
                  </div>
               </div>
               <div class="voting-chart">
                  <app-vote-result-gauge-chart
                  [voteResultData]="resolutionAnalytics?.resolutionVoteResultDistribution || []" [loading]="loading"
                  [totalVotes]="resolutionAnalytics?.defaultResolutionTotalVotes || 0">
                  </app-vote-result-gauge-chart>
               </div>
            </div>
         </div>
      </div>
     </div>
     <div class="row">
      <div class="col-md-6">
         <div class="assessment-container">
            <div class="assessment-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="assessment-text">
                     {{ 'DASHBOARD_PAGE.ASSESSMENTS' | translate }}
                  </p>
                  <a class="see-all" (click)="goToAssesment()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <p class="no-assessment-text">
                  {{ 'DASHBOARD_PAGE.TOTAL_ASSESSMENTS' | translate }} ({{assessmentAnalytics?.totalAssessments || 0 }})
               </p>
            </div>
            <div class="assessment-chart-container" [ngStyle]="{'padding':'11px 12px 11px 35px'}">
               <div class="assessment-data">
                  <div class="circle-container d-flex justify-content-start align-items-center" *ngFor="let item of assessmentAnalytics?.assessmentStatusDistribution|| [];">
                     <div class="circle mx-2" [ngStyle]="{ 'background-color':  statusAssessmentColors[item.statusId] }">
                     </div>
                     <span class="status-text">
                     {{item.status}} ({{item.count}})
                     </span>
                  </div>
               </div>
               <div class="assessment-chart">
                  <app-assessment-status-pie-chart
                  [assessmentStatusData]="assessmentAnalytics?.assessmentStatusDistribution || []"
                  [totalAssessments]="getTotalAssessments()" [loading]="loading">
                  </app-assessment-status-pie-chart>
               </div>
            </div>
         </div>
      </div>
      <div class="col-md-6">
         <div class="meeting-container">
            <div class="meeting-container-top">
               <div class="d-flex justify-content-between align-items-center">
                  <p class="meeting-text">
                     {{ 'DASHBOARD_PAGE.MEETING_ATTENDANCE' | translate }}
                  </p>
                  <a class="see-all" (click)="goToMeeting()">{{ 'DASHBOARD_PAGE.SEE_ALL' | translate }}</a>
               </div>
               <div class="d-flex justify-content-between align-items-center">
                  <p class="no-meeting-text">
                     {{ 'DASHBOARD_PAGE.TOTAL_ATTENDANCE' | translate }} ({{meetingCount || 0 }})
                  </p>
                  <div class="dropdown-container2">
                     <ng-select
                     class="form-select fund-dropdown"
                     [(ngModel)]="selectedMeetingTypeId"
                     [clearable]="false"
                     (change)="onMeetingTypeChanged($event)"
                     [disabled]="loading"
                     style="width: 125px; padding: 0; font-size: 10px; border: 2px solid transparent; border-radius: 4px; background: transparent; ">
                     <ng-option *ngFor="let item of meetingTypes" [value]="item.id">
                     {{item.name | translate}}
                     </ng-option>
                     </ng-select>
                  </div>
               </div>
            </div>
            <div class="meeting-chart-container" [ngStyle]="{'justify-content' : meetingsLoading ? ' space-between':'center'}">
               <div class="meeting-chart">
                  <app-meetings-attendance-pie-chart
                  [fundId]="selectedFundId"
                  [selectedMeetingTypeId]="selectedMeetingTypeId"
                  [loading]="meetingsLoading"
                  (meetingCount)="getMeetingCount($event)"
                  (loadingChange)="onMeetingsLoadingChange($event)">
                  </app-meetings-attendance-pie-chart>
               </div>
            </div>
         </div>
      </div>
     </div>
   </div>

</div>
