import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Shared Components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Core Interfaces and Enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// API Generated Types
import { RejectAssessmentCommand } from '@core/api/api.generated';

export interface RejectAssessmentDialogData {
  assessmentId: number;
  assessmentTitle: string;
}

@Component({
  selector: 'app-reject-assessment-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormBuilderComponent,
    CustomButtonComponent,
    TranslateModule,
  ],
  templateUrl: './reject-assessment-dialog.component.html',
  styleUrls: ['./reject-assessment-dialog.component.scss']
})
export class RejectAssessmentDialogComponent implements OnInit {
  // Form properties
  formGroup!: FormGroup;
  formControls: IControlOption[] = [];
  isValidationFire = false;
  isFormSubmitted = false;

  // UI enums
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  constructor(
    private formBuilder: FormBuilder,
    private translateService: TranslateService,
    private dialogRef: MatDialogRef<RejectAssessmentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RejectAssessmentDialogData
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormControls();
  }

  private initializeForm(): void {
    this.formGroup = this.formBuilder.group({
      rejectionReason: ['', [Validators.required, Validators.maxLength(500)]],
      
    });
  }

  private setupFormControls(): void {
    this.formControls = [
      {
        name: 'rejectionReason',
        formControlName: 'rejectionReason',
        id: 'rejectionReason',
        label: 'ASSESSMENTS.REJECTION_REASON',
        type: InputType.Textarea,
        isRequired: true,
        placeholder: 'ASSESSMENTS.REJECTION_REASON_PLACEHOLDER',
        maxLength: 500
      },
    ];
  }

  onSubmit(): void {
    this.isValidationFire = true;

    if (this.formGroup.valid && !this.isFormSubmitted) {
      this.isFormSubmitted = true;

      const command = new RejectAssessmentCommand({
        id: this.data.assessmentId,
        rejectionReason: this.formGroup.get('rejectionReason')?.value ?? '',
        comments: undefined
      });

      this.dialogRef.close(command);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getDialogTitle(): string {
    return this.translateService.instant('ASSESSMENTS.REJECT_ASSESSMENT_TITLE', {
      title: this.data.assessmentTitle
    });
  }
}
