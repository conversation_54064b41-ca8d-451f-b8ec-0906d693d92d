<div class="dialog-container">
  <h2 class=" header pb-0">
    {{'RESOLUTIONS.REJECT_RESOLUTION' | translate }}
  </h2>
<hr>

  <div class="form-fields mb-0">
    <div class="form-container mb-0">
      <label class="required mb-3 ">{{'RESOLUTIONS.REJECTION_REASON' | translate}} </label>
      <textarea
        class="form-control form-control-solid"
        type="text"
        [(ngModel)]="rejectReason"
        name="rejectReason"
        #rejectReasonRef="ngModel"
        placeholder="{{'RESOLUTIONS.REJECTION_REASON' | translate}}"
        maxlength="500"
        minlength="10"
        required
        ></textarea>

      <div *ngIf="rejectReasonRef.invalid && rejectReasonRef.touched"
        class="text-danger my-1">
        <div *ngIf="rejectReasonRef.errors?.['required']">{{'RESOLUTIONS.REJECTION_REASON_REQUIRED' | translate}}</div>

     <div
        *ngIf="rejectReasonRef?.errors?.['minlength']"
        [translateParams]="{ min: 10 }"
        translate="FORM.MIN_LENGTH_ERROR"
      ></div>
      <div
        *ngIf="rejectReasonRef?.errors?.['maxlength']"
        [translateParams]="{ max: 500 }"
        translate="FORM.MAX_LENGTH_ERROR"
      ></div>
      </div>



    </div>


  </div>
<hr>
  <div class="dialog-actions">
      <button class="btn cancel-btn
       w-25" (click)="onCancel()">
      <img src="assets/icons/cancel-icon.png" class="mx-2" alt="verify">
      {{  'COMMON.CANCEL' | translate }}
      <!-- {{ ('FUND_STRATEGIES.SAVE' | translate) }} -->
    </button>
    <button class="btn primary-btn w-25" (click)="onSubmit()">
      <img src="assets/icons/verify-icon.png" class="mx-2" alt="verify">
      {{ 'RESOLUTIONS.REJECT' | translate }}
    </button>

  </div>
</div>


