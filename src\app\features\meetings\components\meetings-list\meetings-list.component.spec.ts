import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule } from '@angular/material/dialog';
import { of } from 'rxjs';

import { MeetingsListComponent } from './meetings-list.component';
import { MeetingServiceProxy } from '@core/api/api.generated';
import { TokenService } from '../auth/services/token.service';

describe('MeetingsListComponent', () => {
  let component: MeetingsListComponent;
  let fixture: ComponentFixture<MeetingsListComponent>;
  let mockMeetingServiceProxy: jasmine.SpyObj<MeetingServiceProxy>;
  let mockTokenService: jasmine.SpyObj<TokenService>;

  beforeEach(async () => {
    const meetingServiceSpy = jasmine.createSpyObj('MeetingServiceProxy', ['meetingList']);
    const tokenServiceSpy = jasmine.createSpyObj('TokenService', ['hasPermission']);

    await TestBed.configureTestingModule({
      imports: [
        MeetingsListComponent,
        RouterTestingModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        MatDialogModule
      ],
      providers: [
        { provide: MeetingServiceProxy, useValue: meetingServiceSpy },
        { provide: TokenService, useValue: tokenServiceSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MeetingsListComponent);
    component = fixture.componentInstance;
    mockMeetingServiceProxy = TestBed.inject(MeetingServiceProxy) as jasmine.SpyObj<MeetingServiceProxy>;
    mockTokenService = TestBed.inject(TokenService) as jasmine.SpyObj<TokenService>;

    // Mock the API response
    const mockResponse = {
      statusCode: 200,
      successed: true,
      message: 'Success',
      data: [],
      errors: [],
      currentPage: 1,
      totalCount: 0,
      totalPages: 0,
      pageSize: 10,
      hasPreviousPage: false,
      hasNextPage: false
    } as any;
    mockMeetingServiceProxy.meetingList.and.returnValue(of(mockResponse));
    mockTokenService.hasPermission.and.returnValue(true);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load meetings on init', () => {
    component.fundId = 1;
    component.loadMeetings();

    expect(mockMeetingServiceProxy.meetingList).toHaveBeenCalledWith(
      1, // fundId
      undefined, // statusId
      undefined, // typeId
      undefined, // fromDate
      undefined, // toDate
      undefined, // onlyMyMeetings
      undefined, // onlyUpcoming
      undefined, // locationType
      1, // currentPage
      10, // pageSize
      undefined, // search
      'meetingDate desc' // orderBy
    );
  });

  it('should handle search', () => {
    spyOn(component, 'loadMeetings');

    component.onSearch('test search');

    expect(component.search).toBe('test search');
    expect(component.currentPage).toBe(1);
    expect(component.loadMeetings).toHaveBeenCalled();
  });
});
