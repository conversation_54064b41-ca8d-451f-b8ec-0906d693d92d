<!-- Meeting Minutes Popup Component -->
<div class="meeting-minutes-popup">
  <!-- Header -->
  <div class="popup-header">
    <h2 class="popup-title">
      {{ 'INVESTMENT_FUNDS.MEETING.MINUTES_TITLE' | translate }}
    </h2>
    
  </div>

  <!-- Meeting Info -->
  <div class="meeting-info">
    <h3 class="meeting-subject">{{ data.meetingSubject }}</h3>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner"></div>
    <p>{{ 'COMMON.LOADING' | translate }}</p>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && hasError" class="error-container">
    <div class="error-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    <p class="error-message">{{ errorMessage | translate }}</p>
    <button
      class="retry-btn"
      (click)="retryLoadMeetingMinutes()">
      <i class="fas fa-redo"></i>
      {{ 'COMMON.RETRY' | translate }}
    </button>
  </div>

  <!-- Content -->
  <div  class="content-container">
    <!-- Minutes Content -->
    <div class="minutes-content">
      <div class="content-wrapper" [innerHTML]="meetingMinutes?.content"></div>
    </div>
  </div>

  <!-- No Minutes State -->
  <div *ngIf="meetingMinutes?.content==null" class="no-minutes-container">
    <div class="no-minutes-icon">
      <i class="fas fa-file-alt"></i>
    </div>
    <p class="no-minutes-message">
      {{ 'INVESTMENT_FUNDS.MEETING.NO_MINUTES_AVAILABLE' | translate }}
    </p>
  </div>

  <!-- Footer -->
  <div class="popup-footer">
    <button
      class="close-btn-footer"
      (click)="onClose()">
      {{ 'COMMON.CLOSE' | translate }}
    </button>
  </div>
</div>
