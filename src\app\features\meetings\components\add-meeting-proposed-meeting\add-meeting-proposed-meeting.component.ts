import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { FileUploadComponent } from "@shared/components/file-upload/file-upload.component";
import { AppearanceEnum } from '@shared/enum/appearance-enum';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CalendarModeEnum } from '@shared/enum/calender-mode';
import { DatepickerComponent } from '@shared/components/datepicker/datepicker.component';
import {
  CreateMeetingsProposalCommand,
  AddMeetingProposalDateDto,
  StringBaseResponse,
  MeetingsProposalServiceProxy
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { Subject, takeUntil } from 'rxjs';
import { DateTime } from 'luxon';
import moment from 'moment';
import { DateConversionService } from '@shared/services/date.service';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';


@Component({
  selector: 'app-add-meeting-proposed-meeting',
  standalone: true,
  imports: [
    BreadcrumbComponent,
    CustomButtonComponent,
    TranslateModule,
    FormBuilderComponent,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    FileUploadComponent,
    MatCheckboxModule,
   DatepickerComponent

],
  templateUrl: './add-meeting-proposed-meeting.component.html',
  styleUrl: './add-meeting-proposed-meeting.component.scss',
})
export class AddMeetingProposedMeetingComponent implements OnDestroy {
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  fundId = 0;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  // Form properties
  formGroup!: FormGroup;
  formControls: IControlOption[] = [];
  isValidationFire = false;
  isFormSubmitted = false;
  slotsArray = [1, 2, 3, 4];
  attachmentIds : number[] = [];

  appearance = AppearanceEnum;
  controlSize = SizeEnum;
  calendarMode = CalendarModeEnum;

  // API and loading states
  isLoading = false;
  hasDuplicateTimeSlots = false;
  isSubmitting = false; // Prevent double submission
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public translateService: TranslateService,
    private fb: FormBuilder,
    private meetingsServiceProxy: MeetingsProposalServiceProxy,
    private errorModalService: ErrorModalService,
    private DateConversionService: DateConversionService,
    private breadcrumbService: BreadcrumbService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((queryParams) => {
      this.fundId = +queryParams['fundId'] || 0;
    });
    this.initializeBreadcrumbs();
    this.formGroup = this.fb.group({
      subject: ['', [Validators.required, Validators.maxLength(255)]],
      description: ['', [Validators.maxLength(1000)]],
      attachmentId: [[]],  // Initialize as empty array for multiple attachments
      slots: this.fb.group({
        slot1: this.createSlotGroup(),
        slot2: this.createSlotGroup(),
        slot3: this.createSlotGroup(),
        slot4: this.createSlotGroup(),
      }),
    });

     this.setDateRange();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

   private createSlotGroup(): FormGroup {
    return this.fb.group({
      checked: [false],
      date: [null], // Use null instead of empty string for better validation
      time: [''],
    });
  }


  onSubmit(): void {
    this.isFormSubmitted = true;
    console.log('Form submitted, validating...');

    // Basic form validation
    if (!this.formGroup.valid) {

      const subjectControl = this.formGroup.get('subject');
      const descriptionControl = this.formGroup.get('description');
      if (subjectControl?.hasError('required')) {
        this.errorModalService.showError(
          this.translateService.instant('INVESTMENT_FUNDS.MEETING.SUBJECT_REQUIRED')
        );
        return;
      }

      if (subjectControl?.hasError('maxlength')) {
        this.errorModalService.showError(
          this.translateService.instant('INVESTMENT_FUNDS.MEETING.SUBJECT_MAX_LENGTH_ERROR' , { max: 255 })
        );
        return;
      }

      if (descriptionControl?.hasError('maxlength')) {
        this.errorModalService.showError(
          this.translateService.instant('INVESTMENT_FUNDS.MEETING.DESCRIPTION_MAX_LENGTH_ERROR' , { max: 1000 })
        );
        return;
      }

    }

    if (!this.fundId) {
      console.log('Fund ID is missing');
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.INVALID_FUND_ID')
      );
      return;
    }

    // Check if at least one time slot is selected and valid
    const checkedSlots = this.getCheckedTimeSlots();
    if (checkedSlots.length === 0) {
      console.log('No valid time slots selected');
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.AT_LEAST_ONE_SLOT_REQUIRED')
      );
      return;
    }

    // Validate time slots for duplicates and completeness
    this.validateTimeSlots();

    // Check for duplicate time slots
    if (this.hasDuplicateTimeSlots) {
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.DUPLICATE_TIME_SLOTS_ERROR')
      );
      return;
    }

    const futureDateValidation = this.validateFutureDateTimes(checkedSlots);
    if (!futureDateValidation.isValid) {
      this.errorModalService.showError(
        this.translateService.instant('INVESTMENT_FUNDS.MEETING.PAST_DATE_TIME_ERROR')
      );
      return;
    }

    this.createMeetingProposal();
  }

  private getCheckedTimeSlots(): AddMeetingProposalDateDto[] {
    const formValue = this.formGroup.value;
    const slots = formValue.slots;
    const checkedSlots: AddMeetingProposalDateDto[] = [];

    Object.keys(slots).forEach(slotKey => {
      const slot = slots[slotKey];
      console.log(`Processing slot ${slotKey}:`, slot);

      // Validate slot data
      if (slot.checked) {
        console.log(`Slot ${slotKey} is checked`);

        // Check if date is valid
        if (!slot.date) {
          return;
        }

        // Check if time is valid
        if (!slot.time || slot.time.trim() === '') {
          return;
        }

        try {
          // Convert date and time to DateTime
          const dateTime = this.combineDateAndTime(slot.date, slot.time);
          if (dateTime) {
            checkedSlots.push(new AddMeetingProposalDateDto({
              id: 0, // Will be set by backend
              proposedDate: dateTime,
              proposedTime: slot.time
            }));
          }
        } catch (error) {
          console.error(`Error processing slot ${slotKey}:`, error);
        }
      }
    });

    return checkedSlots;
  }

  /**
   * Combine date and time into a DateTime object
   */
  private combineDateAndTime(date: any, time: string): DateTime | null {
    try {
      // Handle different date formats
      let dateObj: Date;

      if (typeof date === 'string') {
        dateObj = new Date(date);
      } else if (date && typeof date === 'object' && date.year && date.month && date.day) {
        // NgbDateStruct format from datepicker
        dateObj = new Date(date.year, date.month - 1, date.day);
      } else if (date instanceof Date) {
        dateObj = date;
      } else {
        console.error('Unsupported date format:', date);
        return null;
      }

      if (isNaN(dateObj.getTime())) {
        console.error('Invalid date:', date);
        return null;
      }

      // Validate and parse time (assuming HH:mm format)
      if (!time || typeof time !== 'string') {
        console.error('Invalid time value:', time);
        return null;
      }

      const timeParts = time.split(':');
      if (timeParts.length !== 2) {
        console.error('Invalid time format, expected HH:mm:', time);
        return null;
      }

      const [hours, minutes] = timeParts.map(Number);
      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        console.error('Invalid time values:', { hours, minutes });
        return null;
      }

      // Create DateTime with date and time
      const dateTime = DateTime.fromJSDate(dateObj).set({
        hour: hours,
        minute: minutes,
        second: 0,
        millisecond: 0
      });

      return dateTime.isValid ? dateTime : null;
    } catch (error) {
      console.error('Error combining date and time:', error);
      return null;
    }
  }

  private createMeetingProposal(): void {
    // Prevent double submission
    if (this.isSubmitting) {
      return;
    }

    this.isLoading = true;
    this.isSubmitting = true;
    const formValue = this.formGroup.value;
    const checkedSlots = this.getCheckedTimeSlots();

    const command = new CreateMeetingsProposalCommand({
      id: 0, // Will be set by backend
      fundId: this.fundId,
      subject: formValue.subject,
      description: formValue.description || '',
      attachmentIds: this.attachmentIds??[],
      meetingProposalDate: checkedSlots
    });

    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      this.isLoading = false;
      this.isSubmitting = false;
      return;
    }

    this.meetingsServiceProxy.createMeetingProposal(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: StringBaseResponse) => {
          this.isLoading = false;
          this.isSubmitting = false;
          if (response.successed) {
            this.errorModalService.showSuccess(
              this.translateService.instant('INVESTMENT_FUNDS.MEETING.CREATED_SUCCESSFULLY')
            );
            // Navigate back to meetings list
            this.router.navigate([`/admin/investment-funds/meetings`], {
              queryParams: { fundId: this.fundId, tab: 'meetings' }
            });
          } else {
            this.errorModalService.showError(
              response.message || this.translateService.instant('INVESTMENT_FUNDS.MEETING.MEETING_PROPOSAL_CREATE_ERROR')
            );
          }
        },
        error: (_error) => {
          this.isLoading = false;
          this.isSubmitting = false;
          // this.errorModalService.showError(
          //   this.translateService.instant('INVESTMENT_FUNDS.MEETING.MEETING_PROPOSAL_CREATE_ERROR'));
        }
      });
  }

  onBreadcrumbClicked(event: IBreadcrumbItem): void {
    if (!event.disabled && event.url) {
      this.router.navigateByUrl(event.url);
    }
  }

  private initializeBreadcrumbs(): void {
   let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      { label: 'INVESTMENT_FUNDS.MEETING.MEETINGSTITLE', url:  `/admin/investment-funds/meetings?fundId=${this.fundId} ` },
      { label: 'INVESTMENT_FUNDS.MEETING.ADD', url: '', disabled: true },

   ];
   this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
   
  }

  cancel() {
  this.router.navigate([`/admin/investment-funds/meetings`], {
      queryParams: { fundId: this.fundId, tab: 'meetings' }
    });
  }

  handleFileUpload(files: any): void {
    this.attachmentIds = [];
    if (Array.isArray(files)) {
      files.forEach((file) => {
        if (file?.id) {
          this.attachmentIds.push(file.id);
        }
      });
    } else if (files?.id) {
      this.attachmentIds.push(files.id);
    }
    this.formGroup.get('attachmentId')?.setValue(this.attachmentIds);
    debugger
  }

  setDateRange() {
    moment.locale('en');
    const today = moment();
    const minDate = this.DateConversionService.mapStringToSelectedDate(
      today.format('DD-MM-YYYY')
    );
    const slotsGroup = this.formGroup.get('slots') as FormGroup;
    Object.keys(slotsGroup.controls).forEach(slotKey => {
      const slotGroup = slotsGroup.get(slotKey) as FormGroup;

      const dateField = slotGroup.get('date');
      if (dateField) {
        (dateField as any).minGreg = minDate;
        (dateField as any).minHijri = minDate;  
      }
    });

    slotsGroup.updateValueAndValidity();
  }
  
  private validateTimeSlots(): void {
    const formValue = this.formGroup.value;
    const slots = formValue.slots;
    let hasCheckedSlots = false;
    let checkedSlotsWithIssues: string[] = [];

    Object.keys(slots).forEach(slotKey => {
      const slot = slots[slotKey];
      if (slot.checked) {
        hasCheckedSlots = true;
        const issues: string[] = [];

        if (!slot.date) {
          issues.push('missing date');
        }
        if (!slot.time || slot.time.trim() === '') {
          issues.push('missing time');
        }

        if (issues.length > 0) {
          checkedSlotsWithIssues.push(`${slotKey}: ${issues.join(', ')}`);
        }
      }
    });

    if (!hasCheckedSlots) {
      console.log('No time slots are checked');
    } else if (checkedSlotsWithIssues.length > 0) {
      console.log('Checked slots with issues:', checkedSlotsWithIssues);
    }

    // Also validate uniqueness
    this.validateUniqueTimeSlots();
  }


  private validateFutureDateTimes(checkedSlots: AddMeetingProposalDateDto[]): { isValid: boolean; pastSlots: string[] } {
    const now = moment();
    const pastSlots: string[] = [];

    checkedSlots.forEach((slot, index) => {
      if (slot.proposedDate) {
        const slotDateTime = moment({
                                    year: slot.proposedDate.year,
                                    month: slot.proposedDate.month - 1,
                                    day: slot.proposedDate.day,
                                    hour: slot.proposedDate.hour,
                                    minute: slot.proposedDate.minute,
                                    second: slot.proposedDate.second,
                                    millisecond: slot.proposedDate.millisecond
                                  });
        if (slotDateTime.isSameOrBefore(now)) {
          pastSlots.push(`${this.translateService.instant('INVESTMENT_FUNDS.MEETING.TIME_SLOT')} ${index + 1}: ${slotDateTime.format('DD-MM-YYYY HH:mm')}`);
        }
      }
    });

    return {
      isValid: pastSlots.length === 0,
      pastSlots: pastSlots
    };
  }


  private validateUniqueTimeSlots(): void {
    const formValue = this.formGroup.value;
    const slots = formValue.slots;
    const dateTimeCombinations: { [key: string]: string[] } = {};
    const duplicateSlots: string[] = [];

    // Clear previous duplicate errors
    this.clearDuplicateErrors();

    // Collect all checked slots with valid date and time
    Object.keys(slots).forEach(slotKey => {
      const slot = slots[slotKey];
      if (slot.checked && slot.date && slot.time && slot.time.trim() !== '') {
        // Create a unique key for date-time combination
        const dateTimeKey = this.createDateTimeKey(slot.date, slot.time);

        if (!dateTimeCombinations[dateTimeKey]) {
          dateTimeCombinations[dateTimeKey] = [];
        }
        dateTimeCombinations[dateTimeKey].push(slotKey);
      }
    });

    // Find duplicates
    Object.keys(dateTimeCombinations).forEach(dateTimeKey => {
      const slotsWithSameDateTime = dateTimeCombinations[dateTimeKey];
      if (slotsWithSameDateTime.length > 1) {
        duplicateSlots.push(...slotsWithSameDateTime);
      }
    });

    if (duplicateSlots.length > 0) {
      this.setDuplicateErrors(duplicateSlots);
    }
    this.hasDuplicateTimeSlots = duplicateSlots.length > 0;
  }

  private createDateTimeKey(date: any, time: string): string {
    if (date && typeof date === 'object' && date.year && date.month && date.day) {
      const dateStr = `${date.year}-${String(date.month).padStart(2, '0')}-${String(date.day).padStart(2, '0')}`;
      return `${dateStr}_${time}`;
    } else if (typeof date === 'string') {
      return `${date}_${time}`;
    } else if (date instanceof Date) {
      const dateStr = date.toISOString().split('T')[0];
      return `${dateStr}_${time}`;
    }
    return `unknown_${time}`;
  }


  private clearDuplicateErrors(): void {
    const slotsGroup = this.formGroup.get('slots') as FormGroup;
    if (slotsGroup) {
      Object.keys(slotsGroup.controls).forEach(slotKey => {
        const slotGroup = slotsGroup.get(slotKey) as FormGroup;
        if (slotGroup) {
          const currentErrors = slotGroup.errors || {};
          if (currentErrors['duplicate']) {
            delete currentErrors['duplicate'];
            const hasOtherErrors = Object.keys(currentErrors).length > 0;
            slotGroup.setErrors(hasOtherErrors ? currentErrors : null);
          }
        }
      });
    }
  }


  private setDuplicateErrors(duplicateSlots: string[]): void {
    const slotsGroup = this.formGroup.get('slots') as FormGroup;
    if (slotsGroup) {
      duplicateSlots.forEach(slotKey => {
        const slotGroup = slotsGroup.get(slotKey) as FormGroup;
        if (slotGroup) {
          const currentErrors = slotGroup.errors || {};
          currentErrors['duplicate'] = true;
          slotGroup.setErrors(currentErrors);
        }
      });
    }
  }

  onDateSelected(dateValues: any, slotIndex?: number): void {
    if (dateValues && dateValues.gregorian) {
      console.log('Gregorian date:', dateValues.gregorian);

      if (slotIndex !== undefined) {
        const slotsGroup = this.formGroup.get('slots') as FormGroup;
        const slotKey = `slot${slotIndex + 1}`;
        const slotGroup = slotsGroup.get(slotKey) as FormGroup;

        if (slotGroup) {
          const dateControl = slotGroup.get('date');
          if (dateControl) {
            dateControl.setValue(dateValues.gregorian);
            dateControl.markAsTouched();
            slotGroup.updateValueAndValidity();
            this.validateUniqueTimeSlots();
          }
        }
      }
    }
  }

  onTimeChanged(slotIndex: number): void {
    const slotGroup = (this.formGroup.get('slots') as FormGroup).get(`slot${slotIndex + 1}`) as FormGroup;
    const pickedTime = slotGroup.get('time')?.value;
    console.log(`Slot ${slotIndex + 1} time:`, pickedTime);
    this.validateUniqueTimeSlots();
  }


  onSlotCheckedChanged(slotIndex: number): void {
    console.log('Slot checked changed for slot index:', slotIndex);
    setTimeout(() => {
      this.validateUniqueTimeSlots();
    }, 100); 
  }
}
