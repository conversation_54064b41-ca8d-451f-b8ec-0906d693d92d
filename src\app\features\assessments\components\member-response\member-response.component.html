<!-- Breadcrumb Navigation -->
<!-- <div class="breadcrumb-section">
  <app-breadcrumb
    (onClickEvent)="onBreadcrumbClicked($event)"
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium"
    divider=">">
  </app-breadcrumb>
</div> -->

<div class="d-flex justify-content-between">
  <div class="header-container w-100 d-flex align-items-center justify-content-between mb-3">
    <div class="d-flex align-items-center">
      <span class="rotate-icon mx-2 cursor-pointer" (click)="onCancel()">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M11.0572 0.75H10.9428C8.75212 0.749987 7.03144 0.749976 5.68802 0.930594C4.31137 1.11568 3.21911 1.50272 2.36091 2.36091C1.50271 3.21911 1.11568 4.31137 0.930593 5.68802C0.749975 7.03144 0.749987 8.75212 0.75 10.9428V11.0572C0.749987 13.2479 0.749975 14.9686 0.930593 16.312C1.11568 17.6886 1.50271 18.7809 2.36091 19.6391C3.21911 20.4973 4.31137 20.8843 5.68802 21.0694C7.03144 21.25 8.75212 21.25 10.9428 21.25H11.0572C13.2479 21.25 14.9686 21.25 16.312 21.0694C17.6886 20.8843 18.7809 20.4973 19.6391 19.6391C20.4973 18.7809 20.8843 17.6886 21.0694 16.312C21.25 14.9686 21.25 13.2479 21.25 11.0572V10.9428C21.25 8.75212 21.25 7.03144 21.0694 5.68802C20.8843 4.31137 20.4973 3.21911 19.6391 2.36091C18.7809 1.50272 17.6886 1.11568 16.312 0.930594C14.9686 0.749976 13.2479 0.749987 11.0572 0.75ZM16.1121 2.41722C17.3224 2.57994 18.0454 2.88853 18.5784 3.42157C19.1115 3.95462 19.4201 4.67757 19.5828 5.8879C19.7484 7.11979 19.75 8.73963 19.75 11C19.75 13.2604 19.7484 14.8802 19.5828 16.1121C19.4201 17.3224 19.1115 18.0454 18.5784 18.5784C18.0454 19.1115 17.3224 19.4201 16.1121 19.5828C14.8802 19.7484 13.2604 19.75 11 19.75C8.73963 19.75 7.11979 19.7484 5.88789 19.5828C4.67757 19.4201 3.95462 19.1115 3.42157 18.5784C2.88853 18.0454 2.57994 17.3224 2.41722 16.1121C2.25159 14.8802 2.25 13.2604 2.25 11C2.25 8.73963 2.25159 7.11979 2.41722 5.8879C2.57994 4.67757 2.88853 3.95462 3.42157 3.42157C3.95462 2.88853 4.67757 2.57994 5.88789 2.41722C7.11979 2.25159 8.73963 2.25 11 2.25C13.2604 2.25 14.8802 2.25159 16.1121 2.41722Z"
            fill="#00205A" />
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M11.9622 7.97726C11.6735 8.27428 11.6802 8.74911 11.9773 9.03781C12.1388 9.19487 12.396 9.3971 12.6407 9.58933C12.6596 9.60416 12.6786 9.61906 12.6976 9.63405C12.9434 9.82696 13.2061 10.0333 13.4548 10.2439C13.4572 10.246 13.4595 10.248 13.4619 10.25L7 10.25C6.58579 10.25 6.25 10.5858 6.25 11C6.25 11.4142 6.58579 11.75 7 11.75L13.4619 11.75C13.4595 11.752 13.4572 11.754 13.4548 11.7561C13.2061 11.9667 12.9434 12.173 12.6976 12.3659C12.6786 12.3809 12.6596 12.3958 12.6407 12.4107C12.396 12.6029 12.1388 12.8051 11.9773 12.9622C11.6802 13.2509 11.6735 13.7257 11.9622 14.0227C12.2509 14.3198 12.7257 14.3265 13.0227 14.0378C13.114 13.9491 13.2958 13.8035 13.5672 13.5903C13.5869 13.5748 13.6069 13.5592 13.6272 13.5432C13.8693 13.3532 14.1534 13.1302 14.4245 12.9005C14.715 12.6543 15.0168 12.3787 15.2515 12.1032C15.369 11.9652 15.485 11.8096 15.5746 11.6422C15.661 11.4807 15.75 11.2583 15.75 11C15.75 10.7417 15.661 10.5193 15.5746 10.3578C15.485 10.1904 15.369 10.0348 15.2515 9.89679C15.0168 9.62131 14.715 9.34574 14.4245 9.09954C14.1534 8.8698 13.8693 8.64683 13.6272 8.45676C13.6069 8.44084 13.5869 8.42515 13.5672 8.40971C13.2958 8.19651 13.114 8.05089 13.0227 7.96219C12.7257 7.67349 12.2509 7.68023 11.9622 7.97726Z"
            fill="#00205A" />
        </svg>
      </span>
      <div class="title-container">
        <p class="title">
          {{ 'ASSESSMENTS.MEMBER_RESPONSE.TITLE' | translate }}
        </p>
        <!-- <p class="sub-title" *ngIf="assessment">
          {{ assessment.title }}
        </p> -->
      </div>
      <div class="status-badge me-3" [ngClass]="getResponseStatusClass(assessmentResponse.status)" *ngIf="assessmentResponse">
        <span class="dot"></span>
        {{ getResponseStatusDisplayName(assessmentResponse.status) }}
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div *ngIf="!isLoading && assessment" class="row">
  <div [class]="isAttachmentType() && assessment && assessment.attachment ? 'col-lg-8' : 'col-lg-12'">
    <!-- Assessment Basic Info -->
    <div class="resolution-details-container">
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'FUND_DETAILS.BASIC_INFO' | translate }}
        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleExpand()">
            <img [src]="isExpanded ? 'assets/images/accrdion_up.png' : 'assets/images/accrdion_down.png'" alt="expand"
              style="width: 14px; height: 8px;" />
          </button>
        </div>
      </div>
      <hr *ngIf="isExpanded" style="margin-bottom: 16px;">

      <div class="resolution-details-content" [class.expanded]="isExpanded">
        <div class="row" style="margin-bottom: 28px;">
          <!-- Assessment Title -->
          <div class="col-md-4 info-item">
            <p class="info-label">{{ 'ASSESSMENTS.ASSESSMENT_TITLE' | translate }}</p>
            <p class="info-value">{{ assessment.title }}</p>
          </div>

          <!-- Assessment Type -->
          <div class="col-md-4 info-item">
            <p class="info-label">{{ 'ASSESSMENTS.ASSESSMENT_TYPE' | translate }}</p>
            <p class="info-value">
              {{ (assessment.type === assessmentType._1 ? 'ASSESSMENTS.QUESTIONNAIRE' : 'ASSESSMENTS.ATTACHMENT') |
              translate }}
            </p>
          </div>

          <!-- Assessment Status -->
          <div class="col-md-4 info-item" *ngIf="assessment">
            <p class="info-label">{{ 'ASSESSMENTS.Current_ASSESSMENT_STATUS' | translate }}</p>
            <div class="info-value">
              <div class="status-badge" [ngClass]="getAssessmentStatusClass(assessment.status)">
                <span class="dot"></span>
                {{ getAssessmentStatusDisplayName(assessment.status, assessment.statusDisplayName) }}
              </div>
            </div>
          </div>
        </div>

        <div class="row" style="margin-bottom: 28px;">
      <!-- Description -->
        <div class="col-md-12 info-item " *ngIf="assessment && assessment.description">
          <p class="info-label">{{ 'ASSESSMENTS.DESCRIPTION' | translate }}</p>
          <p class="info-value description-text" [title]="assessment.description">{{ assessment.description }}</p>
        </div>

        <!-- Instructions -->
        <div class="col-md-12 info-item " *ngIf="assessment && assessment.instructions">
          <p class="info-label">{{ 'ASSESSMENTS.INSTRUCTIONS' | translate }}</p>
          <p class="info-value description-text" [title]="assessment.instructions">{{ assessment.instructions }}</p>
        </div>
        </div>

      
      </div>
    </div>

    <!-- Response Instructions -->
    <div class="response-instructions-container mt-3" *ngIf="hasQuestions() && !isReadOnlyMode">
      <!-- <div class="alert alert-info">
        {{ 'ASSESSMENTS.MEMBER_RESPONSE.RESPONSE_INSTRUCTIONS' | translate }}
      </div> -->
      <app-alert [hasClose]="false" [isStaticPosition]="true"  [alertType]="alertType.Info" [msg]="'ASSESSMENTS.MEMBER_RESPONSE.RESPONSE_INSTRUCTIONS' | translate"></app-alert>
    </div>
    <!-- Read-Only Mode Notification -->
    <div class="response-instructions-container mt-3" *ngIf="isReadOnlyMode && hasQuestions()">
      <div class="alert alert-warning">
        <i class="fas fa-eye me-2"></i>
        {{ getReadOnlyModeMessage() | translate }}
      </div>
    </div>

    <!-- Questions and Response Form -->
    <div class="resolution-details-container mt-3" >
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'ASSESSMENTS.QUESTIONS' | translate }}
        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleQuestionsExpand()">
            <img [src]="isQuestionsExpanded ? 'assets/images/accrdion_up.png' : 'assets/images/accrdion_down.png'"
              alt="expand" style="width: 14px; height: 8px;" />
          </button>
        </div>
      </div>
      <hr *ngIf="isQuestionsExpanded" style="margin-bottom: 16px;">

      <div class="resolution-details-content" [class.expanded]="isQuestionsExpanded">
        <form [formGroup]="responseForm" class="response-form">
        <div *ngFor="let question of assessment.questions; let i = index"
             class="question-response-card mb-4">
          <div class="question-header">
            <h6 class="question-title">
              <span class="question-number">{{ 'ASSESSMENTS.QUESTION' | translate }} {{ i + 1 }}</span>
              <span *ngIf="question.isRequired" class="text-danger">*</span>
            </h6>
            <span class="question-type-badge">
              {{ (question.type === 1 ? 'ASSESSMENTS.SINGLE_CHOICE' :
                  question.type === 2 ? 'ASSESSMENTS.MULTI_CHOICE' :
                  'ASSESSMENTS.TEXT_ANSWER') | translate }}
            </span>
            <!-- <span class="answered-indicator"
                    *ngIf="responseForm.get('question_' + (question.id || i))?.value !== null &&
                           responseForm.get('question_' + (question.id || i))?.value !== '' &&
                           responseForm.get('question_' + (question.id || i))?.value?.length !== 0">
                <i class="fas fa-check-circle"></i>
                {{ 'ASSESSMENTS.ANSWERED' | translate }}
              </span> -->
          </div>

          <div class="question-body">
            <p class="question-text">{{ question.questionText }}</p>

            <!-- Single Choice Question (Radio Buttons) -->
            <div *ngIf="isSingleChoice(question.type)" class="single-choice-options">
              <div class="d-flex align-items-center gap-3 form-check " *ngFor="let option of question.options">
                <mat-radio-group
                  [formControlName]="'question_' + (question.id || i)"
                  class="d-flex align-items-center"
                  [id]="'question_' + (question.id || i) + '_option_' + option.id"
                  [name]="'question_' + (question.id || i)"
                  [disabled]="shouldDisableFormControls()">
                  <div>
                    <mat-radio-button [value]="option.id" [id]="'question_' + (question.id || i) + '_option_' + option.id" class="ml-4px">
                    </mat-radio-button>
                  </div>
                </mat-radio-group>
                <label
                  class="form-check-label"
                  [for]="'question_' + (question.id || i) + '_option_' + option.id">
                  {{ isOtherOption(option) ? ('ASSESSMENTS.OTHER' | translate) : option.optionText }}
                </label>
              </div>

              <!-- Custom text input for "Other" option -->
              <div *ngIf="isOtherOptionSelected(question.id || i)" class="other-text-input mt-3">
                <label class="form-label">{{ 'ASSESSMENTS.OTHER_SPECIFY' | translate }}</label>
                <textarea
                  class="form-control"
                  [formControlName]="'question_' + (question.id || i) + '_other_text'"
                  [placeholder]="'ASSESSMENTS.OTHER_PLACEHOLDER' | translate"
                  [disabled]="shouldDisableFormControls()"
                  rows="3"></textarea>

                <!-- Validation Error for Other Text -->
                <div *ngIf="responseForm.get('question_' + (question.id || i) + '_other_text')?.invalid &&
                            responseForm.get('question_' + (question.id || i) + '_other_text')?.touched"
                     class="error-message mt-2">
                  {{ 'ASSESSMENTS.MEMBER_RESPONSE.REQUIRED_FIELD' | translate }}
                </div>
              </div>
            </div>

            <!-- Multiple Choice Question (Checkboxes) -->
            <div *ngIf="isMultipleChoice(question.type)" class="multiple-choice-options">
              <div class="d-flex align-items-center gap-3 form-check" *ngFor="let option of question.options">
                <input
                class="form-check-input"
                type="checkbox"
                [id]="'question_' + (question.id || i) + '_option_' + option.id"
                [value]="option.id"
                [checked]="option.isSelected"
                [disabled]="shouldDisableFormControls()"
                (change)="onMultipleChoiceChange($event, question.id || i, option.id)">
                <label
                  class="form-check-label"
                  [for]="'question_' + (question.id || i) + '_option_' + option.id">
                  {{ option.optionText }}
                </label>
              </div>
            </div>

            <!-- Text Answer Question - Single Choice Style -->
            <div *ngIf="isTextAnswer(question.type)" class="single-choice-options">
              <div class="d-flex align-items-center gap-3 form-check " *ngFor="let option of question.options">
                <mat-radio-group
                  [formControlName]="'question_' + (question.id || i)"
                  class="d-flex align-items-center"
                  [id]="'question_' + (question.id || i) + '_option_' + option.id"
                  [name]="'question_' + (question.id || i)"
                  [disabled]="shouldDisableFormControls()">
                  <div>
                    <mat-radio-button [value]="option.id" [id]="'question_' + (question.id || i) + '_option_' + option.id" class="ml-4px">
                    </mat-radio-button>
                  </div>
                </mat-radio-group>
                <label
                  class="form-check-label"
                  [for]="'question_' + (question.id || i) + '_option_' + option.id">
                  {{  ((option.optionText ? option.optionText :'') | translate) }}
                </label>
              </div>

              <!-- Optional Comment Field for Text Questions -->
              <div class="text-question-comment mt-3">
                <label class="form-label">{{ 'ASSESSMENTS.COMMENT_OPTIONAL' | translate }}</label>
                <textarea
                  class="form-control"
                  [formControlName]="'question_' + (question.id || i) + '_comment'"
                  [placeholder]="'ASSESSMENTS.COMMENT_PLACEHOLDER' | translate"
                  [disabled]="shouldDisableFormControls()"
                  rows="3"></textarea>
              </div>
            </div>

            <!-- Validation Error for Single Choice, Multiple Choice, and Text Questions -->
            <div *ngIf="responseForm.get('question_' + (question.id || i))?.invalid &&
                        responseForm.get('question_' + (question.id || i))?.touched"
                 class="error-message mt-2">
              {{ 'ASSESSMENTS.MEMBER_RESPONSE.REQUIRED_FIELD' | translate }}
            </div>
          </div>
        </div>

        <!-- Attachment Decision Section for Attachment-Type Assessments -->
        <div *ngIf="isAttachmentType()" class="attachment-decision-section mb-4">
          <div class="question-response-card">
            <div class="question-header">
              <h6 class="question-title">
                <span class="question-number">{{ 'ASSESSMENTS.MEMBER_RESPONSE.DECISION' | translate }}</span>
                <span class="text-danger">*</span>
              </h6>
            </div>

            <div class="question-body">
              <!-- Decision Selection -->
              <div class="decision-selection mb-3">
                <div class="decision-options">
                  <div class="form-check" *ngFor="let option of decisionOptions">
                    <input
                      class="form-check-input"
                      type="radio"
                      name="attachment_decision"
                      [id]="'decision_' + option.id"
                      formControlName="attachment_decision"
                      [value]="option.id"
                      [disabled]="shouldDisableFormControls()">
                    <label
                      class="form-check-label"
                      [for]="'decision_' + option.id">
                      {{ option.name }}
                    </label>
                  </div>
                </div>

                <!-- Decision Validation Error -->
                <div *ngIf="responseForm.get('attachment_decision')?.invalid &&
                           responseForm.get('attachment_decision')?.touched"
                     class="error-message mt-2">
                  {{ 'ASSESSMENTS.MEMBER_RESPONSE.DECISION_REQUIRED' | translate }}
                </div>
              </div>

              <!-- Comment Section -->
              <div class="comment-section">
                <label class="form-label" for="attachment_comment">
                  {{ 'ASSESSMENTS.MEMBER_RESPONSE.COMMENT' | translate }}
                  <span class="text-muted">({{ 'COMMON.OPTIONAL' | translate }})</span>
                </label>
                <textarea
                  id="attachment_comment"
                  class="form-control"
                  formControlName="attachment_comment"
                  [placeholder]="'ASSESSMENTS.MEMBER_RESPONSE.COMMENT_PLACEHOLDER' | translate"
                  [disabled]="shouldDisableFormControls()"
                  [attr.data-has-content]="hasAttachmentCommentContent()"
                  rows="4"></textarea>
              </div>
            </div>
          </div>
        </div>

        </form>
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="col-lg-4">
    <div  *ngIf="isAttachmentType() && assessment && assessment.attachment" class="attachment-section">
      <p class="title">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.FILES' | translate }}
      </p>
      <hr>
      <p class="sub-title">
        {{ 'ASSESSMENTS.FILE' | translate }}
      </p>
      <div class="mb-3">
        <app-attachment-card [attachment]="assessment.attachment"></app-attachment-card>
  
      </div>
    </div>
    
  </div>
  <!-- Action Buttons -->
    <div class="dialog-actions d-flex justify-content-end my-4">
      <app-custom-button
        [btnName]="'COMMON.CANCEL' | translate"
        (click)="onCancel()"
        [buttonType]="buttonEnum.Secondary"
        [iconName]="IconEnum.cancel">
      </app-custom-button>

      <app-custom-button
        *ngIf="shouldShowSaveButton()"
        [btnName]="'ASSESSMENTS.MEMBER_RESPONSE.SAVE_DRAFT' | translate"
        (click)="onSaveDraft()"
        class="mx-2"
        [buttonType]="buttonEnum.OutLine"
        [iconName]="IconEnum.draft"
        [disabled]="isSaving || shouldDisableFormControls()">
      </app-custom-button>

      <app-custom-button
        *ngIf="shouldShowSubmitButton()"
        [btnName]="'ASSESSMENTS.MEMBER_RESPONSE.SUBMIT_RESPONSE' | translate"
        (click)="onSubmitResponse()"
        class="mx-2"
        [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.verify"
        [disabled]="isSubmitting || shouldDisableFormControls()">
      </app-custom-button>
    </div>
</div>

