import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';


// Shared Components
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Core Services and Interfaces
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IconEnum } from '@core/enums/icon-enum';
import { SizeEnum } from '@shared/enum/size-enum';


// API Generated Types - Following resolution component patterns
import {
  AssessmentServiceProxy,
  UpdateAssessmentCommand,
  CreateAssessmentQuestionDto,
  CreateAssessmentOptionDto,
  AssessmentType,
  AssessmentByIdDto,
  AssessmentByIdDtoBaseResponse,
  AssessmentStatus,
} from '@core/api/api.generated';

// Services
import { ErrorModalService } from '@core/services/error-modal.service';

// Question Dialog
import { QuestionDialogComponent, QuestionDialogData } from '../question-dialog/question-dialog.component';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';




@Component({
  selector: 'app-edit-assessment',
  standalone: true,
  imports: [
    CommonModule,
    BreadcrumbComponent,
    PageHeaderComponent,
    FormBuilderComponent,
    CustomButtonComponent,
    TranslateModule,
  ],
  templateUrl: './edit-assessment.component.html',
  styleUrls: ['./edit-assessment.component.scss']
})
export class EditAssessmentComponent implements OnInit, OnDestroy {
  // Lifecycle management
  private destroy$ = new Subject<void>();

  // Form properties
  formGroup!: FormGroup;
  formControls: IControlOption[] = [];
  isValidationFire = false;
  isFormSubmitted = false;

  // UI Properties - Following edit-resolution pattern
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];
  canAddAdditionalAttachments: boolean = true;

  // UI enums
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  assessmentType = AssessmentType;

  // Data properties
  currentFundId = 0;
  currentAssessmentId = 0;
  title: string = 'ASSESSMENTS.EDIT_ASSESSMENT';
  isLoading = false;
  currentAssessment: AssessmentByIdDto | null = null;
  assessmentStatus = AssessmentStatus;
  // Questions management (for questionnaire type) - Following resolution component patterns
  questions: CreateAssessmentQuestionDto[] = [];
  currentQuestionIndex = 0;

  // File upload properties
  uploadedFileId: number | null = null;
  uploadedFileName: string = '';
  isFileUploading = false;
  fileUploadError: string = '';
  currentFundName = '';

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private assessmentServiceProxy: AssessmentServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService,
    private dialog: MatDialog,
    private breadcrumbService: BreadcrumbService
  ) { }

  ngOnInit(): void {
        this.currentFundName=localStorage.getItem('fundName') || '';
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Component Initialization - Following edit-user pattern
  private initializeComponent(): void {
    this.getAssessmentIdFromRoute();
    this.initForm();
    this.initFormControls();
    this.loadAssessmentData();
  }

  // Route Parameter Handling - Following edit-user pattern
  private getAssessmentIdFromRoute(): void {
    this.currentAssessmentId = Number(this.route.snapshot.paramMap.get('id'));
    if (!this.currentAssessmentId) {
      this.errorModalService.showError('ASSESSMENTS.INVALID_ASSESSMENT_ID');
      this.router.navigate(['/admin/investment-funds/assessments']);
    }
  }

  // Breadcrumb Navigation - Following create-resolution pattern
  private updateBreadcrumbWithFallback(): void {
    let currentFundName = localStorage.getItem('fundName') || "";
    this.breadcrumbItems = [
      {
        label: 'INVESTMENT_FUNDS.TITLE',
        url: '/admin/investment-funds',
        icon: 'fas fa-home',
      },
      {
        label:currentFundName|| 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`,
      },
      {
        label:'ASSESSMENTS.TITLE',
        url: `/admin/investment-funds/assessments?fundId=${this.currentFundId}`
      },
      { label: 'ASSESSMENTS.EDIT_ASSESSMENT' , url: '', disabled: true },
    ];
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  onBreadcrumbClicked(event: any): void {
    if (event?.url) {
      this.router.navigateByUrl(event.url);
    }
  }

  // Form Controls Configuration - Following create-assessment pattern
  private initFormControls(): void {
    this.formControls = [
      {
        type: InputType.Text,
        formControlName: 'title',
        id: 'title',
        name: 'title',
        label: 'ASSESSMENTS.ASSESSMENT_TITLE',
        placeholder: 'ASSESSMENTS.ASSESSMENT_TITLE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-12',
        maxLength: 255
      },

      {
        type: InputType.Textarea,
        formControlName: 'description',
        id: 'description',
        name: 'description',
        label: 'ASSESSMENTS.DESCRIPTION',
        placeholder: 'ASSESSMENTS.DESCRIPTION_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        maxLength: 1000
      },
      {
        type: InputType.Textarea,
        formControlName: 'instructions',
        id: 'instructions',
        name: 'instructions',
        label: 'ASSESSMENTS.INSTRUCTIONS',
        placeholder: 'ASSESSMENTS.INSTRUCTIONS_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        maxLength: 2000
      },
      {
        type: InputType.Radio,
        formControlName: 'type',
        id: 'type',
        name: 'type',
        label: 'ASSESSMENTS.ASSESSMENT_TYPE',
        isRequired: true,
        class: 'col-md-12',
        options: [
          { name: 'ASSESSMENTS.QUESTIONNAIRE', id: AssessmentType._1 },
          { name: 'ASSESSMENTS.ATTACHMENT', id: AssessmentType._2 }
        ],
        onChange: (value: any) => this.onAssessmentTypeChange(value)
      },
      {
        type: InputType.file,
        formControlName: 'attachmentId',
        id: 'attachmentId',
        name: 'attachmentId',
        label: 'ASSESSMENTS.ATTACHMENT',
        placeholder: 'ASSESSMENTS.ATTACHMENT_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        allowedTypes: ['pdf'],
        max: 10,
        isVisible: () => this.formGroup?.get('type')?.value === AssessmentType._2
      }
    ];
  }

  // Form Initialization - Following edit-resolution pattern
  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      title: ['', [Validators.required, Validators.maxLength(255)]],
      description: ['', [Validators.maxLength(1000)]],
      instructions: ['', [Validators.maxLength(2000)]],
      type: [AssessmentType._1, Validators.required],
      attachmentId: [null]
    });

    // Watch for type changes to update attachment field visibility
    this.formGroup.get('type')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.updateAttachmentFieldVisibility(value);
      });
  }

  // Load Assessment Data using NSwag-generated API
  private loadAssessmentData(): void {
    this.isLoading = true;

    this.assessmentServiceProxy.getById(this.currentAssessmentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: AssessmentByIdDtoBaseResponse) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.currentAssessment = response.data;
            this.populateForm(response.data);
          } else {
            this.errorModalService.showError(
              response.message || this.translateService.instant('ASSESSMENTS.LOAD_ERROR')
            );
          }
        },
        error: (error: any) => {
          this.isLoading = false;
          console.error('Error loading assessment:', error);

          // Extract error message from API response
          let errorMessage = this.translateService.instant('ASSESSMENTS.LOAD_ERROR');
          if (error?.error?.message) {
            errorMessage = error.error.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }

          this.errorModalService.showError(errorMessage);
        }
      });
  }

  // Form Population - Following edit-user pattern
  private populateForm(assessment: AssessmentByIdDto): void {
    try {
      // Populate basic form fields
      this.formGroup.patchValue({
        title: assessment.title || '',
        description: assessment.description || '',
        instructions: assessment.instructions || '',
        type: assessment.type || AssessmentType._1,
        attachmentId: assessment.attachment?.id || null
      });

      // Set current fund ID from assessment data
      this.currentFundId = assessment.fundId;
      this.uploadedFileId=assessment.attachment?.id || null;
      // Populate questions for questionnaire type
      if (assessment.type === AssessmentType._1 && assessment.questions) {
        this.questions = assessment.questions.map(q => {
          const question = new CreateAssessmentQuestionDto();
          question.id = q.id;
          question.questionText = q.questionText;
          question.type = q.questionType;
          question.order = q.displayOrder;
          question.isRequired = q.isRequired;
          question.options = q.options?.map(opt => {
            const option = new CreateAssessmentOptionDto();
            option.optionText = opt.value;
            option.order = opt.order;
            option.isOther = opt.isOther;
            return option;
          }) || [];
          return question;
        });
      }

      // Populate attachment information for attachment type
      if (assessment.type === AssessmentType._2 && assessment.attachment) {

        const attachment = assessment.attachment;

        this.formControls.find(
          (c) => c.formControlName === "attachmentId"
        )!.initialFiles = [attachment];


      }
    this.updateBreadcrumbWithFallback();

      // Update form controls visibility based on assessment type
      this.updateAttachmentFieldVisibility(assessment.type);



    } catch (error) {
      console.error('Error populating form:', error);
      this.errorModalService.showError(this.translateService.instant('ASSESSMENTS.POPULATE_ERROR'));
    }
  }



  // Assessment Type Change Handler
  onAssessmentTypeChange(value: AssessmentType): void {
    this.updateAttachmentFieldVisibility(value);

    // Clear questions if switching from questionnaire to attachment
    if (value === AssessmentType._2) {
      this.questions = [];
    }

    // Clear attachment if switching from attachment to questionnaire
    if (value === AssessmentType._1) {
      this.formGroup.get('attachmentId')?.setValue(null);
      this.uploadedFileId = null;
      this.uploadedFileName = '';
    }
  }

  private updateAttachmentFieldVisibility(type: AssessmentType): void {
    const attachmentControl = this.formControls.find(c => c.formControlName === 'attachmentId');
    if (attachmentControl) {
      attachmentControl.isVisible = () => type === AssessmentType._2;
    }
  }

  // File Upload Handler
  onFileUpload(event: any): void {
    if (event && event.file) {
      this.uploadedFileId = event.file.id;
      this.uploadedFileName = event.file.name || event.file.fileName || '';
      this.formGroup.get('attachmentId')?.setValue(event.file.id);
    }
  }
    dateSelected(event: any): void {
    this.formGroup
      .get(event.control.formControlName)
      ?.setValue(event.event.formattedGregorian);
  }




  // Question Management - Following create-assessment pattern
  addAssessmentQuestion(): void {
    const dialogData: QuestionDialogData = {
      isEdit: false,
      existingQuestions: this.questions
    };

    const dialogRef = this.dialog.open(QuestionDialogComponent, {
      width: '800px',
      data: dialogData,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const newQuestion = new CreateAssessmentQuestionDto({
          id: result.id,
          questionText: result.questionText,
          type: result.type || result.questionType,
          order: this.questions.length + 1,
          isRequired: result.isRequired || false,
          options: result.options || [],
          assessmentId: this.currentAssessmentId,
          customText: result.customText
        });
        this.questions.push(newQuestion);
      }
    });
  }

  // Legacy method name for backward compatibility
  addQuestion(): void {
    this.addAssessmentQuestion();
  }

  editAssessmentQuestion(question: CreateAssessmentQuestionDto, index: number): void {
    const dialogData: QuestionDialogData = {
      question: new CreateAssessmentQuestionDto(question),
      isEdit: true,
      questionIndex: index,
      existingQuestions: this.questions
    };

    const dialogRef = this.dialog.open(QuestionDialogComponent, {
      width: '800px',
      data: dialogData,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.questions[index] = new CreateAssessmentQuestionDto({
          id: question.id,
          questionText: result.questionText,
          type: result.type || result.questionType,
          order: question.order,
          isRequired: result.isRequired || false,
          options: result.options || [],
          assessmentId: question.assessmentId,
          customText: undefined
        });
      }
    });
  }

  // Legacy method name for backward compatibility
  editQuestion(index: number): void {
    const question = this.questions[index];
    this.editAssessmentQuestion(question, index);
  }

  removeQuestion(index: number): void {

    this.questions.splice(index, 1);
    this.reorderQuestions();
  }

  private reorderQuestions(): void {
    // Update display order for all questions - Following resolution item pattern
    this.questions.forEach((question, index) => {
      question.order = index + 1;
    });
  }

  // Form Submission - Following create-assessment pattern
  onSubmit(): void {

    this.isValidationFire = true;

    // Validate form based on assessment type
    if (!this.validateFormForSubmission()) {
      return;
    }

    if (this.formGroup.valid && !this.isFormSubmitted) {
      this.isFormSubmitted = true;
      this.callUpdateApi();
    }
  }

  private validateFormForSubmission(): boolean {
    const assessmentType = this.formGroup.get('type')?.value;

    // Validate questionnaire type
    if (assessmentType === AssessmentType._1) {
      if (this.questions.length === 0) {
        this.showValidationError('ASSESSMENTS.VALIDATION.QUESTIONS_REQUIRED');
        return false;
      }
    }

    // Validate attachment type
    if (assessmentType === AssessmentType._2) {
      if (!this.uploadedFileId) {
        this.showValidationError('ASSESSMENTS.VALIDATION.ATTACHMENT_REQUIRED');
        return false;
      }
    }

    return true;
  }

  private showValidationError(messageKey: string): void {
    this.isValidationFire = false;
    this.errorModalService.showError(this.translateService.instant(messageKey));
  }

  onSaveAsDraft(): void {
    this.isValidationFire = false;
    if(!this.isFormSubmitted)
    {
      this.isFormSubmitted = true;
      this.callUpdateApi(true);
    }
  }

  // Update API call using NSwag-generated service
  private callUpdateApi(isDraft: boolean = false): void {
    const command = new UpdateAssessmentCommand({
      id: this.currentAssessmentId,
      fundId: this.currentFundId,
      title: this.formGroup.get('title')?.value ?? '',
      type: this.formGroup.get('type')?.value ?? AssessmentType._1,
      attachmentId: this.formGroup.get('attachmentId')?.value || undefined,
      questions: this.formGroup.get('type')?.value === AssessmentType._1 ? this.questions : undefined,
      saveAsDraft: isDraft,
      allowAnonymousResponses: false,
      allowResponseEditing: true,
      description: this.formGroup.get('description')?.value ?? undefined,
      instructions: this.formGroup.get('instructions')?.value ?? undefined
    });

    this.assessmentServiceProxy.edit(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isFormSubmitted = false;
          this.isValidationFire = false;

          if (response.successed) {
            const messageKey = isDraft ? 'ASSESSMENTS.SUCCESS_SAVED_DRAFT' : 'ASSESSMENTS.SUCCESS_UPDATED';
            this.errorModalService.showSuccess(this.translateService.instant(messageKey));

            this.router.navigate(['/admin/investment-funds/assessments'], {
              queryParams: { fundId: this.currentFundId }
            });
          } else {
            // this.errorModalService.showError(
            //   response.message || this.translateService.instant('ASSESSMENTS.UPDATE_ERROR')
            // );
          }
        },
        error: (error: any) => {
          this.isFormSubmitted = false;
          this.isValidationFire = false;
          console.error('Error updating assessment:', error);

          let errorMessage = this.translateService.instant('ASSESSMENTS.UPDATE_ERROR');
          if (error?.error?.message) {
            errorMessage = error.error.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }

          // this.errorModalService.showError(errorMessage);
        }
      });
  }

  // Utility methods
  isQuestionnaireType(): boolean {
    return this.formGroup?.get('type')?.value === AssessmentType._1;
  }

  isAttachmentType(): boolean {
    return this.formGroup?.get('type')?.value === AssessmentType._2;
  }

  // Navigation
  onCancel(): void {
    this.router.navigate(['/admin/investment-funds/assessments'], {
      queryParams: { fundId: this.currentFundId }
    });
  }
  getOptions(options:CreateAssessmentOptionDto[]):CreateAssessmentOptionDto[]
  {
    return options.filter(o=>!o.isOther);
  }
}
