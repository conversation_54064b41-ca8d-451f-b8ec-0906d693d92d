import { Component } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomButtonComponent } from '../../../../shared/components/custom-button/custom-button.component';
import { IconEnum } from '@core/enums/icon-enum';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import {
  AuthenticationServiceProxy,
  IJwtAuthResponseBaseResponse,
  SignInCommand,
  UserManagementServiceProxy,
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { AuthService } from '../../services/auth-service/auth.service';
import { TokenService } from '../../services/token.service';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { UserManagementService } from '@shared/services/users/user-management.service';
@Component({
  selector: 'app-change-password',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    CustomButtonComponent,
    FormBuilderComponent
  ],
  templateUrl: './change-password.component.html',
  styleUrl: './change-password.component.scss'
})

export class ChangePasswordComponent {
  formGroup!: FormGroup;
  showPassword = false;
  currentLang:any;
  lang:any;
  isFormSubmitted: boolean | undefined = false;


  // formControls: IControlOption[] = [
  //   {
  //     formControlName: 'newPassword',
  //     type: InputType.Password,
  //     id: 'newPassword',
  //     name: 'newPassword',
  //     label: 'LOGIN_PAGE.NEW_PASSWORD',
  //     placeholder: 'LOGIN_PAGE.NEW_PASSWORD',
  //     isRequired: true,
  //     class: 'col-md-6 offset-6',
  //     minLength: 8,
  //     maxLength: 50,
  //     showStrengthIndicator:true,
  //     tooltipText: this.tooltipText || 'l'
  //   },
  //   {
  //     formControlName: 'confirmPassword',
  //     type: InputType.Password,
  //     id: 'confirmPassword',
  //     name: 'confirmPassword',
  //     label: 'LOGIN_PAGE.CONFIRM_PASSWORD',
  //     placeholder: 'LOGIN_PAGE.CONFIRM_PASSWORD',
  //     isRequired: true,
  //     class: 'col-md-6',
  //     minLength: 8,
  //     maxLength: 50,
  //     tooltipText: 'null' // will be set later

  //   },

  // ];

  iconName = IconEnum.arrowRight;
  tooltipText!: string;

   formControls!: IControlOption[];
  constructor(
    private formBuilder: FormBuilder,
    private apiClient: AuthenticationServiceProxy,
    private router: Router,
    private languageService: LanguageService,
    private errorModalService: ErrorModalService,
    private authService: AuthService,
    private tokenService: TokenService,
    private translateService: TranslateService,
    private userManagementService: UserManagementService,

  ) {
     this.lang = JSON.parse(localStorage.getItem('lang') || '""');
    this.currentLang = this.lang === 'en' ? LanguageEnum.en : LanguageEnum.ar;
  }

  ngOnInit() {
    this.buildTooltipText();
     this.languageService.currentLang$.subscribe(lang => {
      this.currentLang = lang;
            this.buildTooltipText();
    });

    const rules = [
    'LOGIN_PAGE.PASSWORD_RULES.RULE_1',
    'LOGIN_PAGE.PASSWORD_RULES.RULE_2',
    'LOGIN_PAGE.PASSWORD_RULES.RULE_3',
    'LOGIN_PAGE.PASSWORD_RULES.RULE_4',
    'LOGIN_PAGE.PASSWORD_RULES.RULE_5',
    'LOGIN_PAGE.PASSWORD_RULES.RULE_6'
  ];

  // this.tooltipText = rules
  //   .map(rule => this.translateService.instant(rule))
  //   .join('\n');

     this.formControls = [
    {
      formControlName: 'newPassword',
      type: InputType.Password,
      id: 'newPassword',
      name: 'newPassword',
      label: 'LOGIN_PAGE.NEW_PASSWORD',
      placeholder: 'LOGIN_PAGE.NEW_PASSWORD',
      isRequired: true,
      class: 'col-md-6 offset-6',
      minLength: 8,
      maxLength: 50,
      showStrengthIndicator:true,
      tooltipText: this.tooltipText
    },
    {
      formControlName: 'confirmPassword',
      type: InputType.Password,
      id: 'confirmPassword',
      name: 'confirmPassword',
      label: 'LOGIN_PAGE.CONFIRM_PASSWORD',
      placeholder: 'LOGIN_PAGE.CONFIRM_PASSWORD',
      isRequired: true,
      class: 'col-md-6',
      minLength: 8,
      maxLength: 50,
      tooltipText: this.tooltipText
    },

  ];
    this.initForm()

  }

    private buildTooltipText(): void {
    this.tooltipText =
      `${this.translateService.instant('LOGIN_PAGE.PASSWORD_RULES.RULE_1')}\n` +
      `${this.translateService.instant('LOGIN_PAGE.PASSWORD_RULES.RULE_2')}\n` +
      `${this.translateService.instant('LOGIN_PAGE.PASSWORD_RULES.RULE_3')}\n` +
      `${this.translateService.instant('LOGIN_PAGE.PASSWORD_RULES.RULE_4')}\n` +
      `${this.translateService.instant('LOGIN_PAGE.PASSWORD_RULES.RULE_5')}\n` +
      `${this.translateService.instant('LOGIN_PAGE.PASSWORD_RULES.RULE_6')}`;
  }

  changeLanguage(): void {
    this.currentLang =
      this.currentLang === LanguageEnum.en ? LanguageEnum.ar : LanguageEnum.en;
      this.languageService.switchLang(this.currentLang,true);
  }

  onSubmit(): void {
    debugger
    this.isFormSubmitted = true;

    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }

    const newPassword = this.formGroup.get('newPassword')?.value;
    const confirmPassword = this.formGroup.get('confirmPassword')?.value;

    if (newPassword !== confirmPassword) {
      this.formGroup.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return;
    } else {
      this.formGroup.get('confirmPassword')?.setErrors(null);
    }

    const userId = localStorage.getItem('userId');
    const obj = {
      userId: Number(userId),
      ...this.formGroup.value
    };

    this.userManagementService.setNewPasswordForUser(obj).subscribe({
      next: (data: any) => {
        this.errorModalService.showSuccess(
          this.translateService.instant('LOGIN_PAGE.CHANGE_PASSWORD_SUCCESSFUL')
        );
        this.authService.logout();
      },
      error: (error) => {
        console.error('Error changing password', error);
      }
    });
  }




  private initForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }
      formGroup[control.formControlName] = [null, validators];
    });

    this.formGroup = this.formBuilder.group(formGroup);
  }

}
