// Dashboard Notifications Component - Pixel Perfect Design
// Based on reference mockup with exact measurements and colors

.dashboard-notifications {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border: 1px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  // Loading State
  .notifications-loading {
    .spinner-border {
      width: 2rem;
      height: 2rem;
      color: #007bff;
    }
  }

  // Notifications Container
  .notifications-container {
    height: 100%;
    display: flex;
    flex-direction: column;
   border: 1px solid #EAEEF1;;
    // Tab Header
    .notifications-header {
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 0;

      .tab-navigation {
        display: flex;
        width: 100%;

        .tab-btn {
          flex: 1;
          background: transparent;
          border: none;
          padding: 16px 20px;
          font-size: 14px;
          font-weight: 500;
          color: #6c757d;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;

          &:hover {
            background: #F8FAFC;
            color: #007bff;
          }

          &.active {
            background: #ffffff;
            color: #00205A;
            border-bottom: 2px solid #00205A;

            &::after {
              content: '';
              position: absolute;
              bottom: -1px;
              left: 0;
              right: 0;
              height: 4px;
              background: #00205A;
            }
          }

          .tab-text {
            font-weight: 500;
          }

          .tab-count {
            font-size: 12px;
            color: #6c757d;
            font-weight: 400;
          }

          &.active .tab-count {
            color: #007bff;
          }
        }
      }
    }

    // Content Area
    .notifications-content {
      flex: 1;
      overflow-y: auto;
      padding: 0;

      // Empty State
      .empty-state {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        padding: 20px;

        .empty-state-content {
          text-align: center;

          i {
            opacity: 0.5;
            color: #6c757d;
          }

          h6, p {
            margin: 0;
            color: #6c757d;
          }

          h6 {
            font-size: 14px;
            font-weight: 500;
          }

          p {
            font-size: 12px;
          }
        }
      }

      // Notifications List
      .notifications-list {
        .notification-item {
          display: flex;
          align-items: center;
          padding: 16px 20px;
          border-bottom: 1px solid transparent;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          background-color: #F8FAFC;

          &:hover {
          background-color: #F8FAFC;
          }

          &:last-child {
            border-bottom: none;
          }

          &.unread {
          background-color: #F8FAFC;
            border-left: 3px solid transparent;

            &::before {
              content: '';
              position: absolute;
              left: 8px;
              top: 50%;
              transform: translateY(-50%);
              width: 6px;
              height: 6px;
              background: #007bff;
              border-radius: 50%;
            }
          }

          // Status Indicator (Right side in RTL)
          .status-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
            margin-left: 16px;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin-bottom: 4px;
            }

            .status-text {
              font-size: 11px;
              color: #6c757d;
              text-align: center;
              line-height: 1.2;
              white-space: nowrap;
            }
          }

          // Content (Center)
          .item-content {
            flex: 1;
            text-align: center;
            padding: 0 16px;

            .fund-name {
              font-size: 13px;
              font-weight: 500;
              color: #2c3e50;
              line-height: 1.3;
              margin: 0;
            }
          }

          // Action/Type (Left side in RTL)
          .item-action {
            min-width: 80px;
            text-align: left;
            margin-right: 16px;

            .action-text {
              font-size: 12px;
              color: #007bff;
              font-weight: 500;
              white-space: nowrap;
            }
          }
        }
      }
    }

    // Footer Actions
    .notifications-footer {
      background: #F8FAFC;
      border-top: 1px solid #e9ecef;
      padding: 12px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 12px;

      .footer-btn {
        background: transparent;
        border: none;
        color: #007bff;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 4px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          background: rgba(0, 123, 255, 0.1);
          color: #0056b3;
        }

        i {
          font-size: 11px;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-notifications {
    .notifications-container {
      .notifications-header {
        .tab-navigation {
          .tab-btn {
            padding: 14px 16px;
            font-size: 13px;

            .tab-text {
              font-size: 13px;
            }

            .tab-count {
              font-size: 11px;
            }
          }
        }
      }

      .notifications-content {
        .notifications-list {
          .notification-item {
            padding: 14px 16px;

            .status-indicator {
              min-width: 70px;
              margin-left: 12px;

              .status-text {
                font-size: 10px;
              }
            }

            .item-content {
              padding: 0 12px;

              .fund-name {
                font-size: 12px;
              }
            }

            .item-action {
              min-width: 70px;
              margin-right: 12px;

              .action-text {
                font-size: 11px;
              }
            }
          }
        }
      }

      .notifications-footer {
        padding: 10px 16px;

        .footer-btn {
          font-size: 11px;
          padding: 5px 10px;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .dashboard-notifications {
    .notifications-container {
      .notifications-content {
        .notifications-list {
          .notification-item {
            &.unread {
              border-left: none;
              border-right: 3px solid #f8f9fa;

              &::before {
                left: auto;
                right: 8px;
              }
            }

            .status-indicator {
              margin-left: 0;
              margin-right: 16px;
            }

            .item-action {
              text-align: right;
              margin-right: 0;
              margin-left: 16px;
            }
          }
        }
      }

      .notifications-footer {
        flex-direction: row-reverse;
      }
    }
  }
}

// Print Styles
@media print {
  .dashboard-notifications {
    .notifications-container {
      .notifications-header {
        .tab-navigation {
          .tab-btn {
            &:not(.active) {
              display: none;
            }
          }
        }
      }

      .notifications-footer {
        display: none;
      }
    }
  }
}
