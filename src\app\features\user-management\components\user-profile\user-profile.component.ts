import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, of, Subject, takeUntil } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { FilePreviewService } from '@core/services/file-preview.service';

// Shared imports
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ErrorModalService } from '@core/services/error-modal.service';

// Core imports
import {
  UserManagementServiceProxy,
  UserProfileResponseDtoBaseResponse,
} from '@core/api/api.generated';

// Enums and interfaces
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// Validators
import {
  saudiMobileValidator,
  saudiIbanValidator,
  saudiPassportValidator,
} from '@shared/validators/saudi-validators';
import { FileUploadService } from '@shared/services/file.service';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { AttachmentModule } from '@shared/enum/AttachmentModule';
import { SizeEnum } from '@core/enums/size';
import { BreadcrumbService } from '@core/gl-services/breadcrumb-services/breadcrumb.service';

@Component({
  selector: 'app-user-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    BreadcrumbComponent,
    PageHeaderComponent,
    CustomButtonComponent,
    ChangePasswordComponent,

  ],
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss'],
})
export class UserProfileComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  userProfileForm!: FormGroup;
  formControls: IControlOption[] = [];
  breadcrumbItems: IBreadcrumbItem[] = [];

  isLoading = false;
  isFormSubmitted = false;
  currentUserData: any = null;
  userId: number | null = null;
  isViewMode = false; // New property to control view/edit mode
  showChangePassword = false; // Property to control change password form visibility

  // Enums for template
  ButtonTypeEnum = ButtonTypeEnum;
    breadcrumbSizeEnum = SizeEnum;


  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private userManagementService: UserManagementServiceProxy,
    private errorModalService: ErrorModalService,
    private fileUploadService: FileUploadService,
    private filePreviewService: FilePreviewService,
    private breadcrumbService: BreadcrumbService
  ) {
    this.initializeBreadcrumbs();
  }

  ngOnInit(): void {
    this.getUserIdFromRoute();
    this.initializeForm();
    this.setupFormControls();
    this.loadUserData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getUserIdFromRoute(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.userId = Number(id);
      this.isViewMode = true; // If userId is provided, it's view mode
      this.updateBreadcrumbsForViewMode();
    } else {
      this.userId = null;
      this.isViewMode = false; // No userId means current user profile (edit mode)
    }
  }

  private initializeBreadcrumbs(): void {
    this.breadcrumbItems = [
      {
        label: 'COMMON.HOME',
        url: '/admin/dashboard',
      },
      {
        label: 'USER_PROFILE.PAGE_TITLE',
        disabled: true,
      },
    ];
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  private updateBreadcrumbsForViewMode(): void {
    this.breadcrumbItems = [
      {
        label: 'COMMON.HOME',
        url: '/admin/dashboard',
      },
      {
        label: 'USER_MANAGEMENT.TITLE',
        url: '/admin/user-management',
      },
      {
        label: 'USER_PROFILE.VIEW_TITLE',
        disabled: true,
      },
    ];
    this.breadcrumbService.setBreadcrumbData(this.breadcrumbItems);
  }

  private initializeForm(): void {
    this.userProfileForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(100)]],
      countryCode: ['+966', [Validators.required]], // Fixed to +966 for Saudi numbers
      userName: ['', this.isViewMode ? [] : [Validators.required, saudiMobileValidator()]], // Mobile validation in edit mode
      iban: ['', [saudiIbanValidator()]], // Optional field with Saudi IBAN validation
      nationality: ['', [Validators.maxLength(50)]], // Optional field with max length
      cv: [''],
      personalPhoto: [''],
      passportNo: ['', this.isViewMode ? [] : [Validators.required, saudiPassportValidator()]], // Required in edit mode
      status: [''], // Read-only field
      role: [''], // Read-only field
    });
  }

  private setupFormControls(): void {
    this.formControls = [
      // Personal Photo Section
      {
        formControlName: 'personalPhoto',
        type: InputType.file,
        id: 'personalPhoto',
        name: 'personalPhoto',
        label: 'USER_PROFILE.PERSONAL_PHOTO',
        placeholder: '',
        isRequired: false,
        class: 'col-12 photo-upload-section',
        allowedTypes: ['jpg', 'jpeg', 'png'],
        max: 2,
        isReadonly: this.isViewMode,
        moduleId : AttachmentModule.User
      },

      // Basic Information Section
      {
        formControlName: 'name',
        type: InputType.Text,
        id: 'name',
        name: 'name',
        label: 'USER_PROFILE.NAME',
        placeholder: 'USER_PROFILE.NAME_PLACEHOLDER',
        isRequired: !this.isViewMode,
        class: 'col-md-6',
        maxLength: 100,
        isReadonly: this.isViewMode,
      },
      {
        formControlName: 'email',
        type: InputType.Email,
        id: 'email',
        name: 'email',
        label: 'USER_PROFILE.EMAIL',
        placeholder: 'USER_PROFILE.EMAIL_PLACEHOLDER',
        isRequired: !this.isViewMode,
        class: 'col-md-6',
        maxLength: 100,
        isReadonly: this.isViewMode,
      },

      // Contact Information
      {
        formControlName: 'countryCode',
        type: InputType.Text,
        id: 'countryCode',
        name: 'countryCode',
        label: 'USER_PROFILE.COUNTRY_CODE',
        placeholder: '',
        class: 'col-md-1',
        maxLength: 5,
        isReadonly: true, // Always readonly
      },
      {
        formControlName: 'username',
        type: InputType.Mixed,
        id: 'username',
        name: 'username',
        label: 'USER_PROFILE.MOBILE',
        placeholder: 'USER_PROFILE.MOBILE_PLACEHOLDER',
        isRequired: !this.isViewMode,
        class: 'col-md-5',
        maxLength: 11, // 10 digits for Saudi mobile format
        //pattern: '^(05|5)(5|0|3|6|4|9|1|8|7)([0-9]{8})$', // Saudi mobile pattern
        isReadonly: this.isViewMode,
      },
      {
        formControlName: 'iban',
        type: InputType.Mixed,
        id: 'iban',
        name: 'iban',
        label: 'USER_PROFILE.IBAN',
        placeholder: 'USER_PROFILE.IBAN_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        maxLength: 24, // SA + 22 digits
        pattern: '^SA[0-9]{22}$', // Pattern hint for UI
        isReadonly: this.isViewMode,
      },
      {
        formControlName: 'nationality',
        type: InputType.Text,
        id: 'nationality',
        name: 'nationality',
        label: 'USER_PROFILE.NATIONALITY',
        placeholder: 'USER_PROFILE.NATIONALITY_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        maxLength: 100,
        isReadonly: this.isViewMode,
      },

      // Document Upload
      {
        formControlName: 'cv',
        type: InputType.file,
        id: 'cv',
        name: 'cv',
        label: 'USER_PROFILE.CV',
        placeholder: '',
        isRequired: false,
        class: 'col-md-6',
        allowedTypes: ['pdf', 'doc', 'docx'],
        max: 10, // 10MB max for CV
        isReadonly: this.isViewMode,
        moduleId : AttachmentModule.User
      },
      {
        formControlName: 'passportNo',
        type: InputType.Mixed,
        id: 'passportNo',
        name: 'passportNo',
        label: 'USER_PROFILE.PASSPORT_NO',
        placeholder: 'USER_PROFILE.PASSPORT_NO_PLACEHOLDER',
        isRequired: !this.isViewMode,
        class: 'col-md-6',
        maxLength: 9, // 1 letter + 8 digits
        pattern: '^[A-Z][0-9]{8}$', // Pattern hint for UI
        isReadonly: this.isViewMode,
      },

      // Read-only Status Information
      {
        formControlName: 'status',
        type: InputType.Text,
        id: 'status',
        name: 'status',
        label: 'USER_PROFILE.STATUS',
        placeholder: '',
        isRequired: false,
        class: 'col-md-6',
        isReadonly: true, // Always readonly
      },
      {
        formControlName: 'role',
        type: InputType.Text,
        id: 'role',
        name: 'role',
        label: 'USER_PROFILE.ROLE',
        placeholder: '',
        isRequired: false,
        class: 'col-md-6',
        isReadonly: true, // Always readonly
      },
    ];
  }

  loadUserData(): void {
    this.isLoading = true;

    // Use getUserProfile with userId parameter as requested
    // If userId is null, it will get current user profile
    this.userManagementService
      .getUserProfile(this.userId || undefined)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error('Error loading user profile:', error);
          this.errorModalService.showError('USER_PROFILE.LOAD_ERROR');
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((response) => {
        if (response) {
          this.populateForm(response);
        }
      });
  }

  private populateForm(response: UserProfileResponseDtoBaseResponse): void {
    this.currentUserData = response;

    try {
      const user = response.data;

      this.userProfileForm.patchValue({
        name: user.fullName || '',
        email: user.email || '',
        username: user.userName || '',
        iban: user.iban || '',
        nationality: user.nationality || '',
        passportNo: user.passportNo || '',
        status: user.isActive ? 'Active' : 'Inactive',
        role: Array.isArray(user.roles) ? user.roles.map((role: any) => role?.name).join(', ') : '',
      });

      // If in view mode, disable all form controls
      if (this.isViewMode) {
        this.userProfileForm.disable();
      }

      console.log('Form populated successfully with user data:', user);
    } catch (error) {
      console.error('Error populating form:', error);
      this.errorModalService.showError('USER_PROFILE.POPULATE_ERROR');
    }
  }

  onValueChange(_event: any, _control: IControlOption): void {
    // Handle value changes if needed
  }

  onKeyPressed(_event: any, _control: IControlOption): void {
    // Handle key press events if needed
  }

  onDropdownChange(_event: any, _control: IControlOption): void {
    // Handle dropdown changes if needed
  }

  onFileUploaded(event: any): void {
    const { file, control } = event;
    if (file && control) {
      const controlName = control.formControlName;

      // Validate file size and type
      if (
        control.allowedTypes &&
        !control.allowedTypes.some((type: string) =>
          file.name.toLowerCase().endsWith(`.${type.toLowerCase()}`)
        )
      ) {
        this.errorModalService.showError(
          `Invalid file type for ${controlName}. Allowed types: ${control.allowedTypes.join(
            ', '
          )}`
        );
        return;
      }

      if (control.max && file.size > control.max * 1024 * 1024) {
        this.errorModalService.showError(
          `File size exceeds maximum allowed size (${control.max}MB) for ${controlName}`
        );
        return;
      }

      // Set the file in the form control
      this.userProfileForm.get(controlName)?.setValue(file);

      console.log(`File uploaded for ${controlName}:`, file);
    }
  }

  onSubmit(): void {
    this.isFormSubmitted = true;

    if (this.userProfileForm.valid) {
      this.isLoading = true;

      // Prepare form data for API call
      const formData = this.prepareFormData();

      // Call update profile API
      this.userManagementService
        .updateUserProfile(formData)
        .pipe(
          takeUntil(this.destroy$),
          catchError((error) => {
            console.error('Error updating profile:', error);
            this.errorModalService.showError('USER_PROFILE.UPDATE_ERROR');
            return of(null);
          }),
          finalize(() => {
            this.isLoading = false;
          })
        )
        .subscribe((response) => {
          if (response && response.successed) {
            // Show success message and reload data
            this.errorModalService.showSuccess('USER_PROFILE.UPDATE_SUCCESS');
            this.loadUserData();
            this.isFormSubmitted = false;
          }
        });
    } else {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.userProfileForm);
      this.errorModalService.showError('USER_PROFILE.VALIDATION_ERROR');
    }
  }

  onCancel(): void {
    if (this.isViewMode) {
      // If in view mode, go back to user management
      this.router.navigate(['/admin/user-management']);
    } else {
      // If in edit mode (current user profile), go to dashboard
      this.router.navigate(['/admin/dashboard']);
    }
  }

  onToggleChangePassword(): void {
    this.showChangePassword = true;
  }

  onPasswordChanged(): void {
    // Password was successfully changed
    this.showChangePassword = false;
    // Optionally show a success message or refresh data
  }

  onChangePasswordCancelled(): void {
    // User cancelled password change
    this.showChangePassword = false;
  }

  // Helper method to check if form should be editable
  isFormEditable(): boolean {
    return !this.isViewMode;
  }

  private prepareFormData(): any {
    const formValue = this.userProfileForm.value;

    // Prepare data according to API requirements
    return {
      fullName: formValue.name,
      email: formValue.email,
      userName: formValue.userName,
      iban: formValue.iban,
      nationality: formValue.nationality,
      passportNo: formValue.passportNo,
      // Add other fields as needed
    };
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  // Get user photo URL with fallback to default avatar
  getUserPhotoUrl(): string {
    if (this.currentUserData?.data?.personalPhoto?.filePath) {
      // If the photo is a full URL, use it directly
      if (this.currentUserData?.data?.personalPhoto?.filePath.startsWith('http')) {
        return this.currentUserData?.data?.personalPhoto?.filePath;
      }

      // If it's a relative path, construct the full URL using environment API URL
      // Check if it already starts with a slash
      const photoPath = this.currentUserData?.data?.personalPhoto?.filePath.startsWith(
        '/'
      )
        ? this.currentUserData?.data?.personalPhoto?.filePath
        : `/${this.currentUserData?.data?.personalPhoto?.filePath}`;

      return `${environment.apiUrl}${photoPath}`;
    }
    // Fallback to default avatar
    return 'assets/images/avatar-member.png';
  }

  // Handle image load error - fallback to default avatar
  onImageError(event: any): void {
    event.target.src = 'assets/images/avatar-member.png';
  }

  // Preview/Download CV file
  downloadFile(): void {
    const cvFile = this.currentUserData?.data?.cvFile;

    if (!cvFile) {
      console.warn('No CV file available');
      return;
    }

    // Try preview first using attachment ID if available
    if (cvFile.id) {
      const fileName = this.extractFileNameFromUrl(cvFile.filePath) || 'CV.pdf';
      this.filePreviewService.previewFileById(cvFile.id, fileName, () => {
        this.fallbackDownload(cvFile);
      });
    } else {
      // Fallback to direct download if no attachment ID
      this.fallbackDownload(cvFile);
    }
  }

  private fallbackDownload(cvFile: any): void {
    const filePath = cvFile.filePath;
    let fullUrl: string;
    let fileName: string;

    // Determine the full URL for the CV file
    if (filePath.startsWith('http')) {
      // If it's already a full URL, use it directly
      fullUrl = filePath;
    } else {
      // If it's a relative path, construct the full URL
      const path = filePath.startsWith('/') ? filePath : `/${filePath}`;
      fullUrl = `${environment.apiUrl}${path}`;
    }

    // Extract filename from the URL or use a default name
    fileName = this.extractFileNameFromUrl(filePath) || 'CV.pdf';

    // Create download link
    const link = document.createElement('a');
    link.href = fullUrl;
    link.target = '_blank'; // Open in new tab as fallback
    link.download = fileName; // Set download filename

    // Add to DOM temporarily to trigger download
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
  }

  // Helper method to extract filename from URL
  private extractFileNameFromUrl(url: string): string | null {
    try {
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1];

      // If filename has an extension, return it
      if (fileName && fileName.includes('.')) {
        return fileName;
      }

      return null;
    } catch (error) {
      console.error('Error extracting filename from URL:', error);
      return null;
    }
  }

   onBreadcrumbClicked(event: any): void {
    if (event?.url) {
      this.router.navigateByUrl(event.url);
    }
  }
}
