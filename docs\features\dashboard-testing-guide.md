# 🧪 Dashboard Testing Guide

## 📋 Overview

This document provides comprehensive testing guidelines, strategies, and examples for the Dashboard feature in the Jadwa Investment Web Application.

## 🎯 Testing Strategy

### 🔬 Testing Pyramid
```
    /\
   /  \     E2E Tests (Few)
  /____\    - User workflows
 /      \   - Integration scenarios
/________\  
           Unit Tests (Many)
           - Components
           - Services  
           - Utilities
```

### 📊 Coverage Goals
- **Unit Tests**: >90% code coverage
- **Integration Tests**: Critical user paths
- **Accessibility Tests**: WCAG 2.1 AA compliance
- **Performance Tests**: Loading and rendering metrics

## 🔧 Unit Testing

### 📱 Component Testing

#### 🎛️ Dashboard Component Tests
**File**: `dashboard.component.spec.ts`

**Test Categories**:
```typescript
describe('DashboardComponent', () => {
  // 1. Component Creation
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // 2. Initialization
  it('should initialize with default values', () => {
    expect(component.loading).toBeFalse();
    expect(component.errorMessage).toBe('');
    expect(component.dashboardData).toBeNull();
  });

  // 3. Data Loading
  it('should load dashboard data on init', () => {
    component.ngOnInit();
    expect(dashboardService.getAutoDashboard).toHaveBeenCalled();
    expect(component.dashboardData).toBeTruthy();
  });

  // 4. Error Handling
  it('should handle error when loading dashboard data', () => {
    dashboardService.getAutoDashboard.and.returnValue(throwError(() => new Error('API Error')));
    component.ngOnInit();
    expect(component.errorMessage).toBe('DASHBOARD.ERROR.LOAD_FAILED');
  });

  // 5. User Interactions
  it('should refresh dashboard data', () => {
    spyOn(component, 'loadDashboardData' as any);
    component.onRefresh();
    expect(component['loadDashboardData']).toHaveBeenCalled();
  });
});
```

#### 📊 Box Info Card Component Tests
**File**: `box-info-card.component.spec.ts`

**Test Categories**:
```typescript
describe('BoxInfoCardComponent', () => {
  // 1. Input Properties
  it('should display title and value', () => {
    component.title = 'Test Title';
    component.value = 42;
    fixture.detectChanges();

    const titleElement = fixture.debugElement.query(By.css('.card-title'));
    const valueElement = fixture.debugElement.query(By.css('.card-value'));

    expect(titleElement.nativeElement.textContent.trim()).toBe('Test Title');
    expect(valueElement.nativeElement.textContent.trim()).toBe('42');
  });

  // 2. Computed Properties
  it('should return correct card color class', () => {
    component.color = 'warning';
    expect(component.cardColorClass).toBe('card-warning');
  });

  // 3. Conditional Rendering
  it('should display trend section when trend is not neutral', () => {
    component.trend = 'up';
    component.trendValue = '+5%';
    fixture.detectChanges();

    const trendSection = fixture.debugElement.query(By.css('.trend-section'));
    expect(trendSection).toBeTruthy();
  });

  // 4. Accessibility
  it('should have proper ARIA attributes', () => {
    component.title = 'Test KPI';
    component.value = 100;
    fixture.detectChanges();

    const cardElement = fixture.debugElement.query(By.css('.box-info-card'));
    expect(cardElement.nativeElement.getAttribute('aria-label')).toBe('Test KPI: 100');
  });
});
```

### 🔧 Service Testing

#### 📡 Dashboard Service Tests
**File**: `dashboard.service.spec.ts`

**Test Categories**:
```typescript
describe('DashboardService', () => {
  // 1. API Calls
  it('should call autoDashboard with correct parameters', () => {
    const fromDate = new Date('2023-01-01');
    const toDate = new Date('2023-12-31');
    
    service.getAutoDashboard(fromDate, toDate, 15).subscribe();
    
    expect(dashboardProxy.autoDashboard).toHaveBeenCalledWith(
      DateTime.fromJSDate(fromDate),
      DateTime.fromJSDate(toDate),
      15
    );
  });

  // 2. Error Handling
  it('should handle network errors', () => {
    const networkError = new Error('Network Error');
    dashboardProxy.autoDashboard.and.returnValue(throwError(() => networkError));

    service.getAutoDashboard().subscribe({
      error: (error) => {
        expect(error).toBe(networkError);
        expect(errorModalService.showError).toHaveBeenCalledWith(networkError);
      }
    });
  });

  // 3. Caching Behavior
  it('should use shareReplay for caching', () => {
    const observable = service.getAutoDashboard();
    
    observable.subscribe();
    observable.subscribe();

    expect(dashboardProxy.autoDashboard).toHaveBeenCalledTimes(1);
  });
});
```

#### 🛠️ Dashboard Utils Service Tests
**File**: `dashboard-utils.service.spec.ts`

**Test Categories**:
```typescript
describe('DashboardUtilsService', () => {
  // 1. Data Transformation
  it('should convert KPIs to card configurations', () => {
    const kpis: DashboardKPIsDto = {
      totalFunds: 10,
      activeFunds: 8,
      totalResolutions: 25,
      pendingResolutions: 5
    };

    const cards = service.convertKPIsToCards(kpis);

    expect(cards.length).toBeGreaterThan(0);
    expect(cards[0]).toEqual({
      title: 'DASHBOARD.KPI.TOTAL_FUNDS',
      value: 10,
      icon: 'fas fa-briefcase',
      color: 'primary',
      route: '/admin/investment-funds'
    });
  });

  // 2. Edge Cases
  it('should return empty array for null KPIs', () => {
    const cards = service.convertKPIsToCards(null as any);
    expect(cards).toEqual([]);
  });

  // 3. Utility Functions
  it('should format large numbers with M suffix', () => {
    expect(service.formatNumber(1500000)).toBe('1.5M');
    expect(service.formatNumber(2000000)).toBe('2.0M');
  });
});
```

## 🎭 Mock Data Strategy

### 📊 Comprehensive Mock Data
```typescript
// Mock Dashboard Response
export const mockDashboardResponse: BaseDashboardResponseBaseResponse = {
  statusCode: 200,
  successed: true,
  message: 'Success',
  data: {
    userName: 'Test User',
    userRole: 'FundManager',
    generatedAt: DateTime.fromJSDate(new Date('2023-06-15T10:30:00Z')),
    kpIs: {
      totalFunds: 10,
      activeFunds: 8,
      totalResolutions: 25,
      pendingResolutions: 5,
      totalMembers: 15,
      totalMeetings: 12,
      totalAssessments: 8
    },
    recentNotifications: [
      {
        id: 1,
        title: 'Test Notification',
        body: 'Test notification body',
        createdAt: DateTime.fromJSDate(new Date()),
        isRead: false,
        notificationType: 'system',
        fundId: 123
      }
    ],
    recentActivities: [
      {
        id: 1,
        description: 'Fund created successfully',
        activityType: 'fund_created',
        occurredAt: DateTime.fromJSDate(new Date()),
        userName: 'Test User',
        fundId: 123
      }
    ],
    quickActions: []
  },
  errors: []
};

// Mock Error Response
export const mockErrorResponse = {
  statusCode: 500,
  successed: false,
  message: 'Internal Server Error',
  data: null,
  errors: ['Database connection failed']
};
```

### 🔧 Service Mocking
```typescript
// Dashboard Service Mock
export const createMockDashboardService = () => {
  return jasmine.createSpyObj('DashboardService', [
    'getAutoDashboard',
    'getFundManagerDashboard',
    'getDashboardKPIs',
    'getRecentNotifications',
    'getRecentActivities'
  ]);
};

// Utils Service Mock
export const createMockDashboardUtils = () => {
  return jasmine.createSpyObj('DashboardUtilsService', [
    'createDashboardViewModel',
    'convertKPIsToCards',
    'enhanceNotifications',
    'enhanceActivities',
    'formatNumber',
    'getDashboardGreeting'
  ]);
};
```

## 🎯 Integration Testing

### 🔄 Component Integration Tests
```typescript
describe('Dashboard Integration', () => {
  it('should display KPI cards when data is loaded', fakeAsync(() => {
    // Setup
    dashboardService.getAutoDashboard.and.returnValue(of(mockDashboardResponse));
    
    // Execute
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    
    // Verify
    const kpiCards = fixture.debugElement.queryAll(By.css('app-box-info-card'));
    expect(kpiCards.length).toBeGreaterThan(0);
  }));

  it('should handle refresh workflow', fakeAsync(() => {
    // Setup
    let callCount = 0;
    dashboardService.getAutoDashboard.and.callFake(() => {
      callCount++;
      return of(mockDashboardResponse);
    });
    
    // Execute
    component.ngOnInit();
    tick();
    component.onRefresh();
    tick();
    
    // Verify
    expect(callCount).toBe(2);
  }));
});
```

### 🌐 API Integration Tests
```typescript
describe('API Integration', () => {
  it('should handle successful API response', () => {
    dashboardProxy.autoDashboard.and.returnValue(of(mockDashboardResponse));
    
    service.getAutoDashboard().subscribe(response => {
      expect(response.successed).toBeTrue();
      expect(response.data).toBeTruthy();
    });
  });

  it('should handle API error response', () => {
    dashboardProxy.autoDashboard.and.returnValue(throwError(() => mockErrorResponse));
    
    service.getAutoDashboard().subscribe({
      error: (error) => {
        expect(error).toBe(mockErrorResponse);
        expect(errorModalService.showError).toHaveBeenCalled();
      }
    });
  });
});
```

## ♿ Accessibility Testing

### 🎯 ARIA Testing
```typescript
describe('Accessibility', () => {
  it('should have proper ARIA labels for loading state', () => {
    component.loading = true;
    fixture.detectChanges();

    const loadingContainer = fixture.debugElement.query(By.css('.loading-container'));
    expect(loadingContainer.nativeElement.getAttribute('role')).toBe('status');
    expect(loadingContainer.nativeElement.getAttribute('aria-label')).toBeTruthy();
  });

  it('should have proper heading hierarchy', () => {
    component.ngOnInit();
    fixture.detectChanges();

    const headings = fixture.debugElement.queryAll(By.css('h1, h2, h3, h4, h5, h6'));
    expect(headings.length).toBeGreaterThan(0);
    
    // Verify heading levels are sequential
    const levels = headings.map(h => parseInt(h.nativeElement.tagName.charAt(1)));
    expect(levels).toEqual(levels.sort());
  });

  it('should support keyboard navigation', () => {
    fixture.detectChanges();
    
    const focusableElements = fixture.debugElement.queryAll(
      By.css('button, [tabindex="0"], input, select, textarea, a[href]')
    );
    
    expect(focusableElements.length).toBeGreaterThan(0);
    
    focusableElements.forEach(element => {
      expect(element.nativeElement.tabIndex).toBeGreaterThanOrEqual(0);
    });
  });
});
```

### 🔍 Screen Reader Testing
```typescript
describe('Screen Reader Support', () => {
  it('should have meaningful alt text for images', () => {
    fixture.detectChanges();
    
    const images = fixture.debugElement.queryAll(By.css('img'));
    images.forEach(img => {
      const altText = img.nativeElement.getAttribute('alt');
      expect(altText).toBeTruthy();
      expect(altText.length).toBeGreaterThan(0);
    });
  });

  it('should have proper form labels', () => {
    fixture.detectChanges();
    
    const inputs = fixture.debugElement.queryAll(By.css('input, select, textarea'));
    inputs.forEach(input => {
      const id = input.nativeElement.id;
      if (id) {
        const label = fixture.debugElement.query(By.css(`label[for="${id}"]`));
        expect(label).toBeTruthy();
      }
    });
  });
});
```

## 📱 Responsive Testing

### 📐 Viewport Testing
```typescript
describe('Responsive Design', () => {
  it('should adapt to mobile viewport', () => {
    // Simulate mobile viewport
    Object.defineProperty(window, 'innerWidth', { value: 375 });
    Object.defineProperty(window, 'innerHeight', { value: 667 });
    window.dispatchEvent(new Event('resize'));
    
    fixture.detectChanges();
    
    const mobileElements = fixture.debugElement.queryAll(By.css('.d-block.d-md-none'));
    expect(mobileElements.length).toBeGreaterThan(0);
  });

  it('should show desktop layout on large screens', () => {
    // Simulate desktop viewport
    Object.defineProperty(window, 'innerWidth', { value: 1920 });
    Object.defineProperty(window, 'innerHeight', { value: 1080 });
    window.dispatchEvent(new Event('resize'));
    
    fixture.detectChanges();
    
    const desktopElements = fixture.debugElement.queryAll(By.css('.d-none.d-md-block'));
    expect(desktopElements.length).toBeGreaterThan(0);
  });
});
```

## 🚀 Performance Testing

### ⚡ Loading Performance
```typescript
describe('Performance', () => {
  it('should load within acceptable time', fakeAsync(() => {
    const startTime = performance.now();
    
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    
    const endTime = performance.now();
    const loadTime = endTime - startTime;
    
    expect(loadTime).toBeLessThan(1000); // 1 second
  }));

  it('should handle large datasets efficiently', () => {
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      title: `Item ${i}`,
      value: i * 10
    }));
    
    component.kpiCards = largeDataset;
    
    const startTime = performance.now();
    fixture.detectChanges();
    const endTime = performance.now();
    
    expect(endTime - startTime).toBeLessThan(100); // 100ms
  });
});
```

## 🔧 Test Utilities

### 🛠️ Helper Functions
```typescript
// Test Helper Functions
export class DashboardTestHelpers {
  static createMockDateTime(date: string): DateTime {
    return DateTime.fromJSDate(new Date(date));
  }
  
  static triggerResize(width: number, height: number): void {
    Object.defineProperty(window, 'innerWidth', { value: width });
    Object.defineProperty(window, 'innerHeight', { value: height });
    window.dispatchEvent(new Event('resize'));
  }
  
  static waitForAsync(fixture: ComponentFixture<any>): Promise<void> {
    return fixture.whenStable().then(() => fixture.detectChanges());
  }
  
  static getByTestId(fixture: ComponentFixture<any>, testId: string): DebugElement {
    return fixture.debugElement.query(By.css(`[data-testid="${testId}"]`));
  }
}
```

### 📊 Custom Matchers
```typescript
// Custom Jasmine Matchers
beforeEach(() => {
  jasmine.addMatchers({
    toHaveAccessibleName: () => ({
      compare: (actual: HTMLElement, expected: string) => {
        const accessibleName = actual.getAttribute('aria-label') || 
                              actual.getAttribute('aria-labelledby') ||
                              actual.textContent?.trim();
        
        return {
          pass: accessibleName === expected,
          message: `Expected element to have accessible name "${expected}" but got "${accessibleName}"`
        };
      }
    })
  });
});
```

## 🎯 Test Execution

### 🏃‍♂️ Running Tests
```bash
# Run all tests
ng test

# Run tests with coverage
ng test --code-coverage

# Run tests in watch mode
ng test --watch

# Run specific test file
ng test --include="**/dashboard.component.spec.ts"

# Run tests with specific configuration
ng test --browsers=ChromeHeadless --watch=false
```

### 📊 Coverage Reports
```bash
# Generate coverage report
ng test --code-coverage --watch=false

# View coverage report
open coverage/index.html
```

## 🔮 Advanced Testing

### 🎭 Visual Regression Testing
```typescript
// Using tools like Percy or Chromatic
describe('Visual Regression', () => {
  it('should match dashboard snapshot', async () => {
    component.ngOnInit();
    fixture.detectChanges();
    
    await percySnapshot('Dashboard - Default State');
  });
  
  it('should match loading state snapshot', async () => {
    component.loading = true;
    fixture.detectChanges();
    
    await percySnapshot('Dashboard - Loading State');
  });
});
```

### 🔄 End-to-End Testing
```typescript
// Using Playwright or Cypress
describe('Dashboard E2E', () => {
  it('should load dashboard and display data', async () => {
    await page.goto('/admin/dashboard');
    await page.waitForSelector('.dashboard-content');
    
    const kpiCards = await page.locator('app-box-info-card').count();
    expect(kpiCards).toBeGreaterThan(0);
  });
  
  it('should refresh data when refresh button is clicked', async () => {
    await page.goto('/admin/dashboard');
    await page.click('[aria-label="Refresh"]');
    await page.waitForSelector('.loading-container');
    await page.waitForSelector('.dashboard-content');
  });
});
```
