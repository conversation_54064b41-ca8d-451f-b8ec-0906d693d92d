# Assessment API 400 Bad Request Error - Fix Documentation

## 🚨 Issue Summary

**Error**: `400 Bad Request` when calling `https://localhost:7010/api/Assessment/GetById`
**Root Cause**: NSwag-generated API client has incorrect URL construction for the `getById` method
**Impact**: Assessment details and edit functionality not working

## 🔍 Problem Analysis

### Current Generated Code (Incorrect)
```typescript
getById(id: number): Observable<AssessmentByIdDtoBaseResponse> {
    let url_ = this.baseUrl + "/api/Assessment/GetById";
    if (id === undefined || id === null)
        throw new Error("The parameter 'id' must be defined.");
    url_ = url_.replace("{id}", encodeURIComponent("" + id)); // ❌ No {id} to replace
    url_ = url_.replace(/[?&]$/, "");
}
```

**Problem**: The URL template `/api/Assessment/GetById` doesn't contain `{id}` placeholder, but the code tries to replace it.

### Expected Backend API Format
Based on documentation in `docs/Sprint3Docs/edit-assessment-integration-guide.md`:
- **Endpoint**: `GET /api/Assessment/{id}` (RESTful style)
- **Example**: `GET /api/Assessment/123`

## ✅ Immediate Fix Applied

### Fixed Generated Code
```typescript
getById(id: number): Observable<AssessmentByIdDtoBaseResponse> {
    let url_ = this.baseUrl + "/api/Assessment/GetById/" + encodeURIComponent("" + id);
    if (id === undefined || id === null)
        throw new Error("The parameter 'id' must be defined.");
    url_ = url_.replace(/[?&]$/, "");
}
```

**Location**: `src/app/core/api/api.generated.ts` (lines 167-171)

## 🔧 Long-term Solution

### Option 1: Backend API Route Fix (Recommended)
Update the backend controller to match the expected URL pattern:

```csharp
[HttpGet("GetById/{id}")]
public async Task<AssessmentByIdDtoBaseResponse> GetById(int id)
{
    // Implementation
}
```

### Option 2: Backend API Route Alternative
If the backend expects query parameters:

```csharp
[HttpGet("GetById")]
public async Task<AssessmentByIdDtoBaseResponse> GetById([FromQuery] int id)
{
    // Implementation
}
```

Then the NSwag-generated code should be:
```typescript
getById(id: number): Observable<AssessmentByIdDtoBaseResponse> {
    let url_ = this.baseUrl + "/api/Assessment/GetById?id=" + encodeURIComponent("" + id);
    // ...
}
```

## 🔄 Regeneration Process

### After Backend Fix
1. **Update Backend**: Fix the controller route attribute
2. **Regenerate API**: Run `nswag run` to regenerate the client
3. **Verify Fix**: Check that the generated URL construction is correct
4. **Test**: Verify the API calls work correctly

### Command to Regenerate
```bash
npm run nswag
# or
nswag run
```

## 🧪 Testing the Fix

### Manual Testing
1. Navigate to Assessment Details: `/admin/investment-funds/assessments/details/123?fundId=14`
2. Navigate to Edit Assessment: `/admin/investment-funds/assessments/edit/123?fundId=14`
3. Check browser network tab for successful API calls

### Expected API Calls
- **Details**: `GET https://localhost:7010/api/Assessment/GetById/123`
- **Edit**: `GET https://localhost:7010/api/Assessment/GetById/123`

## 📋 Affected Components

### Components Using getById
1. **assessment-details.component.ts** (line 129)
2. **edit-assessment.component.ts** (line 269)

### API Integration Pattern
Both components follow the same pattern:
```typescript
this.assessmentServiceProxy.getById(this.currentAssessmentId)
  .pipe(takeUntil(this.destroy$))
  .subscribe({
    next: (response: AssessmentByIdDtoBaseResponse) => {
      if (response.successed && response.data) {
        // Handle success
      }
    },
    error: (error: any) => {
      // Handle error
    }
  });
```

## ⚠️ Important Notes

1. **Temporary Fix**: The current fix in `api.generated.ts` will be overwritten when `nswag run` is executed
2. **Backend Priority**: The backend API route should be fixed first
3. **Consistency**: Ensure all similar endpoints follow the same URL pattern
4. **Documentation**: Update API documentation to reflect the correct endpoint format

## 🔗 Related Files

- `src/app/core/api/api.generated.ts` - Generated API client
- `docs/Sprint3Docs/edit-assessment-integration-guide.md` - API documentation
- `nswag.json` - NSwag configuration
- Assessment components using the API

## 📝 Next Steps

1. **Coordinate with Backend Team**: Fix the Assessment controller route
2. **Regenerate API Client**: Run `nswag run` after backend fix
3. **Test All Assessment Features**: Verify list, details, create, and edit functionality
4. **Update Documentation**: Ensure all API documentation reflects correct endpoints
